<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\SowTemplateManager;
use models\composite\oDrawManagement\SowTemplate;
use PHPUnit\Framework\TestCase;

/**
 * Tests the SowTemplateManager class.
 *
 * @covers \models\composite\oDrawManagement\SowTemplateManager::__construct
 * @covers \models\composite\oDrawManagement\SowTemplateManager::forProcessingCompany
 * @covers \models\composite\oDrawManagement\SowTemplateManager::getOrCreateTemplateId
 * @covers \models\composite\oDrawManagement\SowTemplateManager::getTemplateId
 * @covers \models\composite\oDrawManagement\SowTemplateManager::getTemplate
 * @covers \models\composite\oDrawManagement\SowTemplateManager::getTemplateDataArray
 * @covers \models\composite\oDrawManagement\SowTemplateManager::saveSettings
 * @covers \models\composite\oDrawManagement\SowTemplateManager::saveCategories
 * @covers \models\composite\oDrawManagement\SowTemplateManager::saveLineItems
 */
class SowTemplateManagerTest extends TestCase
{
    /**
     * Tests the constructor with valid PC ID.
     */
    public function testConstructorWithValidPCId(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            $this->assertInstanceOf(SowTemplateManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the forProcessingCompany static method with null PC ID.
     */
    public function testForProcessingCompanyWithNullPCId(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("PCID must be provided or available in PageVariables.");
        
        SowTemplateManager::forProcessingCompany(null);
    }

    /**
     * Tests the forProcessingCompany static method with valid PC ID.
     */
    public function testForProcessingCompanyWithValidPCId(): void
    {
        try {
            $manager = SowTemplateManager::forProcessingCompany(123);
            $this->assertInstanceOf(SowTemplateManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the getOrCreateTemplateId method with null PC ID.
     */
    public function testGetOrCreateTemplateIdWithNullPCId(): void
    {
        try {
            $manager = new SowTemplateManager(null);
            $result = $manager->getOrCreateTemplateId();
            $this->assertNull($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the getTemplateId method.
     */
    public function testGetTemplateId(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            $result = $manager->getTemplateId();
            $this->assertTrue(is_int($result) || is_null($result));
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the getTemplate method.
     */
    public function testGetTemplate(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            $result = $manager->getTemplate();
            $this->assertInstanceOf(SowTemplate::class, $result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the getTemplateDataArray method.
     */
    public function testGetTemplateDataArray(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            $result = $manager->getTemplateDataArray();
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the saveSettings method.
     */
    public function testSaveSettings(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            $data = [
                'allowBorrowersAddEditCategories' => 1,
                'allowBorrowersDeleteCategories' => 0,
                'allowBorrowersAddEditLineItems' => 1,
                'allowBorrowersDeleteLineItems' => 0,
                'allowBorrowersSOWRevisions' => 1,
                'allowBorrowersExceedFinancedRehabCostOnRevision' => 0,
                'drawFee' => 50.00
            ];
            
            $manager->saveSettings($data);
            $this->addToAssertionCount(1); // Method executed without error
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the saveCategories method with too many categories.
     */
    public function testSaveCategoriesWithTooManyCategories(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            

            $categoriesData = [];
            for ($i = 0; $i < 21; $i++) {
                $categoriesData[] = [
                    'id' => null,
                    'categoryName' => "Category $i",
                    'description' => "Description $i",
                    'order' => $i
                ];
            }
            
            $result = $manager->saveCategories($categoriesData);
            $this->assertFalse($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the saveCategories method with valid data.
     */
    public function testSaveCategoriesWithValidData(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            
            $categoriesData = [
                [
                    'id' => null,
                    'categoryName' => 'Test Category',
                    'description' => 'Test Description',
                    'order' => 1
                ]
            ];
            
            $result = $manager->saveCategories($categoriesData);
            $this->assertIsBool($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the saveCategories method with empty array.
     */
    public function testSaveCategoriesWithEmptyArray(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            
            $result = $manager->saveCategories([]);
            $this->assertIsBool($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the saveLineItems method with too many categories.
     */
    public function testSaveLineItemsWithTooManyCategories(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            

            $lineItemsData = [];
            for ($i = 0; $i < 21; $i++) {
                $lineItemsData[$i] = [
                    [
                        'id' => null,
                        'categoryId' => $i,
                        'name' => "Line Item $i",
                        'description' => "Description $i",
                        'order' => 1
                    ]
                ];
            }
            
            $result = $manager->saveLineItems($lineItemsData);
            $this->assertFalse($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the saveLineItems method with valid data.
     */
    public function testSaveLineItemsWithValidData(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            
            $lineItemsData = [
                1 => [
                    [
                        'id' => null,
                        'categoryId' => 1,
                        'name' => 'Test Line Item',
                        'description' => 'Test Description',
                        'order' => 1
                    ]
                ]
            ];
            
            $result = $manager->saveLineItems($lineItemsData);
            $this->assertIsBool($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the saveLineItems method with empty array.
     */
    public function testSaveLineItemsWithEmptyArray(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            
            $result = $manager->saveLineItems([]);
            $this->assertIsBool($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the MAX_CATEGORIES constant.
     */
    public function testMaxCategoriesConstant(): void
    {
        $this->assertEquals(20, SowTemplateManager::MAX_CATEGORIES);
        $this->assertIsInt(SowTemplateManager::MAX_CATEGORIES);
    }

    /**
     * Tests that MAX_CATEGORIES constant is reasonable.
     */
    public function testMaxCategoriesIsReasonable(): void
    {
        $this->assertGreaterThan(0, SowTemplateManager::MAX_CATEGORIES);
        $this->assertLessThanOrEqual(100, SowTemplateManager::MAX_CATEGORIES);
    }

    /**
     * Tests saveCategories with categories containing special characters.
     */
    public function testSaveCategoriesWithSpecialCharacters(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            
            $categoriesData = [
                [
                    'id' => null,
                    'categoryName' => 'Category with "quotes" & symbols',
                    'description' => 'Description with <html> tags',
                    'order' => 1
                ]
            ];
            
            $result = $manager->saveCategories($categoriesData);
            $this->assertIsBool($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests saveLineItems with line items containing special characters.
     */
    public function testSaveLineItemsWithSpecialCharacters(): void
    {
        try {
            $manager = new SowTemplateManager(123);
            
            $lineItemsData = [
                1 => [
                    [
                        'id' => null,
                        'categoryId' => 1,
                        'name' => 'Line Item with "quotes" & symbols',
                        'description' => 'Description with <html> tags',
                        'order' => 1
                    ]
                ]
            ];
            
            $result = $manager->saveLineItems($lineItemsData);
            $this->assertIsBool($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests constructor with very large PC ID.
     */
    public function testConstructorWithLargePCId(): void
    {
        try {
            $largePCId = PHP_INT_MAX;
            $manager = new SowTemplateManager($largePCId);
            $this->assertInstanceOf(SowTemplateManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests forProcessingCompany with very large PC ID.
     */
    public function testForProcessingCompanyWithLargePCId(): void
    {
        try {
            $largePCId = PHP_INT_MAX;
            $manager = SowTemplateManager::forProcessingCompany($largePCId);
            $this->assertInstanceOf(SowTemplateManager::class, $manager);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }
}
