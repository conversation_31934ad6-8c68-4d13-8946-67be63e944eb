<?php

namespace tests\phpunit\models\composite\oDrawManagement\validation;

use PHPUnit\Framework\TestCase;
use models\composite\oDrawManagement\validation\PropertyValidator;
use models\composite\oDrawManagement\dto\shared\CategoryData;
use models\composite\oDrawManagement\dto\shared\LineItemData;
use models\composite\oDrawManagement\dto\shared\DrawRequestData;

/**
 * Test cases for PropertyValidator
 */
class PropertyValidatorTest extends TestCase
{
    private PropertyValidator $validator;

    protected function setUp(): void
    {
        $this->validator = new PropertyValidator();
    }

    /**
     * Test valid line item validation
     */
    public function testValidLineItemValidation(): void
    {
        $lineItem = new LineItemData();
        $lineItem->name = 'Test Line Item';
        $lineItem->cost = 100.00;
        $lineItem->completedAmount = 50.00;
        $lineItem->completedPercent = 50.0;
        $lineItem->requestedAmount = 25.00;
        $lineItem->requestedPercent = 25.0;
        $lineItem->order = 1;

        $result = $this->validator->validate($lineItem);

        $this->assertTrue($result);
        $this->assertEmpty($this->validator->getErrors());
    }

    /**
     * Test invalid line item validation
     */
    public function testInvalidLineItemValidation(): void
    {
        $lineItem = new LineItemData();
        $lineItem->name = ''; // Invalid: empty name
        $lineItem->cost = -10.00; // Invalid: negative cost
        $lineItem->completedAmount = 150.00; // Invalid: exceeds cost
        $lineItem->requestedAmount = 200.00; // Invalid: exceeds remaining cost
        $lineItem->order = 0; // Invalid: zero order

        $result = $this->validator->validate($lineItem);

        $this->assertFalse($result);
        $this->assertNotEmpty($this->validator->getErrors());

        $errors = $this->validator->getErrors();
        $this->assertContains('Line item \'\' must have a positive cost', $errors);
    }

    /**
     * Test line item amount consistency validation
     */
    public function testLineItemAmountConsistency(): void
    {
        $lineItem = new LineItemData();
        $lineItem->name = 'Test Item';
        $lineItem->cost = 100.00;
        $lineItem->completedAmount = 30.00;
        $lineItem->completedPercent = 50.0; // Inconsistent with amount (should be 30%)
        $lineItem->order = 1;

        $result = $this->validator->validate($lineItem);

        $this->assertFalse($result);
        $errors = $this->validator->getErrors();
        $this->assertContains('Line item \'Test Item\' completed amount and percent are inconsistent', $errors);
    }

    /**
     * Test requested amount validation
     */
    public function testRequestedAmountValidation(): void
    {
        $lineItem = new LineItemData();
        $lineItem->name = 'Test Item';
        $lineItem->cost = 100.00;
        $lineItem->completedAmount = 60.00;
        $lineItem->requestedAmount = 50.00; // Invalid: exceeds remaining (40.00)
        $lineItem->order = 1;

        $result = $this->validator->validate($lineItem);

        $this->assertFalse($result);
        $errors = $this->validator->getErrors();
        $this->assertContains('Line item \'Test Item\' requested amount cannot exceed remaining cost', $errors);
    }

    /**
     * Test valid category validation
     */
    public function testValidCategoryValidation(): void
    {
        $category = new CategoryData();
        $category->categoryName = 'Test Category';
        $category->description = 'Test Description';
        $category->order = 1;


        $lineItem = new LineItemData();
        $lineItem->name = 'Test Line Item';
        $lineItem->cost = 100.00;
        $lineItem->order = 1;
        $category->lineItems = [$lineItem];

        $result = $this->validator->validate($category);

        $this->assertTrue($result);
        $this->assertEmpty($this->validator->getErrors());
    }

    /**
     * Test category without line items
     */
    public function testCategoryWithoutLineItems(): void
    {
        $category = new CategoryData();
        $category->categoryName = 'Test Category';
        $category->order = 1;
        $category->lineItems = []; // Empty line items

        $result = $this->validator->validate($category);

        $this->assertFalse($result);
        $errors = $this->validator->getErrors();
        $this->assertContains('Category \'Test Category\' must have at least one line item', $errors);
    }

    /**
     * Test category with duplicate line item orders
     */
    public function testCategoryWithDuplicateLineItemOrders(): void
    {
        $category = new CategoryData();
        $category->categoryName = 'Test Category';
        $category->order = 1;

        $lineItem1 = new LineItemData();
        $lineItem1->name = 'Item 1';
        $lineItem1->cost = 100.00;
        $lineItem1->order = 1;

        $lineItem2 = new LineItemData();
        $lineItem2->name = 'Item 2';
        $lineItem2->cost = 200.00;
        $lineItem2->order = 1; // Duplicate order

        $category->lineItems = [$lineItem1, $lineItem2];

        $result = $this->validator->validate($category);

        $this->assertFalse($result);
        $errors = $this->validator->getErrors();
        $this->assertContains('Category \'Test Category\' has duplicate line item orders', $errors);
    }

    /**
     * Test valid draw request validation
     */
    public function testValidDrawRequestValidation(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->LMRId = 123;
        $drawRequest->status = 'pending';

        $category = new CategoryData();
        $category->categoryName = 'Test Category';
        $category->order = 1;

        $lineItem = new LineItemData();
        $lineItem->name = 'Test Line Item';
        $lineItem->cost = 100.00;
        $lineItem->order = 1;

        $category->lineItems = [$lineItem];
        $drawRequest->categories = [$category];

        $result = $this->validator->validate($drawRequest);

        $this->assertTrue($result);
        $this->assertEmpty($this->validator->getErrors());
    }

    /**
     * Test draw request without categories
     */
    public function testDrawRequestWithoutCategories(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->LMRId = 123;
        $drawRequest->status = 'pending';
        $drawRequest->categories = []; // Empty categories

        $result = $this->validator->validate($drawRequest);

        $this->assertFalse($result);
        $errors = $this->validator->getErrors();
        $this->assertContains('Draw request must have at least one category', $errors);
    }

    /**
     * Test draw request with duplicate category orders
     */
    public function testDrawRequestWithDuplicateCategoryOrders(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->LMRId = 123;
        $drawRequest->status = 'pending';

        $category1 = new CategoryData();
        $category1->categoryName = 'Category 1';
        $category1->order = 1;

        $category2 = new CategoryData();
        $category2->categoryName = 'Category 2';
        $category2->order = 1; // Duplicate order


        $lineItem1 = new LineItemData();
        $lineItem1->name = 'Item 1';
        $lineItem1->cost = 100.00;
        $lineItem1->order = 1;

        $lineItem2 = new LineItemData();
        $lineItem2->name = 'Item 2';
        $lineItem2->cost = 200.00;
        $lineItem2->order = 1;

        $category1->lineItems = [$lineItem1];
        $category2->lineItems = [$lineItem2];
        $drawRequest->categories = [$category1, $category2];

        $result = $this->validator->validate($drawRequest);

        $this->assertFalse($result);
        $errors = $this->validator->getErrors();
        $this->assertContains('Draw request has duplicate category orders', $errors);
    }

    /**
     * Test amount validation in draw request
     */
    public function testDrawRequestAmountValidation(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->LMRId = 123;
        $drawRequest->status = 'approved';
        $drawRequest->amountRequested = 100.00;
        $drawRequest->amountApproved = 150.00; // Invalid: exceeds requested

        $category = new CategoryData();
        $category->categoryName = 'Test Category';
        $category->order = 1;

        $lineItem = new LineItemData();
        $lineItem->name = 'Test Line Item';
        $lineItem->cost = 100.00;
        $lineItem->order = 1;

        $category->lineItems = [$lineItem];
        $drawRequest->categories = [$category];

        $result = $this->validator->validate($drawRequest);

        $this->assertFalse($result);
        $errors = $this->validator->getErrors();
        $this->assertContains('Approved amount cannot exceed requested amount', $errors);
    }

    /**
     * Test context-based validation
     */
    public function testContextBasedValidation(): void
    {
        $lineItem = new LineItemData();
        $lineItem->name = 'Test Item';
        $lineItem->cost = 100.00;
        $lineItem->requestedAmount = 50.00;
        $lineItem->order = 1;


        $context = ['maxRequestableAmount' => 30.00];
        $result = $this->validator->validate($lineItem, $context);

        $this->assertFalse($result);
        $errors = $this->validator->getErrors();
        $this->assertContains('Line item \'Test Item\' requested amount exceeds maximum allowed', $errors);
    }

    /**
     * Test batch validation
     */
    public function testBatchValidation(): void
    {
        $lineItem1 = new LineItemData();
        $lineItem1->name = 'Valid Item';
        $lineItem1->cost = 100.00;
        $lineItem1->order = 1;

        $lineItem2 = new LineItemData();
        $lineItem2->name = ''; // Invalid
        $lineItem2->cost = -50.00; // Invalid
        $lineItem2->order = 0; // Invalid

        $dtos = [$lineItem1, $lineItem2];
        $result = $this->validator->validateBatch($dtos);

        $this->assertFalse($result);
        $this->assertNotEmpty($this->validator->getErrors());
    }
}
