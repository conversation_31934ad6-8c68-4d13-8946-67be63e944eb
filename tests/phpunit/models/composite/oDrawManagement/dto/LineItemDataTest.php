<?php

namespace tests\models\composite\oDrawManagement\dto;

use models\composite\oDrawManagement\dto\shared\LineItemData;
use PHPUnit\Framework\TestCase;

/**
 * Tests the LineItemData DTO class.
 *
 * @covers \models\composite\oDrawManagement\dto\shared\LineItemData::getValidationRules
 * @covers \models\composite\oDrawManagement\dto\shared\LineItemData::calculateRequestedAmountFromPercent
 * @covers \models\composite\oDrawManagement\dto\shared\LineItemData::calculateRequestedPercentFromAmount
 * @covers \models\composite\oDrawManagement\dto\shared\LineItemData::calculateCompletedPercentFromAmount
 * @covers \models\composite\oDrawManagement\dto\shared\LineItemData::getMaxRequestableAmount
 * @covers \models\composite\oDrawManagement\dto\shared\LineItemData::getMaxRequestablePercent
 * @covers \models\composite\oDrawManagement\dto\shared\LineItemData::getRemainingCost
 * @covers \models\composite\oDrawManagement\dto\shared\LineItemData::getRemainingPercent
 * @covers \models\composite\oDrawManagement\dto\shared\LineItemData::validateRequestedAmounts
 * @covers \models\composite\oDrawManagement\dto\shared\LineItemData::syncRequestedValues
 */
class LineItemDataTest extends TestCase
{
    /**
     * Tests creating LineItemData from array.
     */
    public function testFromArray(): void
    {
        $data = [
            'id' => 123,
            'drawId' => 456,
            'categoryId' => 789,
            'templateCategoryId' => 101,
            'templateLineItemId' => 102,
            'name' => 'Test Line Item',
            'description' => 'Test Description',
            'order' => 2,
            'cost' => 1000.0,
            'completedAmount' => 500.0,
            'completedPercent' => 50.0,
            'requestedAmount' => 300.0,
            'requestedPercent' => 30.0,
            'disbursedAmount' => 200.0,
            'notes' => 'Test notes',
            'lenderNotes' => 'Test lender notes',
            'createdAt' => '2023-01-01 12:00:00',
            'updatedAt' => '2023-01-02 12:00:00'
        ];

        $lineItem = LineItemData::fromArray($data);

        $this->assertEquals(123, $lineItem->id);
        $this->assertEquals(456, $lineItem->drawId);
        $this->assertEquals(789, $lineItem->categoryId);
        $this->assertEquals(101, $lineItem->templateCategoryId);
        $this->assertEquals(102, $lineItem->templateLineItemId);
        $this->assertEquals('Test Line Item', $lineItem->name);
        $this->assertEquals('Test Description', $lineItem->description);
        $this->assertEquals(2, $lineItem->order);
        $this->assertEquals(1000.0, $lineItem->cost);
        $this->assertEquals(500.0, $lineItem->completedAmount);
        $this->assertEquals(50.0, $lineItem->completedPercent);
        $this->assertEquals(300.0, $lineItem->requestedAmount);
        $this->assertEquals(30.0, $lineItem->requestedPercent);
        $this->assertEquals(200.0, $lineItem->disbursedAmount);
        $this->assertEquals('Test notes', $lineItem->notes);
        $this->assertEquals('Test lender notes', $lineItem->lenderNotes);
        $this->assertEquals('2023-01-01 12:00:00', $lineItem->createdAt);
        $this->assertEquals('2023-01-02 12:00:00', $lineItem->updatedAt);
    }

    /**
     * Tests validation with valid data.
     */
    public function testValidationWithValidData(): void
    {
        $lineItem = new LineItemData();
        $lineItem->name = 'Valid Line Item';
        $lineItem->description = 'Valid description';
        $lineItem->order = 1;
        $lineItem->cost = 1000.0;
        $lineItem->completedAmount = 500.0;
        $lineItem->completedPercent = 50.0;
        $lineItem->requestedAmount = 300.0;
        $lineItem->requestedPercent = 30.0;

        $this->assertTrue($lineItem->validate());
        $this->assertEmpty($lineItem->getValidationErrors());
    }

    /**
     * Tests validation with missing required fields.
     */
    public function testValidationWithMissingRequiredFields(): void
    {
        $lineItem = new LineItemData();
        $lineItem->order = 0;

        $this->assertFalse($lineItem->validate());
        $errors = $lineItem->getValidationErrors();
        $this->assertArrayHasKey('name', $errors);
        $this->assertArrayHasKey('order', $errors);
    }

    /**
     * Tests validation with completed amount exceeding cost.
     */
    public function testValidationWithCompletedAmountExceedingCost(): void
    {
        $lineItem = new LineItemData();
        $lineItem->name = 'Test Line Item';
        $lineItem->order = 1;
        $lineItem->cost = 1000.0;
        $lineItem->completedAmount = 1500.0;

        $this->assertFalse($lineItem->validate());
        $errors = $lineItem->getValidationErrors();
        $this->assertArrayHasKey('completedAmount', $errors);
        $this->assertStringContainsString('cannot exceed total cost', $lineItem->getFirstError());
    }

    /**
     * Tests validation with requested amount exceeding remaining cost.
     */
    public function testValidationWithRequestedAmountExceedingRemainingCost(): void
    {
        $lineItem = new LineItemData();
        $lineItem->name = 'Test Line Item';
        $lineItem->order = 1;
        $lineItem->cost = 1000.0;
        $lineItem->completedAmount = 600.0;
        $lineItem->requestedAmount = 500.0;

        $this->assertFalse($lineItem->validate());
        $errors = $lineItem->getValidationErrors();
        $this->assertArrayHasKey('requestedAmount', $errors);
        $this->assertStringContainsString('cannot exceed remaining cost', $lineItem->getFirstError());
    }

    /**
     * Tests validation with requested percent exceeding remaining percent.
     */
    public function testValidationWithRequestedPercentExceedingRemainingPercent(): void
    {
        $lineItem = new LineItemData();
        $lineItem->name = 'Test Line Item';
        $lineItem->order = 1;
        $lineItem->cost = 1000.0;
        $lineItem->completedPercent = 60.0;
        $lineItem->requestedPercent = 50.0; // Exceeds remaining percent (40.0)

        $this->assertFalse($lineItem->validate());
        $errors = $lineItem->getValidationErrors();
        $this->assertArrayHasKey('requestedPercent', $errors);
        $this->assertStringContainsString('cannot exceed remaining percent', $lineItem->getFirstError());
    }

    /**
     * Tests calculateRequestedAmountFromPercent method.
     */
    public function testCalculateRequestedAmountFromPercent(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $lineItem->requestedPercent = 30.0;

        $lineItem->calculateRequestedAmountFromPercent();

        $this->assertEquals(300.0, $lineItem->requestedAmount);
    }

    /**
     * Tests calculateRequestedAmountFromPercent with zero cost.
     */
    public function testCalculateRequestedAmountFromPercentWithZeroCost(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 0.0;
        $lineItem->requestedPercent = 30.0;
        $lineItem->requestedAmount = 100.0; // Set initial value

        $lineItem->calculateRequestedAmountFromPercent();

        $this->assertEquals(100.0, $lineItem->requestedAmount); // Unchanged when cost is 0
    }

    /**
     * Tests calculateRequestedPercentFromAmount method.
     */
    public function testCalculateRequestedPercentFromAmount(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $lineItem->requestedAmount = 300.0;

        $lineItem->calculateRequestedPercentFromAmount();

        $this->assertEquals(30.0, $lineItem->requestedPercent);
    }

    /**
     * Tests calculateRequestedPercentFromAmount with zero cost.
     */
    public function testCalculateRequestedPercentFromAmountWithZeroCost(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 0.0;
        $lineItem->requestedAmount = 300.0;
        $lineItem->requestedPercent = 50.0; // Set initial value

        $lineItem->calculateRequestedPercentFromAmount();

        $this->assertEquals(50.0, $lineItem->requestedPercent); // Unchanged when cost is 0
    }

    /**
     * Tests calculateCompletedPercentFromAmount method.
     */
    public function testCalculateCompletedPercentFromAmount(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $lineItem->completedAmount = 250.0;

        $lineItem->calculateCompletedPercentFromAmount();

        $this->assertEquals(25.0, $lineItem->completedPercent);
    }

    /**
     * Tests getMaxRequestableAmount method.
     */
    public function testGetMaxRequestableAmount(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $lineItem->completedAmount = 300.0;

        $maxRequestable = $lineItem->getMaxRequestableAmount();

        $this->assertEquals(700.0, $maxRequestable);
    }

    /**
     * Tests getMaxRequestablePercent method.
     */
    public function testGetMaxRequestablePercent(): void
    {
        $lineItem = new LineItemData();
        $lineItem->completedPercent = 30.0;

        $maxRequestable = $lineItem->getMaxRequestablePercent();

        $this->assertEquals(70.0, $maxRequestable);
    }

    /**
     * Tests getRemainingCost method.
     */
    public function testGetRemainingCost(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $lineItem->completedAmount = 300.0;

        $remaining = $lineItem->getRemainingCost();

        $this->assertEquals(700.0, $remaining);
    }

    /**
     * Tests getRemainingPercent method.
     */
    public function testGetRemainingPercent(): void
    {
        $lineItem = new LineItemData();
        $lineItem->completedPercent = 30.0;

        $remaining = $lineItem->getRemainingPercent();

        $this->assertEquals(70.0, $remaining);
    }

    /**
     * Tests validateRequestedAmounts method.
     */
    public function testValidateRequestedAmounts(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $lineItem->completedAmount = 300.0;
        $lineItem->completedPercent = 30.0;
        $lineItem->requestedAmount = 400.0;
        $lineItem->requestedPercent = 40.0;

        $errors = $lineItem->validateRequestedAmounts();

        $this->assertEmpty($errors); // Should be valid - remaining cost is 700, requested is 400
    }

    /**
     * Tests validateRequestedAmounts with invalid amounts.
     */
    public function testValidateRequestedAmountsWithInvalidAmounts(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $lineItem->completedAmount = 300.0;
        $lineItem->completedPercent = 30.0;
        $lineItem->requestedAmount = 800.0;
        $lineItem->requestedPercent = 80.0;

        $errors = $lineItem->validateRequestedAmounts();

        $this->assertNotEmpty($errors);
        $this->assertContains('Requested amount exceeds remaining cost', $errors);
        $this->assertContains('Requested percent exceeds remaining percent', $errors);
    }

    /**
     * Tests validateRequestedAmounts with inconsistent values.
     */
    public function testValidateRequestedAmountsWithInconsistentValues(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $lineItem->requestedAmount = 300.0;
        $lineItem->requestedPercent = 40.0;

        $errors = $lineItem->validateRequestedAmounts();

        $this->assertNotEmpty($errors);
        $this->assertContains('Requested amount and percent are inconsistent', $errors);
    }

    /**
     * Tests syncRequestedValues method based on amount.
     */
    public function testSyncRequestedValuesBasedOnAmount(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $lineItem->requestedAmount = 300.0;
        $lineItem->requestedPercent = 0.0;

        $lineItem->syncRequestedValues('amount');

        $this->assertEquals(30.0, $lineItem->requestedPercent);
    }

    /**
     * Tests syncRequestedValues method based on percent.
     */
    public function testSyncRequestedValuesBasedOnPercent(): void
    {
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $lineItem->requestedAmount = 0.0;
        $lineItem->requestedPercent = 30.0;

        $lineItem->syncRequestedValues('percent');

        $this->assertEquals(300.0, $lineItem->requestedAmount);
    }

    /**
     * Tests default values.
     */
    public function testDefaultValues(): void
    {
        $lineItem = new LineItemData();

        $this->assertNull($lineItem->id);
        $this->assertNull($lineItem->drawId);
        $this->assertNull($lineItem->categoryId);
        $this->assertNull($lineItem->templateCategoryId);
        $this->assertNull($lineItem->templateLineItemId);
        $this->assertEquals('', $lineItem->name);
        $this->assertEquals('', $lineItem->description);
        $this->assertEquals(1, $lineItem->order);
        $this->assertEquals(0.0, $lineItem->cost);
        $this->assertEquals(0.0, $lineItem->completedAmount);
        $this->assertEquals(0.0, $lineItem->completedPercent);
        $this->assertEquals(0.0, $lineItem->requestedAmount);
        $this->assertEquals(0.0, $lineItem->requestedPercent);
        $this->assertEquals(0.0, $lineItem->disbursedAmount);
        $this->assertEquals('', $lineItem->notes);
        $this->assertEquals('', $lineItem->lenderNotes);
        $this->assertNull($lineItem->createdAt);
        $this->assertNull($lineItem->updatedAt);
    }
}
