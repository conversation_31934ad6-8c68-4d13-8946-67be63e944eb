<?php

namespace tests\models\composite\oDrawManagement\dto;

use models\composite\oDrawManagement\dto\base\ValidatableDTO;
use PHPUnit\Framework\TestCase;

/**
 * Test DTO class for validation testing
 */
class TestValidatableDTO extends ValidatableDTO
{
    public $id = null;
    public $name = '';
    public $amount = 0.0;
    public $active = false;
    public $tags = [];

    protected function getValidationRules(): array
    {
        return [
            'id' => [
                'type' => 'int',
                'min' => 1
            ],
            'name' => [
                'required' => true,
                'type' => 'string',
                'minLength' => 2,
                'maxLength' => 50
            ],
            'amount' => [
                'type' => 'float',
                'min' => 0,
                'max' => 10000
            ],
            'active' => [
                'type' => 'bool'
            ],
            'tags' => [
                'type' => 'array'
            ]
        ];
    }
}

/**
 * Tests the ValidatableDTO class.
 *
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::getValidationRules
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::validate
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::validateProperty
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::validateType
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::addValidationError
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::getValidationErrors
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::getPropertyErrors
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::isValid
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::getFirstError
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::getAllErrorMessages
 * @covers \models\composite\oDrawManagement\dto\base\ValidatableDTO::fromArray
 */
class ValidatableDTOTest extends TestCase
{
    /**
     * Create a concrete implementation of ValidatableDTO for testing
     */
    private function createTestDTO(): TestValidatableDTO
    {
        return new TestValidatableDTO();
    }

    /**
     * Tests validation with valid data.
     */
    public function testValidateWithValidData(): void
    {
        $dto = $this->createTestDTO();
        $dto->id = 123;
        $dto->name = 'Test Name';
        $dto->amount = 99.99;
        $dto->active = true;
        $dto->tags = ['tag1', 'tag2'];

        $result = $dto->validate();

        $this->assertTrue($result);
        $this->assertTrue($dto->isValid());
        $this->assertEmpty($dto->getValidationErrors());
        $this->assertNull($dto->getFirstError());
        $this->assertEmpty($dto->getAllErrorMessages());
    }

    /**
     * Tests validation with required field missing.
     */
    public function testValidateWithRequiredFieldMissing(): void
    {
        $dto = $this->createTestDTO();
        $dto->id = 123;
        // name is required but not set (empty string)
        $dto->amount = 99.99;

        $result = $dto->validate();

        $this->assertFalse($result);
        $this->assertFalse($dto->isValid());
        $this->assertNotEmpty($dto->getValidationErrors());
        $this->assertArrayHasKey('name', $dto->getValidationErrors());
        $this->assertStringContainsString('required', $dto->getFirstError());
    }

    /**
     * Tests validation with type mismatch.
     */
    public function testValidateWithTypeMismatch(): void
    {
        $dto = $this->createTestDTO();
        $dto->id = 'not_an_integer'; // String instead of int - this violates type rule
        $dto->name = 'Test Name';

        $result = $dto->validate();

        $this->assertFalse($result);
        $this->assertArrayHasKey('id', $dto->getValidationErrors());
        $this->assertStringContainsString('type', $dto->getFirstError());
    }

    /**
     * Tests validation with minimum value violation.
     */
    public function testValidateWithMinValueViolation(): void
    {
        $dto = $this->createTestDTO();
        $dto->id = 0; // Violates min value of 1
        $dto->name = 'Test Name';
        $dto->amount = -5.0; // Violates min value of 0

        $result = $dto->validate();

        $this->assertFalse($result);
        $this->assertArrayHasKey('id', $dto->getValidationErrors());
        $this->assertArrayHasKey('amount', $dto->getValidationErrors());
    }

    /**
     * Tests validation with maximum value violation.
     */
    public function testValidateWithMaxValueViolation(): void
    {
        $dto = $this->createTestDTO();
        $dto->id = 123;
        $dto->name = 'Test Name';
        $dto->amount = 15000.0; // Violates max value of 10000

        $result = $dto->validate();

        $this->assertFalse($result);
        $this->assertArrayHasKey('amount', $dto->getValidationErrors());
        $this->assertStringContainsString('exceed', $dto->getFirstError());
    }

    /**
     * Tests validation with string length violations.
     */
    public function testValidateWithStringLengthViolations(): void
    {
        $dto = $this->createTestDTO();
        $dto->id = 123;
        $dto->name = 'A'; // Too short (minLength: 2)

        $result = $dto->validate();

        $this->assertFalse($result);
        $this->assertArrayHasKey('name', $dto->getValidationErrors());
        $this->assertStringContainsString('at least 2 characters', $dto->getFirstError());


        $dto->name = str_repeat('A', 51); // Too long (maxLength: 50)
        $result = $dto->validate();

        $this->assertFalse($result);
        $this->assertArrayHasKey('name', $dto->getValidationErrors());
    }

    /**
     * Tests validation with 'in' rule.
     */
    public function testValidateWithInRule(): void
    {
        $dto = new class extends ValidatableDTO {
            public string $status = '';

            protected function getValidationRules(): array
            {
                return [
                    'status' => [
                        'in' => ['active', 'inactive', 'pending']
                    ]
                ];
            }
        };

        $dto->status = 'invalid_status';
        $result = $dto->validate();

        $this->assertFalse($result);
        $this->assertArrayHasKey('status', $dto->getValidationErrors());
        $this->assertStringContainsString('must be one of', $dto->getFirstError());


        $dto->status = 'active';
        $result = $dto->validate();

        $this->assertTrue($result);
    }

    /**
     * Tests validation with regex rule.
     */
    public function testValidateWithRegexRule(): void
    {
        $dto = new class extends ValidatableDTO {
            public string $email = '';

            protected function getValidationRules(): array
            {
                return [
                    'email' => [
                        'regex' => '/^[^\s@]+@[^\s@]+\.[^\s@]+$/'
                    ]
                ];
            }
        };

        $dto->email = 'invalid_email';
        $result = $dto->validate();

        $this->assertFalse($result);
        $this->assertArrayHasKey('email', $dto->getValidationErrors());
        $this->assertStringContainsString('format is invalid', $dto->getFirstError());


        $dto->email = '<EMAIL>';
        $result = $dto->validate();

        $this->assertTrue($result);
    }

    /**
     * Tests validation with custom rule.
     */
    public function testValidateWithCustomRule(): void
    {
        $dto = new class extends ValidatableDTO {
            public int $value = 0;

            protected function getValidationRules(): array
            {
                return [
                    'value' => [
                        'custom' => function($value, $dto) {
                            if ($value % 2 !== 0) {
                                return 'Value must be even';
                            }
                            return true;
                        }
                    ]
                ];
            }
        };

        $dto->value = 3; // Odd number
        $result = $dto->validate();

        $this->assertFalse($result);
        $this->assertArrayHasKey('value', $dto->getValidationErrors());
        $this->assertStringContainsString('must be even', $dto->getFirstError());


        $dto->value = 4; // Even number
        $result = $dto->validate();

        $this->assertTrue($result);
    }

    /**
     * Tests getPropertyErrors method.
     */
    public function testGetPropertyErrors(): void
    {
        $dto = $this->createTestDTO();
        $dto->id = 0;
        $dto->name = '';

        $dto->validate();

        $idErrors = $dto->getPropertyErrors('id');
        $nameErrors = $dto->getPropertyErrors('name');
        $nonExistentErrors = $dto->getPropertyErrors('nonexistent');

        $this->assertNotEmpty($idErrors);
        $this->assertNotEmpty($nameErrors);
        $this->assertEmpty($nonExistentErrors);
    }

    /**
     * Tests getAllErrorMessages method.
     */
    public function testGetAllErrorMessages(): void
    {
        $dto = $this->createTestDTO();
        $dto->id = 0;
        $dto->name = '';
        $dto->amount = -10;

        $dto->validate();

        $allMessages = $dto->getAllErrorMessages();

        $this->assertIsArray($allMessages);
        $this->assertGreaterThanOrEqual(3, count($allMessages));
        $this->assertContains('Field id must be at least 1', $allMessages);
        $this->assertContains('Field name is required', $allMessages);
        $this->assertContains('Field amount must be at least 0', $allMessages);
    }

    /**
     * Tests fromArray with validation enabled.
     */
    public function testFromArrayWithValidation(): void
    {
        $data = [
            'id' => 123,
            'name' => 'Test Name',
            'amount' => 99.99,
            'active' => true,
            'tags' => ['tag1', 'tag2']
        ];

        $dto = TestValidatableDTO::fromArray($data);

        $this->assertInstanceOf(ValidatableDTO::class, $dto);
        $this->assertEquals(123, $dto->id);
        $this->assertEquals('Test Name', $dto->name);
    }

    /**
     * Tests fromArray with validation enabled and invalid data.
     */
    public function testFromArrayWithValidationAndInvalidData(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Validation failed');

        $data = [
            'id' => 0,
            'name' => '',
        ];

        TestValidatableDTO::fromArray($data, true);
    }

    /**
     * Tests type validation for different types.
     */
    public function testTypeValidation(): void
    {
        $dto = new class extends ValidatableDTO {
            public $intValue;
            public $floatValue;
            public $stringValue;
            public $boolValue;
            public $arrayValue;
            public $objectValue;
            public $numericValue;

            protected function getValidationRules(): array
            {
                return [
                    'intValue' => ['type' => 'int'],
                    'floatValue' => ['type' => 'float'],
                    'stringValue' => ['type' => 'string'],
                    'boolValue' => ['type' => 'bool'],
                    'arrayValue' => ['type' => 'array'],
                    'objectValue' => ['type' => 'object'],
                    'numericValue' => ['type' => 'numeric'],
                ];
            }
        };


        $dto->intValue = 123;
        $dto->floatValue = 123.45;
        $dto->stringValue = 'test';
        $dto->boolValue = true;
        $dto->arrayValue = [1, 2, 3];
        $dto->objectValue = new \stdClass();
        $dto->numericValue = '123.45';

        $this->assertTrue($dto->validate());


        $dto->intValue = 'not_int';
        $this->assertFalse($dto->validate());
    }

    /**
     * Tests validation with null values when not required.
     */
    public function testValidationWithNullValues(): void
    {
        $dto = new class extends ValidatableDTO {
            public ?int $optionalId = null;
            public string $requiredName = '';

            protected function getValidationRules(): array
            {
                return [
                    'optionalId' => ['type' => 'int'],
                    'requiredName' => ['required' => true, 'type' => 'string']
                ];
            }
        };


        $dto->requiredName = 'Test';

        $this->assertTrue($dto->validate());
    }
}
