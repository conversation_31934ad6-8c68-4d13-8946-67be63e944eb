<?php

namespace tests\models\composite\oDrawManagement\dto;

use models\composite\oDrawManagement\dto\response\DrawManagementResponse;
use models\composite\oDrawManagement\dto\shared\DrawRequestData;
use models\composite\oDrawManagement\dto\shared\CategoryData;
use models\composite\oDrawManagement\dto\shared\LineItemData;
use models\composite\oDrawManagement\DrawRequest;
use PHPUnit\Framework\TestCase;

/**
 * Tests the DrawManagementResponse DTO class.
 *
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::fromDrawRequest
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::fromCategories
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::calculateTotals
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::populateFromArray
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::toArray
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::addPermission
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::addPermissions
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::addSummary
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::addSummaryData
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::getCategory
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::hasPermission
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::getCompletionPercentage
 * @covers \models\composite\oDrawManagement\dto\response\DrawManagementResponse::getRequestPercentage
 */
class DrawManagementResponseTest extends TestCase
{
    /**
     * Create a mock DrawRequestData for testing
     */
    private function createMockDrawRequest(): DrawRequestData
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->id = 123;
        $drawRequest->LMRId = 456;
        $drawRequest->status = DrawRequest::STATUS_PENDING;


        $category1 = new CategoryData();
        $category1->id = 1;
        $category1->categoryName = 'Category 1';

        $lineItem1 = new LineItemData();
        $lineItem1->id = 1;
        $lineItem1->cost = 1000.0;
        $lineItem1->completedAmount = 500.0;
        $lineItem1->requestedAmount = 300.0;
        $category1->addLineItem($lineItem1);

        $category2 = new CategoryData();
        $category2->id = 2;
        $category2->categoryName = 'Category 2';

        $lineItem2 = new LineItemData();
        $lineItem2->id = 2;
        $lineItem2->cost = 2000.0;
        $lineItem2->completedAmount = 1000.0;
        $lineItem2->requestedAmount = 500.0;
        $category2->addLineItem($lineItem2);

        $drawRequest->addCategory($category1);
        $drawRequest->addCategory($category2);

        return $drawRequest;
    }

    /**
     * Tests fromDrawRequest static method.
     */
    public function testFromDrawRequest(): void
    {
        $drawRequest = $this->createMockDrawRequest();
        $permissions = ['edit' => true, 'Delete' => false];
        $summary = ['totalBudget' => 5000.0];

        $response = DrawManagementResponse::fromDrawRequest($drawRequest, $permissions, $summary);

        $this->assertInstanceOf(DrawManagementResponse::class, $response);
        $this->assertSame($drawRequest, $response->drawRequest);
        $this->assertEquals($drawRequest->categories, $response->categories);
        $this->assertEquals($permissions, $response->permissions);
        $this->assertEquals($summary, $response->summary);
        $this->assertEquals(DrawRequest::STATUS_PENDING, $response->status);


        $this->assertEquals(2, $response->totalCategories);
        $this->assertEquals(2, $response->totalLineItems);
        $this->assertEquals(3000.0, $response->totalCost);
        $this->assertEquals(1500.0, $response->totalCompletedAmount);
        $this->assertEquals(800.0, $response->totalRequestedAmount);
    }

    /**
     * Tests fromCategories static method.
     */
    public function testFromCategories(): void
    {
        $category1 = new CategoryData();
        $category1->id = 1;
        $lineItem1 = new LineItemData();
        $lineItem1->cost = 1000.0;
        $lineItem1->completedAmount = 500.0;
        $lineItem1->requestedAmount = 300.0;
        $category1->addLineItem($lineItem1);

        $category2 = new CategoryData();
        $category2->id = 2;
        $lineItem2 = new LineItemData();
        $lineItem2->cost = 2000.0;
        $lineItem2->completedAmount = 1000.0;
        $lineItem2->requestedAmount = 500.0;
        $category2->addLineItem($lineItem2);

        $categories = [$category1, $category2];
        $permissions = ['view' => true];
        $summary = ['notes' => 'Test summary'];
        $status = DrawRequest::STATUS_APPROVED;

        $response = DrawManagementResponse::fromCategories($categories, $permissions, $summary, $status);

        $this->assertInstanceOf(DrawManagementResponse::class, $response);
        $this->assertEquals($categories, $response->categories);
        $this->assertEquals($permissions, $response->permissions);
        $this->assertEquals($summary, $response->summary);
        $this->assertEquals($status, $response->status);
        $this->assertNull($response->drawRequest);


        $this->assertEquals(2, $response->totalCategories);
        $this->assertEquals(2, $response->totalLineItems);
        $this->assertEquals(3000.0, $response->totalCost);
        $this->assertEquals(1500.0, $response->totalCompletedAmount);
        $this->assertEquals(800.0, $response->totalRequestedAmount);
    }

    /**
     * Tests calculateTotals method with empty categories.
     */
    public function testCalculateTotalsWithEmptyCategories(): void
    {
        $response = new DrawManagementResponse();
        $response->categories = [];

        $response->calculateTotals();

        $this->assertEquals(0, $response->totalCategories);
        $this->assertEquals(0, $response->totalLineItems);
        $this->assertEquals(0.0, $response->totalCost);
        $this->assertEquals(0.0, $response->totalCompletedAmount);
        $this->assertEquals(0.0, $response->totalRequestedAmount);
    }

    /**
     * Tests addPermission method.
     */
    public function testAddPermission(): void
    {
        $response = new DrawManagementResponse();

        $response->addPermission('edit', true);
        $response->addPermission('Delete', false);

        $this->assertTrue($response->permissions['edit']);
        $this->assertFalse($response->permissions['Delete']);
        $this->assertTrue($response->hasPermission('edit'));
        $this->assertFalse($response->hasPermission('Delete'));
    }

    /**
     * Tests addPermissions method.
     */
    public function testAddPermissions(): void
    {
        $response = new DrawManagementResponse();
        $response->permissions = ['existing' => true];

        $newPermissions = ['edit' => true, 'Delete' => false];
        $response->addPermissions($newPermissions);

        $this->assertTrue($response->permissions['existing']);
        $this->assertTrue($response->permissions['edit']);
        $this->assertFalse($response->permissions['Delete']);
        $this->assertCount(3, $response->permissions);
    }

    /**
     * Tests addSummary method.
     */
    public function testAddSummary(): void
    {
        $response = new DrawManagementResponse();

        $response->addSummary('totalBudget', 5000.0);
        $response->addSummary('notes', 'Test notes');

        $this->assertEquals(5000.0, $response->summary['totalBudget']);
        $this->assertEquals('Test notes', $response->summary['notes']);
    }

    /**
     * Tests addSummaryData method.
     */
    public function testAddSummaryData(): void
    {
        $response = new DrawManagementResponse();
        $response->summary = ['existing' => 'value'];

        $newSummary = ['totalBudget' => 5000.0, 'notes' => 'Test notes'];
        $response->addSummaryData($newSummary);

        $this->assertEquals('value', $response->summary['existing']);
        $this->assertEquals(5000.0, $response->summary['totalBudget']);
        $this->assertEquals('Test notes', $response->summary['notes']);
        $this->assertCount(3, $response->summary);
    }

    /**
     * Tests getCategory method.
     */
    public function testGetCategory(): void
    {
        $response = new DrawManagementResponse();

        $category1 = new CategoryData();
        $category1->id = 1;
        $category1->categoryName = 'Category 1';

        $category2 = new CategoryData();
        $category2->id = 2;
        $category2->categoryName = 'Category 2';

        $response->categories = [$category1, $category2];

        $found = $response->getCategory(2);
        $this->assertInstanceOf(CategoryData::class, $found);
        $this->assertEquals('Category 2', $found->categoryName);

        $notFound = $response->getCategory(999);
        $this->assertNull($notFound);
    }

    /**
     * Tests hasPermission method.
     */
    public function testHasPermission(): void
    {
        $response = new DrawManagementResponse();
        $response->permissions = ['edit' => true, 'Delete' => false];

        $this->assertTrue($response->hasPermission('edit'));
        $this->assertFalse($response->hasPermission('Delete'));
        $this->assertFalse($response->hasPermission('nonexistent'));
    }

    /**
     * Tests getCompletionPercentage method.
     */
    public function testGetCompletionPercentage(): void
    {
        $response = new DrawManagementResponse();
        $response->totalCost = 1000.0;
        $response->totalCompletedAmount = 250.0;

        $percentage = $response->getCompletionPercentage();

        $this->assertEquals(25.0, $percentage);
    }

    /**
     * Tests getCompletionPercentage method with zero cost.
     */
    public function testGetCompletionPercentageWithZeroCost(): void
    {
        $response = new DrawManagementResponse();
        $response->totalCost = 0.0;
        $response->totalCompletedAmount = 250.0;

        $percentage = $response->getCompletionPercentage();

        $this->assertEquals(0.0, $percentage);
    }

    /**
     * Tests getRequestPercentage method.
     */
    public function testGetRequestPercentage(): void
    {
        $response = new DrawManagementResponse();
        $response->totalCost = 1000.0;
        $response->totalRequestedAmount = 300.0;

        $percentage = $response->getRequestPercentage();

        $this->assertEquals(30.0, $percentage);
    }

    /**
     * Tests getRequestPercentage method with zero cost.
     */
    public function testGetRequestPercentageWithZeroCost(): void
    {
        $response = new DrawManagementResponse();
        $response->totalCost = 0.0;
        $response->totalRequestedAmount = 300.0;

        $percentage = $response->getRequestPercentage();

        $this->assertEquals(0.0, $percentage);
    }

    /**
     * Tests populateFromArray method.
     */
    public function testPopulateFromArray(): void
    {
        $data = [
            'status' => DrawRequest::STATUS_APPROVED,
            'totalCategories' => 5,
            'drawRequest' => [
                'id' => 123,
                'LMRId' => 456,
                'status' => DrawRequest::STATUS_APPROVED
            ],
            'categories' => [
                [
                    'id' => 1,
                    'categoryName' => 'Category 1',
                    'lineItems' => []
                ],
                [
                    'id' => 2,
                    'categoryName' => 'Category 2',
                    'lineItems' => []
                ]
            ]
        ];

        $response = DrawManagementResponse::fromArray($data);

        $this->assertEquals(DrawRequest::STATUS_APPROVED, $response->status);
        $this->assertEquals(5, $response->totalCategories);
        $this->assertInstanceOf(DrawRequestData::class, $response->drawRequest);
        $this->assertEquals(123, $response->drawRequest->id);
        $this->assertCount(2, $response->categories);
        $this->assertInstanceOf(CategoryData::class, $response->categories[0]);
        $this->assertEquals('Category 1', $response->categories[0]->categoryName);
    }

    /**
     * Tests toArray method.
     */
    public function testToArray(): void
    {
        $drawRequest = $this->createMockDrawRequest();
        $response = DrawManagementResponse::fromDrawRequest($drawRequest);
        $response->addPermission('edit', true);
        $response->addSummary('notes', 'Test notes');

        $result = $response->toArray();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('drawRequest', $result);
        $this->assertArrayHasKey('categories', $result);
        $this->assertArrayHasKey('permissions', $result);
        $this->assertArrayHasKey('summary', $result);
        $this->assertArrayHasKey('status', $result);
        $this->assertArrayHasKey('totalCategories', $result);
        $this->assertArrayHasKey('totalLineItems', $result);
        $this->assertArrayHasKey('totalCost', $result);
        $this->assertArrayHasKey('totalCompletedAmount', $result);
        $this->assertArrayHasKey('totalRequestedAmount', $result);

        $this->assertIsArray($result['drawRequest']);
        $this->assertIsArray($result['categories']);
        $this->assertCount(2, $result['categories']);
        $this->assertTrue($result['permissions']['edit']);
        $this->assertEquals('Test notes', $result['summary']['notes']);
    }

    /**
     * Tests default property values.
     */
    public function testDefaultValues(): void
    {
        $response = new DrawManagementResponse();

        $this->assertNull($response->drawRequest);
        $this->assertIsArray($response->categories);
        $this->assertEmpty($response->categories);
        $this->assertIsArray($response->permissions);
        $this->assertEmpty($response->permissions);
        $this->assertIsArray($response->summary);
        $this->assertEmpty($response->summary);
        $this->assertNull($response->status);
        $this->assertNull($response->totalCategories);
        $this->assertNull($response->totalLineItems);
        $this->assertNull($response->totalCost);
        $this->assertNull($response->totalCompletedAmount);
        $this->assertNull($response->totalRequestedAmount);
    }

    /**
     * Tests calculateTotals with mixed category types.
     */
    public function testCalculateTotalsWithMixedCategoryTypes(): void
    {
        $response = new DrawManagementResponse();

        $validCategory = new CategoryData();
        $lineItem = new LineItemData();
        $lineItem->cost = 1000.0;
        $validCategory->addLineItem($lineItem);

        $invalidCategory = ['not' => 'a CategoryData object'];

        $response->categories = [$validCategory, $invalidCategory];
        $response->calculateTotals();

        $this->assertEquals(2, $response->totalCategories);
        $this->assertEquals(1, $response->totalLineItems);
        $this->assertEquals(1000.0, $response->totalCost);
    }
}
