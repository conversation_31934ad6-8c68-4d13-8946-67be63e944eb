<?php

namespace tests\models\composite\oDrawManagement\dto;

use models\composite\oDrawManagement\dto\shared\DrawRequestData;
use models\composite\oDrawManagement\dto\shared\CategoryData;
use models\composite\oDrawManagement\dto\shared\LineItemData;
use models\composite\oDrawManagement\DrawRequest;
use PHPUnit\Framework\TestCase;

/**
 * Tests the DrawRequestData DTO class.
 *
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::getValidationRules
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::populateFromArray
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::toArray
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::addCategory
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::getCategory
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::removeCategory
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::getTotalCost
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::getTotalCompletedAmount
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::getTotalRequestedAmount
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::sortCategories
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::canBeSubmitted
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::canBeApproved
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::canBeRejected
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::getAllLineItems
 * @covers \models\composite\oDrawManagement\dto\shared\DrawRequestData::getLineItem
 */
class DrawRequestDataTest extends TestCase
{
    /**
     * Tests creating DrawRequestData from array.
     */
    public function testFromArray(): void
    {
        $data = [
            'id' => 123,
            'LMRId' => 456,
            'status' => DrawRequest::STATUS_PENDING,
            'sowApproved' => 1,
            'isDrawRequest' => 1,
            'submittedAt' => '2023-01-01 12:00:00',
            'approvedAt' => '2023-01-02 12:00:00',
            'rejectedAt' => null,
            'rejectionReason' => '',
            'amountRequested' => 5000.0,
            'amountApproved' => 4500.0,
            'lenderNotes' => 'Test lender notes',
            'createdAt' => '2023-01-01 10:00:00',
            'updatedAt' => '2023-01-02 14:00:00',
            'categories' => [
                [
                    'id' => 1,
                    'categoryName' => 'Category 1',
                    'order' => 1,
                    'lineItems' => []
                ],
                [
                    'id' => 2,
                    'categoryName' => 'Category 2',
                    'order' => 2,
                    'lineItems' => []
                ]
            ]
        ];

        $drawRequest = DrawRequestData::fromArray($data);

        $this->assertEquals(123, $drawRequest->id);
        $this->assertEquals(456, $drawRequest->LMRId);
        $this->assertEquals(DrawRequest::STATUS_PENDING, $drawRequest->status);
        $this->assertEquals(1, $drawRequest->sowApproved);
        $this->assertEquals(1, $drawRequest->isDrawRequest);
        $this->assertEquals('2023-01-01 12:00:00', $drawRequest->submittedAt);
        $this->assertEquals('2023-01-02 12:00:00', $drawRequest->approvedAt);
        $this->assertNull($drawRequest->rejectedAt);
        $this->assertEquals('', $drawRequest->rejectionReason);
        $this->assertEquals(5000.0, $drawRequest->amountRequested);
        $this->assertEquals(4500.0, $drawRequest->amountApproved);
        $this->assertEquals('Test lender notes', $drawRequest->lenderNotes);
        $this->assertEquals('2023-01-01 10:00:00', $drawRequest->createdAt);
        $this->assertEquals('2023-01-02 14:00:00', $drawRequest->updatedAt);
        $this->assertCount(2, $drawRequest->categories);
        $this->assertInstanceOf(CategoryData::class, $drawRequest->categories[0]);
        $this->assertEquals('Category 1', $drawRequest->categories[0]->categoryName);
    }

    /**
     * Tests validation with valid data.
     */
    public function testValidationWithValidData(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->LMRId = 456;
        $drawRequest->status = DrawRequest::STATUS_NEW;
        $drawRequest->amountRequested = 5000.0;
        $drawRequest->amountApproved = 4500.0;
        $drawRequest->lenderNotes = 'Valid notes';

        $this->assertTrue($drawRequest->validate());
        $this->assertEmpty($drawRequest->getValidationErrors());
    }

    /**
     * Tests validation with missing required fields.
     */
    public function testValidationWithMissingRequiredFields(): void
    {
        $drawRequest = new DrawRequestData();
        // LMRId is required but not set
        // status is required but defaults to STATUS_NEW

        $this->assertFalse($drawRequest->validate());
        $errors = $drawRequest->getValidationErrors();
        $this->assertArrayHasKey('LMRId', $errors);
    }

    /**
     * Tests validation with invalid status.
     */
    public function testValidationWithInvalidStatus(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->LMRId = 456;
        $drawRequest->status = 'invalid_status';

        $this->assertFalse($drawRequest->validate());
        $errors = $drawRequest->getValidationErrors();
        $this->assertArrayHasKey('status', $errors);
        $this->assertStringContainsString('must be one of', $drawRequest->getFirstError());
    }

    /**
     * Tests validation with negative amounts.
     */
    public function testValidationWithNegativeAmounts(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->LMRId = 456;
        $drawRequest->status = DrawRequest::STATUS_NEW;
        $drawRequest->amountRequested = -100.0;
        $drawRequest->amountApproved = -50.0;

        $this->assertFalse($drawRequest->validate());
        $errors = $drawRequest->getValidationErrors();
        $this->assertArrayHasKey('amountRequested', $errors);
        $this->assertArrayHasKey('amountApproved', $errors);
    }

    /**
     * Tests validation with too long lender notes.
     */
    public function testValidationWithTooLongLenderNotes(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->LMRId = 456;
        $drawRequest->status = DrawRequest::STATUS_NEW;
        $drawRequest->lenderNotes = str_repeat('A', 2001); // Too long (max: 2000)

        $this->assertFalse($drawRequest->validate());
        $errors = $drawRequest->getValidationErrors();
        $this->assertArrayHasKey('lenderNotes', $errors);
    }

    /**
     * Tests toArray method.
     */
    public function testToArray(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->id = 123;
        $drawRequest->LMRId = 456;
        $drawRequest->status = DrawRequest::STATUS_PENDING;
        $drawRequest->amountRequested = 5000.0;


        $category1 = new CategoryData();
        $category1->id = 1;
        $category1->categoryName = 'Category 1';

        $category2 = new CategoryData();
        $category2->id = 2;
        $category2->categoryName = 'Category 2';

        $drawRequest->addCategory($category1);
        $drawRequest->addCategory($category2);

        $result = $drawRequest->toArray();

        $this->assertIsArray($result);
        $this->assertEquals(123, $result['id']);
        $this->assertEquals(456, $result['LMRId']);
        $this->assertEquals(DrawRequest::STATUS_PENDING, $result['status']);
        $this->assertEquals(5000.0, $result['amountRequested']);
        $this->assertArrayHasKey('categories', $result);
        $this->assertCount(2, $result['categories']);
        $this->assertEquals('Category 1', $result['categories'][0]['categoryName']);
        $this->assertEquals('Category 2', $result['categories'][1]['categoryName']);
    }

    /**
     * Tests addCategory method.
     */
    public function testAddCategory(): void
    {
        $drawRequest = new DrawRequestData();
        $category = new CategoryData();
        $category->id = 1;
        $category->categoryName = 'Test Category';

        $drawRequest->addCategory($category);

        $this->assertCount(1, $drawRequest->categories);
        $this->assertEquals('Test Category', $drawRequest->categories[0]->categoryName);
    }

    /**
     * Tests getCategory method.
     */
    public function testGetCategory(): void
    {
        $drawRequest = new DrawRequestData();
        
        $category1 = new CategoryData();
        $category1->id = 1;
        $category1->categoryName = 'Category 1';

        $category2 = new CategoryData();
        $category2->id = 2;
        $category2->categoryName = 'Category 2';

        $drawRequest->addCategory($category1);
        $drawRequest->addCategory($category2);

        $found = $drawRequest->getCategory(2);
        $this->assertInstanceOf(CategoryData::class, $found);
        $this->assertEquals('Category 2', $found->categoryName);

        $notFound = $drawRequest->getCategory(999);
        $this->assertNull($notFound);
    }

    /**
     * Tests removeCategory method.
     */
    public function testRemoveCategory(): void
    {
        $drawRequest = new DrawRequestData();
        
        $category1 = new CategoryData();
        $category1->id = 1;
        $category1->categoryName = 'Category 1';

        $category2 = new CategoryData();
        $category2->id = 2;
        $category2->categoryName = 'Category 2';

        $drawRequest->addCategory($category1);
        $drawRequest->addCategory($category2);

        $this->assertCount(2, $drawRequest->categories);

        $removed = $drawRequest->removeCategory(1);
        $this->assertTrue($removed);
        $this->assertCount(1, $drawRequest->categories);
        $this->assertEquals('Category 2', $drawRequest->categories[0]->categoryName);

        $notRemoved = $drawRequest->removeCategory(999);
        $this->assertFalse($notRemoved);
    }

    /**
     * Tests getTotalCost method.
     */
    public function testGetTotalCost(): void
    {
        $drawRequest = new DrawRequestData();
        
        $category1 = new CategoryData();
        $lineItem1 = new LineItemData();
        $lineItem1->cost = 1000.0;
        $category1->addLineItem($lineItem1);

        $category2 = new CategoryData();
        $lineItem2 = new LineItemData();
        $lineItem2->cost = 2000.0;
        $category2->addLineItem($lineItem2);

        $drawRequest->addCategory($category1);
        $drawRequest->addCategory($category2);

        $total = $drawRequest->getTotalCost();
        $this->assertEquals(3000.0, $total);
    }

    /**
     * Tests getTotalCompletedAmount method.
     */
    public function testGetTotalCompletedAmount(): void
    {
        $drawRequest = new DrawRequestData();
        
        $category1 = new CategoryData();
        $lineItem1 = new LineItemData();
        $lineItem1->completedAmount = 500.0;
        $category1->addLineItem($lineItem1);

        $category2 = new CategoryData();
        $lineItem2 = new LineItemData();
        $lineItem2->completedAmount = 750.0;
        $category2->addLineItem($lineItem2);

        $drawRequest->addCategory($category1);
        $drawRequest->addCategory($category2);

        $total = $drawRequest->getTotalCompletedAmount();
        $this->assertEquals(1250.0, $total);
    }

    /**
     * Tests getTotalRequestedAmount method.
     */
    public function testGetTotalRequestedAmount(): void
    {
        $drawRequest = new DrawRequestData();
        
        $category1 = new CategoryData();
        $lineItem1 = new LineItemData();
        $lineItem1->requestedAmount = 300.0;
        $category1->addLineItem($lineItem1);

        $category2 = new CategoryData();
        $lineItem2 = new LineItemData();
        $lineItem2->requestedAmount = 400.0;
        $category2->addLineItem($lineItem2);

        $drawRequest->addCategory($category1);
        $drawRequest->addCategory($category2);

        $total = $drawRequest->getTotalRequestedAmount();
        $this->assertEquals(700.0, $total);
    }

    /**
     * Tests canBeSubmitted method.
     */
    public function testCanBeSubmitted(): void
    {
        $drawRequest = new DrawRequestData();
        $drawRequest->status = DrawRequest::STATUS_NEW;
        
        // Without requested amount
        $this->assertFalse($drawRequest->canBeSubmitted());


        $category = new CategoryData();
        $lineItem = new LineItemData();
        $lineItem->requestedAmount = 500.0;
        $category->addLineItem($lineItem);
        $drawRequest->addCategory($category);

        $this->assertTrue($drawRequest->canBeSubmitted());

        // Change status
        $drawRequest->status = DrawRequest::STATUS_PENDING;
        $this->assertFalse($drawRequest->canBeSubmitted());
    }

    /**
     * Tests canBeApproved method.
     */
    public function testCanBeApproved(): void
    {
        $drawRequest = new DrawRequestData();
        
        $drawRequest->status = DrawRequest::STATUS_NEW;
        $this->assertFalse($drawRequest->canBeApproved());

        $drawRequest->status = DrawRequest::STATUS_PENDING;
        $this->assertTrue($drawRequest->canBeApproved());

        $drawRequest->status = DrawRequest::STATUS_APPROVED;
        $this->assertFalse($drawRequest->canBeApproved());
    }

    /**
     * Tests canBeRejected method.
     */
    public function testCanBeRejected(): void
    {
        $drawRequest = new DrawRequestData();
        
        $drawRequest->status = DrawRequest::STATUS_NEW;
        $this->assertFalse($drawRequest->canBeRejected());

        $drawRequest->status = DrawRequest::STATUS_PENDING;
        $this->assertTrue($drawRequest->canBeRejected());

        $drawRequest->status = DrawRequest::STATUS_REJECTED;
        $this->assertFalse($drawRequest->canBeRejected());
    }

    /**
     * Tests getAllLineItems method.
     */
    public function testGetAllLineItems(): void
    {
        $drawRequest = new DrawRequestData();
        
        $category1 = new CategoryData();
        $lineItem1 = new LineItemData();
        $lineItem1->id = 1;
        $lineItem1->name = 'Line Item 1';
        $category1->addLineItem($lineItem1);

        $category2 = new CategoryData();
        $lineItem2 = new LineItemData();
        $lineItem2->id = 2;
        $lineItem2->name = 'Line Item 2';
        $lineItem3 = new LineItemData();
        $lineItem3->id = 3;
        $lineItem3->name = 'Line Item 3';
        $category2->addLineItem($lineItem2);
        $category2->addLineItem($lineItem3);

        $drawRequest->addCategory($category1);
        $drawRequest->addCategory($category2);

        $allLineItems = $drawRequest->getAllLineItems();

        $this->assertCount(3, $allLineItems);
        $this->assertEquals('Line Item 1', $allLineItems[0]->name);
        $this->assertEquals('Line Item 2', $allLineItems[1]->name);
        $this->assertEquals('Line Item 3', $allLineItems[2]->name);
    }

    /**
     * Tests getLineItem method.
     */
    public function testGetLineItem(): void
    {
        $drawRequest = new DrawRequestData();
        
        $category = new CategoryData();
        $lineItem1 = new LineItemData();
        $lineItem1->id = 1;
        $lineItem1->name = 'Line Item 1';
        $lineItem2 = new LineItemData();
        $lineItem2->id = 2;
        $lineItem2->name = 'Line Item 2';
        $category->addLineItem($lineItem1);
        $category->addLineItem($lineItem2);

        $drawRequest->addCategory($category);

        $found = $drawRequest->getLineItem(2);
        $this->assertInstanceOf(LineItemData::class, $found);
        $this->assertEquals('Line Item 2', $found->name);

        $notFound = $drawRequest->getLineItem(999);
        $this->assertNull($notFound);
    }

    /**
     * Tests default values.
     */
    public function testDefaultValues(): void
    {
        $drawRequest = new DrawRequestData();

        $this->assertNull($drawRequest->id);
        $this->assertNull($drawRequest->LMRId);
        $this->assertEquals(DrawRequest::STATUS_NEW, $drawRequest->status);
        $this->assertNull($drawRequest->sowApproved);
        $this->assertNull($drawRequest->isDrawRequest);
        $this->assertNull($drawRequest->submittedAt);
        $this->assertNull($drawRequest->approvedAt);
        $this->assertNull($drawRequest->rejectedAt);
        $this->assertEquals('', $drawRequest->rejectionReason);
        $this->assertEquals(0.0, $drawRequest->amountRequested);
        $this->assertEquals(0.0, $drawRequest->amountApproved);
        $this->assertEquals('', $drawRequest->lenderNotes);
        $this->assertNull($drawRequest->createdAt);
        $this->assertNull($drawRequest->updatedAt);
        $this->assertIsArray($drawRequest->categories);
        $this->assertEmpty($drawRequest->categories);
    }
}
