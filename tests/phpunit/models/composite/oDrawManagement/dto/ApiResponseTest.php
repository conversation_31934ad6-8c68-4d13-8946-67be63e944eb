<?php

namespace tests\models\composite\oDrawManagement\dto;

use models\composite\oDrawManagement\dto\response\ApiResponse;
use PHPUnit\Framework\TestCase;

/**
 * Tests the ApiResponse DTO class.
 *
 * @covers \models\composite\oDrawManagement\dto\response\ApiResponse::success
 * @covers \models\composite\oDrawManagement\dto\response\ApiResponse::error
 * @covers \models\composite\oDrawManagement\dto\response\ApiResponse::validationError
 * @covers \models\composite\oDrawManagement\dto\response\ApiResponse::hasErrors
 * @covers \models\composite\oDrawManagement\dto\response\ApiResponse::addError
 * @covers \models\composite\oDrawManagement\dto\response\ApiResponse::addErrors
 * @covers \models\composite\oDrawManagement\dto\response\ApiResponse::setData
 * @covers \models\composite\oDrawManagement\dto\response\ApiResponse::toArray
 */
class ApiResponseTest extends TestCase
{
    /**
     * Tests creating a successful response.
     */
    public function testSuccessResponse(): void
    {
        $data = ['test' => 'value'];
        $message = 'Test successful';

        $response = ApiResponse::success($data, $message);

        $this->assertInstanceOf(ApiResponse::class, $response);
        $this->assertTrue($response->success);
        $this->assertEquals($message, $response->message);
        $this->assertEquals($data, $response->data);
        $this->assertEmpty($response->errors);
        $this->assertNotNull($response->timestamp);
        $this->assertFalse($response->hasErrors());
    }

    /**
     * Tests creating a successful response with default message.
     */
    public function testSuccessResponseWithDefaultMessage(): void
    {
        $data = ['test' => 'value'];

        $response = ApiResponse::success($data);

        $this->assertTrue($response->success);
        $this->assertEquals('Operation completed successfully', $response->message);
        $this->assertEquals($data, $response->data);
    }

    /**
     * Tests creating a successful response with null data.
     */
    public function testSuccessResponseWithNullData(): void
    {
        $response = ApiResponse::success();

        $this->assertTrue($response->success);
        $this->assertNull($response->data);
        $this->assertEquals('Operation completed successfully', $response->message);
    }

    /**
     * Tests creating an error response.
     */
    public function testErrorResponse(): void
    {
        $message = 'Test error';
        $errors = ['field1' => 'Error 1', 'field2' => 'Error 2'];
        $data = ['debug' => 'info'];

        $response = ApiResponse::error($message, $errors, $data);

        $this->assertInstanceOf(ApiResponse::class, $response);
        $this->assertFalse($response->success);
        $this->assertEquals($message, $response->message);
        $this->assertEquals($errors, $response->errors);
        $this->assertEquals($data, $response->data);
        $this->assertNotNull($response->timestamp);
        $this->assertTrue($response->hasErrors());
    }

    /**
     * Tests creating an error response with minimal parameters.
     */
    public function testErrorResponseMinimal(): void
    {
        $message = 'Test error';

        $response = ApiResponse::error($message);

        $this->assertFalse($response->success);
        $this->assertEquals($message, $response->message);
        $this->assertEmpty($response->errors);
        $this->assertNull($response->data);
    }

    /**
     * Tests creating a validation error response.
     */
    public function testValidationErrorResponse(): void
    {
        $validationErrors = ['name' => 'Name is required', 'email' => 'Invalid email'];
        $message = 'Custom validation message';

        $response = ApiResponse::validationError($validationErrors, $message);

        $this->assertFalse($response->success);
        $this->assertEquals($message, $response->message);
        $this->assertEquals($validationErrors, $response->errors);
        $this->assertTrue($response->hasErrors());
    }

    /**
     * Tests creating a validation error response with default message.
     */
    public function testValidationErrorResponseWithDefaultMessage(): void
    {
        $validationErrors = ['name' => 'Name is required'];

        $response = ApiResponse::validationError($validationErrors);

        $this->assertEquals('Validation failed', $response->message);
        $this->assertEquals($validationErrors, $response->errors);
    }

    /**
     * Tests hasErrors method.
     */
    public function testHasErrors(): void
    {
        $response = new ApiResponse();

        $this->assertFalse($response->hasErrors());


        $response->errors = ['error1', 'error2'];
        $this->assertTrue($response->hasErrors());

        $response->errors = [];
        $this->assertFalse($response->hasErrors());
    }

    /**
     * Tests addError method.
     */
    public function testAddError(): void
    {
        $response = new ApiResponse();
        $response->success = true;

        $response->addError('Test error');


        $this->assertContains('Test error', $response->errors);
        $this->assertTrue($response->hasErrors());
    }

    /**
     * Tests addErrors method.
     */
    public function testAddErrors(): void
    {
        $response = new ApiResponse();
        $response->success = true;
        $response->errors = ['existing error'];

        $newErrors = ['error1', 'error2'];
        $response->addErrors($newErrors);


        $this->assertContains('existing error', $response->errors);
        $this->assertContains('error1', $response->errors);
        $this->assertContains('error2', $response->errors);
        $this->assertCount(3, $response->errors);
    }

    /**
     * Tests addErrors method with empty array.
     */
    public function testAddErrorsWithEmptyArray(): void
    {
        $response = new ApiResponse();
        $response->success = true;

        $response->addErrors([]);


        $this->assertEmpty($response->errors);
    }

    /**
     * Tests setData method.
     */
    public function testSetData(): void
    {
        $response = new ApiResponse();
        $data = ['key' => 'value'];

        $response->setData($data);

        $this->assertEquals($data, $response->data);
    }

    /**
     * Tests setData method with null.
     */
    public function testSetDataWithNull(): void
    {
        $response = new ApiResponse();
        $response->data = 'existing data';

        $response->setData(null);

        $this->assertNull($response->data);
    }

    /**
     * Tests toArray method.
     */
    public function testToArray(): void
    {
        $response = new ApiResponse();
        $response->success = true;
        $response->message = 'Test message';
        $response->data = ['key' => 'value'];
        $response->errors = ['error1'];
        $response->timestamp = '2023-01-01T12:00:00+00:00';

        $result = $response->toArray();

        $this->assertIsArray($result);
        $this->assertTrue($result['success']);
        $this->assertEquals('Test message', $result['message']);
        $this->assertEquals(['key' => 'value'], $result['data']);
        $this->assertEquals(['error1'], $result['errors']);
        $this->assertEquals('2023-01-01T12:00:00+00:00', $result['timestamp']);
    }

    /**
     * Tests toArray method with object data that has toArray method.
     */
    public function testToArrayWithObjectData(): void
    {
        $mockObject = new class {
            public function toArray(bool $includeNulls = true): array {
                return ['mock' => 'data'];
            }
        };

        $response = new ApiResponse();
        $response->data = $mockObject;

        $result = $response->toArray();

        $this->assertEquals(['mock' => 'data'], $result['data']);
    }

    /**
     * Tests toArray method with array data.
     */
    public function testToArrayWithArrayData(): void
    {
        $response = new ApiResponse();
        $response->data = [
            'simple' => 'value',
            'object' => new class {
                public function toArray(bool $includeNulls = true): array {
                    return ['nested' => 'data'];
                }
            }
        ];

        $result = $response->toArray();

        $this->assertIsArray($result['data']);
        $this->assertEquals('value', $result['data']['simple']);
        $this->assertEquals(['nested' => 'data'], $result['data']['object']);
    }

    /**
     * Tests toArray method excluding nulls.
     */
    public function testToArrayExcludeNulls(): void
    {
        $response = new ApiResponse();
        $response->success = true;
        $response->message = 'Test';
        $response->data = null;
        $response->errors = [];
        $response->timestamp = '2023-01-01T12:00:00+00:00';

        $result = $response->toArray(false);

        $this->assertArrayNotHasKey('data', $result);
        $this->assertArrayHasKey('success', $result);
        $this->assertArrayHasKey('message', $result);
        $this->assertArrayHasKey('errors', $result);
        $this->assertArrayHasKey('timestamp', $result);
    }

    /**
     * Tests toArray method including nulls.
     */
    public function testToArrayIncludeNulls(): void
    {
        $response = new ApiResponse();
        $response->success = true;
        $response->message = 'Test';
        $response->data = null;
        $response->errors = [];
        $response->timestamp = '2023-01-01T12:00:00+00:00';

        $result = $response->toArray(true);

        $this->assertArrayHasKey('data', $result);
        $this->assertNull($result['data']);
    }

    /**
     * Tests default property values.
     */
    public function testDefaultValues(): void
    {
        $response = new ApiResponse();

        $this->assertFalse($response->success);
        $this->assertEquals('', $response->message);
        $this->assertNull($response->data);
        $this->assertIsArray($response->errors);
        $this->assertEmpty($response->errors);
        $this->assertNull($response->timestamp);
    }

    /**
     * Tests timestamp format in success response.
     */
    public function testTimestampFormat(): void
    {
        $response = ApiResponse::success();

        $this->assertNotNull($response->timestamp);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/', $response->timestamp);
    }

    /**
     * Tests timestamp format in error response.
     */
    public function testTimestampFormatInError(): void
    {
        $response = ApiResponse::error('Test error');

        $this->assertNotNull($response->timestamp);
        $this->assertMatchesRegularExpression('/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[+-]\d{2}:\d{2}$/', $response->timestamp);
    }
}
