<?php

namespace tests\models\composite\oDrawManagement\dto;

use models\composite\oDrawManagement\dto\shared\CategoryData;
use models\composite\oDrawManagement\dto\shared\LineItemData;
use PHPUnit\Framework\TestCase;

/**
 * Tests the CategoryData DTO class.
 *
 * @covers \models\composite\oDrawManagement\dto\shared\CategoryData::getValidationRules
 * @covers \models\composite\oDrawManagement\dto\shared\CategoryData::populateFromArray
 * @covers \models\composite\oDrawManagement\dto\shared\CategoryData::toArray
 * @covers \models\composite\oDrawManagement\dto\shared\CategoryData::addLineItem
 * @covers \models\composite\oDrawManagement\dto\shared\CategoryData::getLineItem
 * @covers \models\composite\oDrawManagement\dto\shared\CategoryData::removeLineItem
 * @covers \models\composite\oDrawManagement\dto\shared\CategoryData::getTotalCost
 * @covers \models\composite\oDrawManagement\dto\shared\CategoryData::getTotalCompletedAmount
 * @covers \models\composite\oDrawManagement\dto\shared\CategoryData::getTotalRequestedAmount
 * @covers \models\composite\oDrawManagement\dto\shared\CategoryData::sortLineItems
 */
class CategoryDataTest extends TestCase
{
    /**
     * Tests creating CategoryData from array.
     */
    public function testFromArray(): void
    {
        $data = [
            'id' => 123,
            'drawId' => 456,
            'templateId' => 789,
            'categoryName' => 'Test Category',
            'description' => 'Test Description',
            'order' => 2,
            'createdAt' => '2023-01-01 12:00:00',
            'updatedAt' => '2023-01-02 12:00:00',
            'lineItems' => [
                [
                    'id' => 1,
                    'name' => 'Line Item 1',
                    'cost' => 100.0,
                    'order' => 1
                ],
                [
                    'id' => 2,
                    'name' => 'Line Item 2',
                    'cost' => 200.0,
                    'order' => 2
                ]
            ]
        ];

        $category = CategoryData::fromArray($data);

        $this->assertEquals(123, $category->id);
        $this->assertEquals(456, $category->drawId);
        $this->assertEquals(789, $category->templateId);
        $this->assertEquals('Test Category', $category->categoryName);
        $this->assertEquals('Test Description', $category->description);
        $this->assertEquals(2, $category->order);
        $this->assertEquals('2023-01-01 12:00:00', $category->createdAt);
        $this->assertEquals('2023-01-02 12:00:00', $category->updatedAt);
        $this->assertCount(2, $category->lineItems);
        $this->assertInstanceOf(LineItemData::class, $category->lineItems[0]);
        $this->assertEquals('Line Item 1', $category->lineItems[0]->name);
    }

    /**
     * Tests validation with valid data.
     */
    public function testValidationWithValidData(): void
    {
        $category = new CategoryData();
        $category->categoryName = 'Valid Category';
        $category->description = 'Valid description';
        $category->order = 1;

        $this->assertTrue($category->validate());
        $this->assertEmpty($category->getValidationErrors());
    }

    /**
     * Tests validation with missing required fields.
     */
    public function testValidationWithMissingRequiredFields(): void
    {
        $category = new CategoryData();
        // categoryName is required but not set
        $category->order = 0; // Invalid (min: 1)

        $this->assertFalse($category->validate());
        $errors = $category->getValidationErrors();
        $this->assertArrayHasKey('categoryName', $errors);
        $this->assertArrayHasKey('order', $errors);
    }

    /**
     * Tests validation with invalid string lengths.
     */
    public function testValidationWithInvalidStringLengths(): void
    {
        $category = new CategoryData();
        $category->categoryName = str_repeat('A', 256);
        $category->description = str_repeat('B', 1001);
        $category->order = 1;

        $this->assertFalse($category->validate());
        $errors = $category->getValidationErrors();
        $this->assertArrayHasKey('categoryName', $errors);
        $this->assertArrayHasKey('description', $errors);
    }

    /**
     * Tests toArray method.
     */
    public function testToArray(): void
    {
        $category = new CategoryData();
        $category->id = 123;
        $category->categoryName = 'Test Category';
        $category->description = 'Test Description';
        $category->order = 1;


        $lineItem1 = new LineItemData();
        $lineItem1->id = 1;
        $lineItem1->name = 'Line Item 1';
        $lineItem1->cost = 100.0;

        $lineItem2 = new LineItemData();
        $lineItem2->id = 2;
        $lineItem2->name = 'Line Item 2';
        $lineItem2->cost = 200.0;

        $category->addLineItem($lineItem1);
        $category->addLineItem($lineItem2);

        $result = $category->toArray();

        $this->assertIsArray($result);
        $this->assertEquals(123, $result['id']);
        $this->assertEquals('Test Category', $result['categoryName']);
        $this->assertEquals('Test Description', $result['description']);
        $this->assertEquals(1, $result['order']);
        $this->assertArrayHasKey('lineItems', $result);
        $this->assertCount(2, $result['lineItems']);
        $this->assertEquals('Line Item 1', $result['lineItems'][0]['name']);
        $this->assertEquals('Line Item 2', $result['lineItems'][1]['name']);
    }

    /**
     * Tests addLineItem method.
     */
    public function testAddLineItem(): void
    {
        $category = new CategoryData();
        $lineItem = new LineItemData();
        $lineItem->id = 1;
        $lineItem->name = 'Test Line Item';

        $category->addLineItem($lineItem);

        $this->assertCount(1, $category->lineItems);
        $this->assertEquals('Test Line Item', $category->lineItems[0]->name);
    }

    /**
     * Tests getLineItem method.
     */
    public function testGetLineItem(): void
    {
        $category = new CategoryData();

        $lineItem1 = new LineItemData();
        $lineItem1->id = 1;
        $lineItem1->name = 'Line Item 1';

        $lineItem2 = new LineItemData();
        $lineItem2->id = 2;
        $lineItem2->name = 'Line Item 2';

        $category->addLineItem($lineItem1);
        $category->addLineItem($lineItem2);

        $found = $category->getLineItem(2);
        $this->assertInstanceOf(LineItemData::class, $found);
        $this->assertEquals('Line Item 2', $found->name);

        $notFound = $category->getLineItem(999);
        $this->assertNull($notFound);
    }

    /**
     * Tests removeLineItem method.
     */
    public function testRemoveLineItem(): void
    {
        $category = new CategoryData();

        $lineItem1 = new LineItemData();
        $lineItem1->id = 1;
        $lineItem1->name = 'Line Item 1';

        $lineItem2 = new LineItemData();
        $lineItem2->id = 2;
        $lineItem2->name = 'Line Item 2';

        $category->addLineItem($lineItem1);
        $category->addLineItem($lineItem2);

        $this->assertCount(2, $category->lineItems);

        $removed = $category->removeLineItem(1);
        $this->assertTrue($removed);
        $this->assertCount(1, $category->lineItems);
        $this->assertEquals('Line Item 2', $category->lineItems[0]->name);

        $notRemoved = $category->removeLineItem(999);
        $this->assertFalse($notRemoved);
    }

    /**
     * Tests getTotalCost method.
     */
    public function testGetTotalCost(): void
    {
        $category = new CategoryData();

        $lineItem1 = new LineItemData();
        $lineItem1->cost = 100.0;

        $lineItem2 = new LineItemData();
        $lineItem2->cost = 200.0;

        $lineItem3 = new LineItemData();

        $category->addLineItem($lineItem1);
        $category->addLineItem($lineItem2);
        $category->addLineItem($lineItem3);

        $total = $category->getTotalCost();
        $this->assertEquals(300.0, $total);
    }

    /**
     * Tests getTotalCompletedAmount method.
     */
    public function testGetTotalCompletedAmount(): void
    {
        $category = new CategoryData();

        $lineItem1 = new LineItemData();
        $lineItem1->completedAmount = 50.0;

        $lineItem2 = new LineItemData();
        $lineItem2->completedAmount = 75.0;

        $category->addLineItem($lineItem1);
        $category->addLineItem($lineItem2);

        $total = $category->getTotalCompletedAmount();
        $this->assertEquals(125.0, $total);
    }

    /**
     * Tests getTotalRequestedAmount method.
     */
    public function testGetTotalRequestedAmount(): void
    {
        $category = new CategoryData();

        $lineItem1 = new LineItemData();
        $lineItem1->requestedAmount = 25.0;

        $lineItem2 = new LineItemData();
        $lineItem2->requestedAmount = 35.0;

        $category->addLineItem($lineItem1);
        $category->addLineItem($lineItem2);

        $total = $category->getTotalRequestedAmount();
        $this->assertEquals(60.0, $total);
    }

    /**
     * Tests sortLineItems method.
     */
    public function testSortLineItems(): void
    {
        $category = new CategoryData();

        $lineItem1 = new LineItemData();
        $lineItem1->id = 1;
        $lineItem1->name = 'Third';
        $lineItem1->order = 3;

        $lineItem2 = new LineItemData();
        $lineItem2->id = 2;
        $lineItem2->name = 'First';
        $lineItem2->order = 1;

        $lineItem3 = new LineItemData();
        $lineItem3->id = 3;
        $lineItem3->name = 'Second';
        $lineItem3->order = 2;

        $category->addLineItem($lineItem1);
        $category->addLineItem($lineItem2);
        $category->addLineItem($lineItem3);

        // Before sorting
        $this->assertEquals('Third', $category->lineItems[0]->name);
        $this->assertEquals('First', $category->lineItems[1]->name);
        $this->assertEquals('Second', $category->lineItems[2]->name);

        $category->sortLineItems();

        // After sorting
        $this->assertEquals('First', $category->lineItems[0]->name);
        $this->assertEquals('Second', $category->lineItems[1]->name);
        $this->assertEquals('Third', $category->lineItems[2]->name);
    }

    /**
     * Tests populateFromArray with LineItemData objects.
     */
    public function testPopulateFromArrayWithLineItemObjects(): void
    {
        $lineItem = new LineItemData();
        $lineItem->id = 1;
        $lineItem->name = 'Test Line Item';

        $data = [
            'categoryName' => 'Test Category',
            'order' => 1,
            'lineItems' => [$lineItem]
        ];

        $category = CategoryData::fromArray($data);

        $this->assertCount(1, $category->lineItems);
        $this->assertInstanceOf(LineItemData::class, $category->lineItems[0]);
        $this->assertEquals('Test Line Item', $category->lineItems[0]->name);
    }

    /**
     * Tests default values.
     */
    public function testDefaultValues(): void
    {
        $category = new CategoryData();

        $this->assertNull($category->id);
        $this->assertNull($category->drawId);
        $this->assertNull($category->templateId);
        $this->assertEquals('', $category->categoryName);
        $this->assertEquals('', $category->description);
        $this->assertEquals(1, $category->order);
        $this->assertNull($category->createdAt);
        $this->assertNull($category->updatedAt);
        $this->assertIsArray($category->lineItems);
        $this->assertEmpty($category->lineItems);
    }
}
