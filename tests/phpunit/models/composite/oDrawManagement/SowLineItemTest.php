<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\SowLineItem;
use models\lendingwise\tblDrawTemplateLineItems;
use PHPUnit\Framework\TestCase;

/**
 * Tests the SowLineItem class.
 *
 * @covers \models\composite\oDrawManagement\SowLineItem::__construct
 * @covers \models\composite\oDrawManagement\SowLineItem::save
 * @covers \models\composite\oDrawManagement\SowLineItem::delete
 * @covers \models\composite\oDrawManagement\SowLineItem::toArray
 */
class SowLineItemTest extends TestCase
{
    /**
     * Tests the constructor with null parameter.
     */
    public function testConstructorWithNull(): void
    {
        $lineItem = new SowLineItem(null);

        $this->assertInstanceOf(SowLineItem::class, $lineItem);
        $this->assertInstanceOf(tblDrawTemplateLineItems::class, $lineItem);
        $this->assertNull($lineItem->id);
        $this->assertNull($lineItem->templateId);
        $this->assertNull($lineItem->categoryId);
        $this->assertNull($lineItem->name);
        $this->assertNull($lineItem->description);
        $this->assertEquals(1, $lineItem->order);
    }

    /**
     * Tests the constructor with a database object.
     */
    public function testConstructorWithDbObject(): void
    {
        $dbObject = new tblDrawTemplateLineItems();
        $dbObject->id = 123;
        $dbObject->templateId = 456;
        $dbObject->categoryId = 789;
        $dbObject->name = 'Test Line Item';
        $dbObject->description = 'Test Description';
        $dbObject->order = 2;

        $lineItem = new SowLineItem($dbObject);

        $this->assertInstanceOf(SowLineItem::class, $lineItem);
        $this->assertEquals(123, $lineItem->id);
        $this->assertEquals(456, $lineItem->templateId);
        $this->assertEquals(789, $lineItem->categoryId);
        $this->assertEquals('Test Line Item', $lineItem->name);
        $this->assertEquals('Test Description', $lineItem->description);
        $this->assertEquals(2, $lineItem->order);
    }

    /**
     * Tests the save method with complete data.
     */
    public function testSaveWithCompleteData(): void
    {
        $lineItem = new SowLineItem();
        $data = [
            'id' => 123,
            'templateId' => 456,
            'categoryId' => 789,
            'name' => 'Test Line Item',
            'description' => 'Test Description',
            'order' => 2
        ];

        try {
            $result = $lineItem->Save($data);
            $this->assertIsArray($result);
            $this->assertEquals(123, $lineItem->id);
            $this->assertEquals(456, $lineItem->templateId);
            $this->assertEquals(789, $lineItem->categoryId);
            $this->assertEquals('Test Line Item', $lineItem->name);
            $this->assertEquals('Test Description', $lineItem->description);
            $this->assertEquals(2, $lineItem->order);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests the save method with minimal required data.
     */
    public function testSaveWithMinimalData(): void
    {
        $lineItem = new SowLineItem();
        $data = [
            'templateId' => 456,
            'categoryId' => 789,
            'name' => 'Test Line Item'
        ];

        try {
            $result = $lineItem->Save($data);
            $this->assertIsArray($result);
            $this->assertEquals(456, $lineItem->templateId);
            $this->assertEquals(789, $lineItem->categoryId);
            $this->assertEquals('Test Line Item', $lineItem->name);
            $this->assertEquals('', $lineItem->description); // Default empty string
            $this->assertEquals(1, $lineItem->order); // Default value
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests the delete method.
     */
    public function testDelete(): void
    {
        $lineItem = new SowLineItem();

        try {
            $lineItem->Delete();
            $this->addToAssertionCount(1); // Method executed without error
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Delete', $e->getMessage());
        }
    }

    /**
     * Tests the delete method with null line item.
     */
    public function testDeleteWithNullLineItem(): void
    {
        $lineItem = new SowLineItem();
        $lineItem = null;


        $this->addToAssertionCount(1); // Method executed without error
    }

    /**
     * Tests the toArray method.
     */
    public function testToArray(): void
    {
        $lineItem = new SowLineItem();
        $lineItem->id = 123;
        $lineItem->templateId = 456;
        $lineItem->categoryId = 789;
        $lineItem->name = 'Test Line Item';
        $lineItem->description = 'Test Description';
        $lineItem->order = 2;

        $result = $lineItem->toArray();

        $this->assertIsArray($result);
        $this->assertEquals(123, $result['id']);
        $this->assertEquals(456, $result['templateId']);
        $this->assertEquals(789, $result['categoryId']);
        $this->assertEquals('Test Line Item', $result['name']);
        $this->assertEquals('Test Description', $result['description']);
        $this->assertEquals(2, $result['order']);
    }

    /**
     * Tests property default values.
     */
    public function testPropertyDefaults(): void
    {
        $lineItem = new SowLineItem();

        $this->assertNull($lineItem->id);
        $this->assertNull($lineItem->templateId);
        $this->assertNull($lineItem->categoryId);
        $this->assertNull($lineItem->name);
        $this->assertNull($lineItem->description);
        $this->assertEquals(1, $lineItem->order);
        $this->assertInstanceOf(tblDrawTemplateLineItems::class, $lineItem);
    }

    /**
     * Tests save method with null id.
     */
    public function testSaveWithNullId(): void
    {
        $lineItem = new SowLineItem();
        $data = [
            'id' => null,
            'templateId' => 456,
            'categoryId' => 789,
            'name' => 'Test Line Item'
        ];

        try {
            $result = $lineItem->Save($data);
            $this->assertIsArray($result);

        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests save method with special characters.
     */
    public function testSaveWithSpecialCharacters(): void
    {
        $lineItem = new SowLineItem();
        $data = [
            'templateId' => 456,
            'categoryId' => 789,
            'name' => 'Line Item with "quotes" & symbols',
            'description' => 'Description with <html> tags'
        ];

        try {
            $result = $lineItem->Save($data);
            $this->assertIsArray($result);
            $this->assertEquals('Line Item with "quotes" & symbols', $lineItem->name);
            $this->assertEquals('Description with <html> tags', $lineItem->description);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests order property default value in save.
     */
    public function testOrderDefaultValueInSave(): void
    {
        $lineItem = new SowLineItem();
        $data = [
            'templateId' => 456,
            'categoryId' => 789,
            'name' => 'Test Line Item'
            // order not provided - should use default
        ];

        try {
            $lineItem->Save($data);
            $this->assertEquals(1, $lineItem->order);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests description property default value in save.
     */
    public function testDescriptionDefaultValueInSave(): void
    {
        $lineItem = new SowLineItem();
        $data = [
            'templateId' => 456,
            'categoryId' => 789,
            'name' => 'Test Line Item'
            // description not provided - should use default
        ];

        try {
            $lineItem->Save($data);
            $this->assertEquals('', $lineItem->description);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests save method with zero order.
     */
    public function testSaveWithZeroOrder(): void
    {
        $lineItem = new SowLineItem();
        $data = [
            'templateId' => 456,
            'categoryId' => 789,
            'name' => 'Test Line Item',
            'order' => 0
        ];

        try {
            $result = $lineItem->Save($data);
            $this->assertIsArray($result);
            $this->assertEquals(0, $lineItem->order);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests save method with large order value.
     */
    public function testSaveWithLargeOrder(): void
    {
        $lineItem = new SowLineItem();
        $data = [
            'templateId' => 456,
            'categoryId' => 789,
            'name' => 'Test Line Item',
            'order' => 999
        ];

        try {
            $result = $lineItem->Save($data);
            $this->assertIsArray($result);
            $this->assertEquals(999, $lineItem->order);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests toArray method with null values.
     */
    public function testToArrayWithNullValues(): void
    {
        $lineItem = new SowLineItem();

        $result = $lineItem->toArray();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('id', $result);
        $this->assertArrayHasKey('templateId', $result);
        $this->assertArrayHasKey('categoryId', $result);
        $this->assertArrayHasKey('name', $result);
        $this->assertArrayHasKey('description', $result);
        $this->assertArrayHasKey('order', $result);
        $this->assertNull($result['id']);
        $this->assertNull($result['templateId']);
        $this->assertNull($result['categoryId']);
        $this->assertNull($result['name']);
        $this->assertNull($result['description']);
        $this->assertEquals(1, $result['order']);
    }
}
