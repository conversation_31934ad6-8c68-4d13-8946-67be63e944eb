<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\SowTemplate;
use models\composite\oDrawManagement\SowCategory;
use models\lendingwise\tblProcessingCompanyDrawTemplateSettings;
use PHPUnit\Framework\TestCase;

/**
 * Tests the SowTemplate class.
 *
 * @covers \models\composite\oDrawManagement\SowTemplate::__construct
 * @covers \models\composite\oDrawManagement\SowTemplate::addCategory
 * @covers \models\composite\oDrawManagement\SowTemplate::toArray
 * @covers \models\composite\oDrawManagement\SowTemplate::save
 * @covers \models\composite\oDrawManagement\SowTemplate::getCategories
 * @covers \models\composite\oDrawManagement\SowTemplate::getCategoryById
 * @covers \models\composite\oDrawManagement\SowTemplate::deleteCategories
 * @covers \models\composite\oDrawManagement\SowTemplate::deleteLineItems
 */
class SowTemplateTest extends TestCase
{
    /**
     * Tests the constructor with a database object.
     */
    public function testConstructorWithDbObject(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;
        $dbObject->allowBorrowersAddEditCategories = 1;
        $dbObject->allowBorrowersDeleteCategories = 0;
        $dbObject->allowBorrowersAddEditLineItems = 1;
        $dbObject->allowBorrowersDeleteLineItems = 0;
        $dbObject->allowBorrowersSOWRevisions = 1;
        $dbObject->allowBorrowersExceedFinancedRehabCostOnRevision = 0;
        $dbObject->drawFee = 50.00;

        $template = new SowTemplate($dbObject);

        $this->assertInstanceOf(SowTemplate::class, $template);
        $this->assertEquals(123, $template->id);
        $this->assertEquals(456, $template->PCID);
        $this->assertEquals(1, $template->allowBorrowersAddEditCategories);
        $this->assertEquals(0, $template->allowBorrowersDeleteCategories);
        $this->assertEquals(1, $template->allowBorrowersAddEditLineItems);
        $this->assertEquals(0, $template->allowBorrowersDeleteLineItems);
        $this->assertEquals(1, $template->allowBorrowersSOWRevisions);
        $this->assertEquals(0, $template->allowBorrowersExceedFinancedRehabCostOnRevision);
        $this->assertEquals(50.00, $template->drawFee);
        $this->assertInstanceOf(tblProcessingCompanyDrawTemplateSettings::class, $template);
        $this->assertIsArray($template->getCategories());
    }

    /**
     * Tests the addCategory method.
     */
    public function testAddCategory(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);


        $categoryDbObject = new \models\lendingwise\tblDrawTemplateCategories();
        $categoryDbObject->id = 789;
        $categoryDbObject->categoryName = 'Test Category';

        $category = new SowCategory($categoryDbObject);

        $template->addCategory($category);

        $categories = $template->getCategories();
        $this->assertArrayHasKey(789, $categories);
        $this->assertInstanceOf(SowCategory::class, $categories[789]);
        $this->assertEquals('Test Category', $categories[789]->categoryName);
    }

    /**
     * Tests the getCategories method.
     */
    public function testgetCategories(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);

        $result = $template->getCategories();

        $this->assertIsArray($result);
        $this->assertEmpty($result); // Initially empty
    }

    /**
     * Tests the getCategoryById method with non-existent category.
     */
    public function testGetCategoryByIdNonExistent(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);

        $result = $template->getCategoryById(999);

        $this->assertNull($result);
    }

    /**
     * Tests the getCategoryById method with existing category.
     */
    public function testGetCategoryByIdExisting(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);


        $categoryDbObject = new \models\lendingwise\tblDrawTemplateCategories();
        $categoryDbObject->id = 789;
        $categoryDbObject->categoryName = 'Test Category';

        $category = new SowCategory($categoryDbObject);
        $template->addCategory($category);

        $result = $template->getCategoryById(789);

        $this->assertInstanceOf(SowCategory::class, $result);
        $this->assertEquals('Test Category', $result->categoryName);
    }

    /**
     * Tests the toArray method.
     */
    public function testToArray(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;
        $dbObject->allowBorrowersAddEditCategories = 1;
        $dbObject->allowBorrowersDeleteCategories = 0;
        $dbObject->allowBorrowersAddEditLineItems = 1;
        $dbObject->allowBorrowersDeleteLineItems = 0;
        $dbObject->allowBorrowersSOWRevisions = 1;
        $dbObject->allowBorrowersExceedFinancedRehabCostOnRevision = 0;
        $dbObject->drawFee = 50.00;

        $template = new SowTemplate($dbObject);

        $result = $template->toArray();

        $this->assertIsArray($result);
        $this->assertEquals(123, $result['id']);
        $this->assertEquals(456, $result['pcId']);
        $this->assertEquals(1, $result['allowBorrowersAddEditCategories']);
        $this->assertEquals(0, $result['allowBorrowersDeleteCategories']);
        $this->assertEquals(1, $result['allowBorrowersAddEditLineItems']);
        $this->assertEquals(0, $result['allowBorrowersDeleteLineItems']);
        $this->assertEquals(1, $result['allowBorrowersSOWRevisions']);
        $this->assertEquals(0, $result['allowBorrowersExceedFinancedRehabCostOnRevision']);
        $this->assertEquals(50.00, $result['drawFee']);
        $this->assertArrayHasKey('categories', $result);
        $this->assertIsArray($result['categories']);
    }

    /**
     * Tests the save method.
     */
    public function testSave(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);

        $data = [
            'allowBorrowersAddEditCategories' => 1,
            'allowBorrowersDeleteCategories' => 0,
            'allowBorrowersAddEditLineItems' => 1,
            'allowBorrowersDeleteLineItems' => 0,
            'allowBorrowersSOWRevisions' => 1,
            'allowBorrowersExceedFinancedRehabCostOnRevision' => 0,
            'drawFee' => 75.50
        ];

        try {
            $template->Save($data);
            $this->addToAssertionCount(1); // Method executed without error
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests the save method with missing data.
     */
    public function testSaveWithMissingData(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);

        $data = []; // Empty data - should use defaults

        try {
            $template->Save($data);
            $this->addToAssertionCount(1); // Method executed without error
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests the deleteCategories method with empty array.
     */
    public function testDeleteCategoriesEmpty(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);


        $this->addToAssertionCount(1); // Method executed without error
    }

    /**
     * Tests the deleteCategories method with non-existent IDs.
     */
    public function testDeleteCategoriesNonExistent(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);


        $this->addToAssertionCount(1); // Method executed without error
    }

    /**
     * Tests the deleteLineItems method with empty array.
     */
    public function testDeleteLineItemsEmpty(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);


        $this->addToAssertionCount(1); // Method executed without error
    }

    /**
     * Tests the deleteLineItems method with non-existent IDs.
     */
    public function testDeleteLineItemsNonExistent(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);


        $this->addToAssertionCount(1); // Method executed without error
    }

    /**
     * Tests property initialization.
     */
    public function testPropertyInitialization(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;
        $dbObject->allowBorrowersAddEditCategories = null;
        $dbObject->allowBorrowersDeleteCategories = null;
        $dbObject->allowBorrowersAddEditLineItems = null;
        $dbObject->allowBorrowersDeleteLineItems = null;
        $dbObject->allowBorrowersSOWRevisions = null;
        $dbObject->allowBorrowersExceedFinancedRehabCostOnRevision = null;
        $dbObject->drawFee = null;

        $template = new SowTemplate($dbObject);

        $this->assertEquals(123, $template->id);
        $this->assertEquals(456, $template->PCID);
        $this->assertNull($template->allowBorrowersAddEditCategories);
        $this->assertNull($template->allowBorrowersDeleteCategories);
        $this->assertNull($template->allowBorrowersAddEditLineItems);
        $this->assertNull($template->allowBorrowersDeleteLineItems);
        $this->assertNull($template->allowBorrowersSOWRevisions);
        $this->assertNull($template->allowBorrowersExceedFinancedRehabCostOnRevision);
        $this->assertNull($template->drawFee);
        $this->assertIsArray($template->getCategories());
        $this->assertEmpty($template->getCategories());
    }

    /**
     * Tests save method with string values that should be converted.
     */
    public function testSaveWithStringValues(): void
    {
        $dbObject = new tblProcessingCompanyDrawTemplateSettings();
        $dbObject->id = 123;
        $dbObject->PCID = 456;

        $template = new SowTemplate($dbObject);

        $data = [
            'allowBorrowersAddEditCategories' => '1',
            'allowBorrowersDeleteCategories' => '0',


            'allowBorrowersSOWRevisions' => '1',
            'allowBorrowersExceedFinancedRehabCostOnRevision' => '0',
            'drawFee' => '75.50'
        ];

        try {
            $template->Save($data);
            $this->addToAssertionCount(1); // Method executed without error
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }
}
