<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\BorrowerDrawLineItemHistory;
use models\lendingwise\tblDrawRequestLineItems_h;
use PHPUnit\Framework\TestCase;

/**
 * Tests the BorrowerDrawLineItemHistory class.
 *
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItemHistory::__construct
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItemHistory::save
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItemHistory::setFromArray
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItemHistory::getDbObject
 * @covers \models\composite\oDrawManagement\BorrowerDrawLineItemHistory::toArray
 */
class BorrowerDrawLineItemHistoryTest extends TestCase
{
    /**
     * Tests the constructor with null parameter.
     */
    public function testConstructorWithNull(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory(null);

            $this->assertInstanceOf(BorrowerDrawLineItemHistory::class, $lineItemHistory);
            $this->assertInstanceOf(tblDrawRequestLineItems_h::class, $lineItemHistory);
            $this->assertNull($lineItemHistory->id);
            $this->assertNull($lineItemHistory->recordId);
            $this->assertNull($lineItemHistory->lineItemId);
            $this->assertEquals(0.0, $lineItemHistory->completedAmount);
            $this->assertEquals(0.0, $lineItemHistory->completedPercent);
            $this->assertEquals(0.0, $lineItemHistory->requestedAmount);
            $this->assertEquals(0.0, $lineItemHistory->disbursedAmount);
            $this->assertNull($lineItemHistory->notes);
            $this->assertNull($lineItemHistory->lenderNotes);
            $this->assertNull($lineItemHistory->createdAt);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the constructor with a database object.
     */
    public function testConstructorWithDbObject(): void
    {
        $dbObject = new tblDrawRequestLineItems_h();
        $dbObject->id = 123;
        $dbObject->recordId = 456;
        $dbObject->lineItemId = 789;
        $dbObject->completedAmount = 500.0;
        $dbObject->completedPercent = 50.0;
        $dbObject->requestedAmount = 300.0;
        $dbObject->disbursedAmount = 200.0;
        $dbObject->notes = 'Test notes';
        $dbObject->lenderNotes = 'Test lender notes';
        $dbObject->createdAt = '2023-01-01 12:00:00';

        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory($dbObject);

            $this->assertInstanceOf(BorrowerDrawLineItemHistory::class, $lineItemHistory);
            $this->assertEquals(123, $lineItemHistory->id);
            $this->assertEquals(456, $lineItemHistory->recordId);
            $this->assertEquals(789, $lineItemHistory->lineItemId);
            $this->assertEquals(500.0, $lineItemHistory->completedAmount);
            $this->assertEquals(50.0, $lineItemHistory->completedPercent);
            $this->assertEquals(300.0, $lineItemHistory->requestedAmount);
            $this->assertEquals(200.0, $lineItemHistory->disbursedAmount);
            $this->assertEquals('Test notes', $lineItemHistory->notes);
            $this->assertEquals('Test lender notes', $lineItemHistory->lenderNotes);
            $this->assertEquals('2023-01-01 12:00:00', $lineItemHistory->createdAt);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the save method with complete data.
     */
    public function testSaveWithCompleteData(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'completedAmount' => 500.0,
                'completedPercent' => 50.0,
                'requestedAmount' => 300.0,
                'disbursedAmount' => 200.0,
                'notes' => 'Test notes',
                'lenderNotes' => 'Test lender notes'
            ];

            $result = $lineItemHistory->Save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the save method with minimal data.
     */
    public function testSaveWithMinimalData(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'requestedAmount' => 300.0
            ];

            $result = $lineItemHistory->Save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the save method without data.
     */
    public function testSaveWithoutData(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();

            $result = $lineItemHistory->Save([]);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the getDbObject method.
     */
    public function testGetDbObject(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();

            $result = $lineItemHistory->getDbObject();

            $this->assertInstanceOf(tblDrawRequestLineItems_h::class, $result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests the toArray method.
     */
    public function testToArray(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $lineItemHistory->id = 123;
            $lineItemHistory->recordId = 456;
            $lineItemHistory->lineItemId = 789;
            $lineItemHistory->completedAmount = 500.0;
            $lineItemHistory->completedPercent = 50.0;
            $lineItemHistory->requestedAmount = 300.0;
            $lineItemHistory->disbursedAmount = 200.0;
            $lineItemHistory->notes = 'Test notes';
            $lineItemHistory->lenderNotes = 'Test lender notes';
            $lineItemHistory->createdAt = '2023-01-01 12:00:00';

            $result = $lineItemHistory->toArray();

            $this->assertIsArray($result);
            $this->assertEquals(123, $result['id']);
            $this->assertEquals(456, $result['recordId']);
            $this->assertEquals(789, $result['lineItemId']);
            $this->assertEquals(500.0, $result['completedAmount']);
            $this->assertEquals(50.0, $result['completedPercent']);
            $this->assertEquals(300.0, $result['requestedAmount']);
            $this->assertEquals(200.0, $result['disbursedAmount']);
            $this->assertEquals('Test notes', $result['notes']);
            $this->assertEquals('Test lender notes', $result['lenderNotes']);
            $this->assertEquals('2023-01-01 12:00:00', $result['createdAt']);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests property default values.
     */
    public function testPropertyDefaults(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();

            $this->assertNull($lineItemHistory->id);
            $this->assertNull($lineItemHistory->recordId);
            $this->assertNull($lineItemHistory->lineItemId);
            $this->assertEquals(0.0, $lineItemHistory->completedAmount);
            $this->assertEquals(0.0, $lineItemHistory->completedPercent);
            $this->assertEquals(0.0, $lineItemHistory->requestedAmount);
            $this->assertEquals(0.0, $lineItemHistory->disbursedAmount);
            $this->assertNull($lineItemHistory->notes);
            $this->assertNull($lineItemHistory->lenderNotes);
            $this->assertNull($lineItemHistory->createdAt);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with zero values.
     */
    public function testSaveWithZeroValues(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'completedAmount' => 0.0,
                'completedPercent' => 0.0,
                'requestedAmount' => 0.0,
                'disbursedAmount' => 0.0
            ];

            $result = $lineItemHistory->Save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with null notes.
     */
    public function testSaveWithNullNotes(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'requestedAmount' => 300.0,
                'notes' => null,
                'lenderNotes' => null
            ];

            $result = $lineItemHistory->Save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with empty string notes.
     */
    public function testSaveWithEmptyStringNotes(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'requestedAmount' => 300.0,
                'notes' => '',
                'lenderNotes' => ''
            ];

            $result = $lineItemHistory->Save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with special characters in notes.
     */
    public function testSaveWithSpecialCharactersInNotes(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'requestedAmount' => 300.0,
                'notes' => 'Notes with "quotes" & symbols <html>',
                'lenderNotes' => 'Lender notes with émojis 🏠 and special chars: @#$%'
            ];

            $result = $lineItemHistory->Save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with large amounts.
     */
    public function testSaveWithLargeAmounts(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'completedAmount' => 999999.99,
                'completedPercent' => 100.0,
                'requestedAmount' => 500000.0,
                'disbursedAmount' => 750000.0
            ];

            $result = $lineItemHistory->Save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method preserves existing values when not provided.
     */
    public function testSavePreservesExistingValues(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();


            $lineItemHistory->completedAmount = 500.0;
            $lineItemHistory->notes = 'Initial notes';

            $data = [
                'recordId' => 456,
                'lineItemId' => 789,
                'requestedAmount' => 300.0
            ];

            $result = $lineItemHistory->Save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests toArray with null values.
     */
    public function testToArrayWithNullValues(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();

            $result = $lineItemHistory->toArray();

            $this->assertIsArray($result);
            $this->assertArrayHasKey('id', $result);
            $this->assertArrayHasKey('recordId', $result);
            $this->assertArrayHasKey('lineItemId', $result);
            $this->assertArrayHasKey('completedAmount', $result);
            $this->assertArrayHasKey('completedPercent', $result);
            $this->assertArrayHasKey('requestedAmount', $result);
            $this->assertArrayHasKey('disbursedAmount', $result);
            $this->assertArrayHasKey('notes', $result);
            $this->assertArrayHasKey('lenderNotes', $result);
            $this->assertArrayHasKey('createdAt', $result);

            $this->assertNull($result['id']);
            $this->assertNull($result['recordId']);
            $this->assertNull($result['lineItemId']);
            $this->assertEquals(0.0, $result['completedAmount']);
            $this->assertEquals(0.0, $result['completedPercent']);
            $this->assertEquals(0.0, $result['requestedAmount']);
            $this->assertEquals(0.0, $result['disbursedAmount']);
            $this->assertNull($result['notes']);
            $this->assertNull($result['lenderNotes']);
            $this->assertNull($result['createdAt']);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests save method with string numeric values.
     */
    public function testSaveWithStringNumericValues(): void
    {
        try {
            $lineItemHistory = new BorrowerDrawLineItemHistory();
            $data = [
                'recordId' => '456',
                'lineItemId' => '789',
                'completedAmount' => '500.0',
                'completedPercent' => '50.0',
                'requestedAmount' => '300.0',
                'disbursedAmount' => '200.0'
            ];

            $result = $lineItemHistory->Save($data);
            $this->assertIsArray($result);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }
}
