<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\DrawRequestManager;
use PHPUnit\Framework\TestCase;

/**
 * Tests the DrawRequestManager class.
 *
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequestLineItemsHistory
 * @covers \models\composite\oDrawManagement\DrawRequestManager::forLoanFile
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequestId
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequest
 * @covers \models\composite\oDrawManagement\DrawRequestManager::hasDrawRequest
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequestDataArray
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getDrawRequestHistory
 * @covers \models\composite\oDrawManagement\DrawRequestManager::saveCategories
 * @covers \models\composite\oDrawManagement\DrawRequestManager::saveLineItems
 * @covers \models\composite\oDrawManagement\DrawRequestManager::saveScopeOfWorkData
 * @covers \models\composite\oDrawManagement\DrawRequestManager::saveDrawRequestData
 * @covers \models\composite\oDrawManagement\DrawRequestManager::getOrCreateDrawRequest
 * @covers \models\composite\oDrawManagement\DrawRequestManager::pcIdFromLMRId
 * @covers \models\composite\oDrawManagement\DrawRequestManager::__construct
 */
class DrawRequestManagerTest extends TestCase
{
    /**
     * Tests the getDrawRequestLineItemsHistory method with null LMRId.
     */
    public function testGetDrawRequestLineItemsHistoryWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestLineItemsHistory(1);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests the getDrawRequestLineItemsHistory method returns array.
     */
    public function testGetDrawRequestLineItemsHistoryReturnsArray(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestLineItemsHistory(999999);

        $this->assertIsArray($result);
    }

    /**
     * Tests that the method validates the expected return structure format.
     */
    public function testGetDrawRequestLineItemsHistoryExpectedStructure(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestLineItemsHistory(1);

        $this->assertIsArray($result);
        $this->assertEmpty($result);

        $drawRequestManagerWithData = new DrawRequestManager(123);

        try {
            $drawRequestManagerWithData->getOrCreateDrawRequest();
            $resultWithData = $drawRequestManagerWithData->getDrawRequestLineItemsHistory(1);

            $this->assertIsArray($resultWithData);

            if (!empty($resultWithData)) {
                $this->validateCategoryStructure($resultWithData);
            }
        } catch (\Exception $e) {
        }

        $this->validateExpectedStructureFormat();
        $this->validateExpectedStructureFormat();
    }

    /**
     * Helper method to validate category structure.
     */
    private function validateCategoryStructure(array $categories): void
    {
        foreach ($categories as $category) {
            $this->assertIsArray($category);
            $this->assertArrayHasKey('id', $category);
            $this->assertArrayHasKey('categoryName', $category);
            $this->assertArrayHasKey('description', $category);
            $this->assertArrayHasKey('order', $category);
            $this->assertArrayHasKey('lineItems', $category);

            $this->assertIsInt($category['id']);
            $this->assertIsString($category['categoryName']);
            $this->assertIsString($category['description']);
            $this->assertIsInt($category['order']);
            $this->assertIsArray($category['lineItems']);


            foreach ($category['lineItems'] as $lineItem) {
                $this->validateLineItemStructure($lineItem);
            }
        }
    }

    /**
     * Helper method to validate line item structure.
     */
    private function validateLineItemStructure(array $lineItem): void
    {
        $requiredKeys = [
            'id', 'name', 'description', 'order', 'cost',
            'completedAmount', 'completedPercent', 'requestedAmount', 'disbursedAmount',
            'notes', 'lenderNotes'
        ];

        foreach ($requiredKeys as $key) {
            $this->assertArrayHasKey($key, $lineItem, "Line item missing required key: $key");
        }

        $this->assertIsInt($lineItem['id']);
        $this->assertIsString($lineItem['name']);
        $this->assertIsString($lineItem['description']);
        $this->assertIsInt($lineItem['order']);
        $this->assertIsNumeric($lineItem['cost']);
        $this->assertIsNumeric($lineItem['completedAmount']);
        $this->assertIsNumeric($lineItem['completedPercent']);
        $this->assertIsNumeric($lineItem['requestedAmount']);
        $this->assertIsNumeric($lineItem['disbursedAmount']);
        $this->assertTrue(is_string($lineItem['notes']) || is_null($lineItem['notes']));
        $this->assertTrue(is_string($lineItem['lenderNotes']) || is_null($lineItem['lenderNotes']));


        $this->assertGreaterThanOrEqual(0, $lineItem['completedPercent']);
        $this->assertLessThanOrEqual(100, $lineItem['completedPercent']);


        $this->assertGreaterThanOrEqual(0, $lineItem['cost']);
        $this->assertGreaterThanOrEqual(0, $lineItem['completedAmount']);
        $this->assertGreaterThanOrEqual(0, $lineItem['requestedAmount']);
        $this->assertGreaterThanOrEqual(0, $lineItem['disbursedAmount']);
    }

    /**
     * Helper method to validate expected structure format understanding.
     */
    private function validateExpectedStructureFormat(): void
    {
        $expectedCategoryKeys = ['id', 'categoryName', 'description', 'order', 'lineItems'];
        $expectedLineItemKeys = [
            'id', 'name', 'description', 'order', 'cost',
            'completedAmount', 'completedPercent', 'requestedAmount', 'disbursedAmount',
            'notes', 'lenderNotes'
        ];

        $this->assertIsArray($expectedCategoryKeys);
        $this->assertIsArray($expectedLineItemKeys);
        $this->assertContains('lineItems', $expectedCategoryKeys);
        $this->assertContains('completedAmount', $expectedLineItemKeys);
        $this->assertContains('completedPercent', $expectedLineItemKeys);
        $this->assertContains('requestedAmount', $expectedLineItemKeys);
        $this->assertContains('disbursedAmount', $expectedLineItemKeys);
        $this->assertContains('notes', $expectedLineItemKeys);
        $this->assertContains('lenderNotes', $expectedLineItemKeys);


        $this->assertEquals(5, count($expectedCategoryKeys));
        $this->assertEquals(11, count($expectedLineItemKeys));


        $this->assertContains('categoryName', $expectedCategoryKeys);
        $this->assertNotContains('name', $expectedCategoryKeys); // Categories use 'categoryName', not 'name'
    }

    /**
     * Tests the forLoanFile static factory method with null LMRId.
     */
    public function testForLoanFileWithNullLMRIdThrowsException(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage("LMRId must be provided or available in PageVariables.");

        DrawRequestManager::forLoanFile(null);
    }

    /**
     * Tests the forLoanFile static factory method with valid LMRId.
     */
    public function testForLoanFileWithValidLMRId(): void
    {
        $drawRequestManager = DrawRequestManager::forLoanFile(123);

        $this->assertInstanceOf(DrawRequestManager::class, $drawRequestManager);
    }

    /**
     * Tests getDrawRequestId method when no draw request exists.
     */
    public function testGetDrawRequestIdWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestId();

        $this->assertNull($result);
    }

    /**
     * Tests getDrawRequest method when no draw request exists.
     */
    public function testGetDrawRequestWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequest();

        $this->assertNull($result);
    }

    /**
     * Tests hasDrawRequest method when no draw request exists.
     */
    public function testHasDrawRequestWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->hasDrawRequest();

        $this->assertFalse($result);
    }

    /**
     * Tests getDrawRequestDataArray method when no draw request exists.
     */
    public function testGetDrawRequestDataArrayWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestDataArray();

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests getDrawRequestHistory method when no draw request exists.
     */
    public function testGetDrawRequestHistoryWithNullLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(null);
        $result = $drawRequestManager->getDrawRequestHistory();

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests saveCategories method with too many categories.
     */
    public function testSaveCategoriesWithTooManyCategories(): void
    {
        $drawRequestManager = new DrawRequestManager(123);


        $categoriesData = [];
        for ($i = 0; $i < 21; $i++) {
            $categoriesData[] = [
                'id' => null,
                'categoryName' => "Category $i",
                'description' => "Description $i",
                'order' => $i
            ];
        }

        $result = $drawRequestManager->saveCategories($categoriesData);

        $this->assertFalse($result);
    }

    /**
     * Tests saveCategories method with valid data.
     */
    public function testSaveCategoriesWithValidData(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $categoriesData = [
            [
                'id' => null,
                'categoryName' => 'Test Category',
                'description' => 'Test Description',
                'order' => 1
            ]
        ];

        try {
            $result = $drawRequestManager->saveCategories($categoriesData);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests saveLineItems method with too many categories.
     */
    public function testSaveLineItemsWithTooManyCategories(): void
    {
        $drawRequestManager = new DrawRequestManager(123);


        $lineItemsData = [];
        for ($i = 0; $i < 21; $i++) {
            $lineItemsData[$i] = [
                [
                    'id' => null,
                    'categoryId' => $i,
                    'name' => "Line Item $i",
                    'description' => "Description $i",
                    'order' => 1,
                    'cost' => 100.00
                ]
            ];
        }

        try {
            $result = $drawRequestManager->saveLineItems($lineItemsData);
            $this->assertFalse($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests saveLineItems method with valid data.
     */
    public function testSaveLineItemsWithValidData(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $lineItemsData = [
            1 => [
                [
                    'id' => null,
                    'categoryId' => 1,
                    'name' => 'Test Line Item',
                    'description' => 'Test Description',
                    'order' => 1,
                    'cost' => 100.00
                ]
            ]
        ];

        try {
            $result = $drawRequestManager->saveLineItems($lineItemsData);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests saveLineItems method as draft.
     */
    public function testSaveLineItemsAsDraft(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $lineItemsData = [
            1 => [
                [
                    'id' => null,
                    'categoryId' => 1,
                    'name' => 'Test Line Item',
                    'description' => 'Test Description',
                    'order' => 1,
                    'cost' => 100.00
                ]
            ]
        ];

        try {
            $result = $drawRequestManager->saveLineItems($lineItemsData, true);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests saveScopeOfWorkData method as draft.
     */
    public function testSaveScopeOfWorkDataAsDraft(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        try {
            $result = $drawRequestManager->saveScopeOfWorkData(true);
            $this->assertIsBool($result);
        } catch (\Error $e) {
            $this->assertStringContainsString('updateDrawRequestStatus', $e->getMessage());
        }
    }

    /**
     * Tests saveScopeOfWorkData method not as draft.
     */
    public function testSaveScopeOfWorkDataNotAsDraft(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        try {
            $result = $drawRequestManager->saveScopeOfWorkData(false);
            $this->assertIsBool($result);
        } catch (\Error $e) {
            $this->assertStringContainsString('updateDrawRequestStatus', $e->getMessage());
        }
    }

    /**
     * Tests saveDrawRequestData method with empty data.
     */
    public function testSaveDrawRequestDataWithEmptyData(): void
    {
        $drawRequestManager = new DrawRequestManager(null);

        $postData = [];
        $result = $drawRequestManager->saveDrawRequestData($postData);

        $this->assertFalse($result);
    }

    /**
     * Tests saveDrawRequestData method with missing lineItems.
     */
    public function testSaveDrawRequestDataWithMissingLineItems(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $postData = [
            'status' => 'pending'
        ];
        $result = $drawRequestManager->saveDrawRequestData($postData);

        $this->assertFalse($result);
    }

    /**
     * Tests saveDrawRequestData method with valid data structure.
     */
    public function testSaveDrawRequestDataWithValidStructure(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $postData = [
            'status' => 'pending',
            'lineItems' => [
                1 => [
                    'requestedAmount' => 100.00,
                    'notes' => 'Test notes',
                    'lenderNotes' => 'Test lender notes'
                ]
            ]
        ];

        try {
            $result = $drawRequestManager->saveDrawRequestData($postData);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests getDrawRequestLineItemsHistory with non-existent record.
     */
    public function testGetDrawRequestLineItemsHistoryWithNonExistentRecord(): void
    {
        $drawRequestManager = new DrawRequestManager(123);
        $result = $drawRequestManager->getDrawRequestLineItemsHistory(999999);

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests pcIdFromLMRId static method with valid LMRId.
     */
    public function testPcIdFromLMRIdWithValidId(): void
    {
        $result = DrawRequestManager::pcIdFromLMRId(123);

        $this->assertTrue(is_int($result) || is_null($result));
    }

    /**
     * Tests pcIdFromLMRId static method with non-existent LMRId.
     */
    public function testPcIdFromLMRIdWithNonExistentId(): void
    {
        $result = DrawRequestManager::pcIdFromLMRId(999999999);

        $this->assertNull($result);
    }

    /**
     * Tests MAX_CATEGORIES constant.
     */
    public function testMaxCategoriesConstant(): void
    {
        $this->assertEquals(20, DrawRequestManager::MAX_CATEGORIES);
        $this->assertIsInt(DrawRequestManager::MAX_CATEGORIES);
    }

    /**
     * Tests constructor with valid LMRId.
     */
    public function testConstructorWithValidLMRId(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $this->assertInstanceOf(DrawRequestManager::class, $drawRequestManager);
    }

    /**
     * Tests that getOrCreateDrawRequest method returns DrawRequest instance.
     */
    public function testGetOrCreateDrawRequestReturnsDrawRequest(): void
    {
        $drawRequestManager = new DrawRequestManager(123);
        $result = $drawRequestManager->getOrCreateDrawRequest();

        $this->assertInstanceOf(\models\composite\oDrawManagement\DrawRequest::class, $result);
    }

    /**
     * Tests that getOrCreateDrawRequest method is idempotent.
     */
    public function testGetOrCreateDrawRequestIsIdempotent(): void
    {
        $drawRequestManager = new DrawRequestManager(123);
        $firstCall = $drawRequestManager->getOrCreateDrawRequest();
        $secondCall = $drawRequestManager->getOrCreateDrawRequest();

        $this->assertSame($firstCall, $secondCall);
    }

    /**
     * Tests saveCategories with empty array.
     */
    public function testSaveCategoriesWithEmptyArray(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        try {
            $result = $drawRequestManager->saveCategories([]);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests saveLineItems with empty array.
     */
    public function testSaveLineItemsWithEmptyArray(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        try {
            $result = $drawRequestManager->saveLineItems([]);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests getDrawRequestDataArray when draw request exists.
     */
    public function testGetDrawRequestDataArrayWhenDrawRequestExists(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        // Force creation of draw request
        $drawRequestManager->getOrCreateDrawRequest();
        $result = $drawRequestManager->getDrawRequestDataArray();

        $this->assertIsArray($result);
    }

    /**
     * Tests hasDrawRequest when draw request exists.
     */
    public function testHasDrawRequestWhenDrawRequestExists(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        // Force creation of draw request
        $drawRequestManager->getOrCreateDrawRequest();
        $result = $drawRequestManager->hasDrawRequest();

        $this->assertTrue($result);
    }

    /**
     * Tests getDrawRequestId when draw request exists.
     */
    public function testGetDrawRequestIdWhenDrawRequestExists(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        try {
            $drawRequestManager->getOrCreateDrawRequest();
            $result = $drawRequestManager->getDrawRequestId();

            $this->assertTrue(is_int($result) || is_null($result));
        } catch (\Exception $e) {
            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests getDrawRequest when draw request exists.
     */
    public function testGetDrawRequestWhenDrawRequestExists(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        // Force creation of draw request
        $drawRequestManager->getOrCreateDrawRequest();
        $result = $drawRequestManager->getDrawRequest();

        $this->assertInstanceOf(\models\composite\oDrawManagement\DrawRequest::class, $result);
    }

    /**
     * Tests saveDrawRequestData with approved status.
     */
    public function testSaveDrawRequestDataWithApprovedStatus(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $postData = [
            'status' => 'approved',
            'lineItems' => [
                1 => [
                    'requestedAmount' => 100.00,
                    'notes' => 'Test notes'
                ]
            ]
        ];

        try {
            $result = $drawRequestManager->saveDrawRequestData($postData);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests saveDrawRequestData with rejected status.
     */
    public function testSaveDrawRequestDataWithRejectedStatus(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $postData = [
            'status' => 'rejected',
            'lineItems' => [
                1 => [
                    'requestedAmount' => 100.00,
                    'rejectReason' => 'Insufficient documentation'
                ]
            ]
        ];

        try {
            $result = $drawRequestManager->saveDrawRequestData($postData);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests pcIdFromLMRId with zero value.
     */
    public function testPcIdFromLMRIdWithZero(): void
    {
        $result = DrawRequestManager::pcIdFromLMRId(0);

        $this->assertNull($result);
    }

    /**
     * Tests pcIdFromLMRId with negative value.
     */
    public function testPcIdFromLMRIdWithNegativeValue(): void
    {
        $result = DrawRequestManager::pcIdFromLMRId(-1);

        $this->assertNull($result);
    }

    /**
     * Tests saveCategories with categories containing special characters.
     */
    public function testSaveCategoriesWithSpecialCharacters(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $categoriesData = [
            [
                'id' => null,
                'categoryName' => 'Category with "quotes" & symbols',
                'description' => 'Description with <html> tags',
                'order' => 1
            ]
        ];

        try {
            $result = $drawRequestManager->saveCategories($categoriesData);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests saveLineItems with line items containing special characters.
     */
    public function testSaveLineItemsWithSpecialCharacters(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $lineItemsData = [
            1 => [
                [
                    'id' => null,
                    'categoryId' => 1,
                    'name' => 'Line Item with "quotes" & symbols',
                    'description' => 'Description with <html> tags',
                    'order' => 1,
                    'cost' => 100.00
                ]
            ]
        ];

        try {
            $result = $drawRequestManager->saveLineItems($lineItemsData);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests saveDrawRequestData with line items containing special characters.
     */
    public function testSaveDrawRequestDataWithSpecialCharacters(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        $postData = [
            'status' => 'pending',
            'lineItems' => [
                1 => [
                    'requestedAmount' => 100.00,
                    'notes' => 'Notes with "quotes" & <html> tags',
                    'lenderNotes' => 'Lender notes with special chars: @#$%',
                    'rejectReason' => 'Reason with émojis 🏠'
                ]
            ]
        ];

        try {
            $result = $drawRequestManager->saveDrawRequestData($postData);
            $this->assertIsBool($result);
        } catch (\TypeError $e) {
            $this->assertStringContainsString('Database2::beginTransaction', $e->getMessage());
        }
    }

    /**
     * Tests that MAX_CATEGORIES constant is reasonable.
     */
    public function testMaxCategoriesIsReasonable(): void
    {
        $this->assertGreaterThan(0, DrawRequestManager::MAX_CATEGORIES);
        $this->assertLessThanOrEqual(100, DrawRequestManager::MAX_CATEGORIES);
    }

    /**
     * Tests getDrawRequestHistory with valid draw request.
     */
    public function testGetDrawRequestHistoryWithValidDrawRequest(): void
    {
        $drawRequestManager = new DrawRequestManager(123);

        // Force creation of draw request
        $drawRequestManager->getOrCreateDrawRequest();
        $result = $drawRequestManager->getDrawRequestHistory();

        $this->assertIsArray($result);
    }

    /**
     * Tests forLoanFile with very large LMRId.
     */
    public function testForLoanFileWithLargeLMRId(): void
    {
        $largeLMRId = PHP_INT_MAX;
        $drawRequestManager = DrawRequestManager::forLoanFile($largeLMRId);

        $this->assertInstanceOf(DrawRequestManager::class, $drawRequestManager);
    }

    /**
     * Tests constructor with very large LMRId.
     */
    public function testConstructorWithLargeLMRId(): void
    {
        $largeLMRId = PHP_INT_MAX;
        $drawRequestManager = new DrawRequestManager($largeLMRId);

        $this->assertInstanceOf(DrawRequestManager::class, $drawRequestManager);
    }
}
