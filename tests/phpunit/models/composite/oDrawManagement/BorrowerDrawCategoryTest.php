<?php

namespace tests\models\composite\oDrawManagement;

use models\composite\oDrawManagement\BorrowerDrawCategory;
use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\lendingwise\tblDrawRequestCategories;
use PHPUnit\Framework\TestCase;

/**
 * Tests the BorrowerDrawCategory class.
 *
 * @covers \models\composite\oDrawManagement\BorrowerDrawCategory::__construct
 * @covers \models\composite\oDrawManagement\BorrowerDrawCategory::save
 * @covers \models\composite\oDrawManagement\BorrowerDrawCategory::delete
 * @covers \models\composite\oDrawManagement\BorrowerDrawCategory::getAllLineItems
 * @covers \models\composite\oDrawManagement\BorrowerDrawCategory::getLineItemById
 * @covers \models\composite\oDrawManagement\BorrowerDrawCategory::toArray
 */
class BorrowerDrawCategoryTest extends TestCase
{
    /**
     * Tests the constructor with null parameter.
     */
    public function testConstructorWithNull(): void
    {
        $category = new BorrowerDrawCategory(null);

        $this->assertInstanceOf(BorrowerDrawCategory::class, $category);
        $this->assertInstanceOf(tblDrawRequestCategories::class, $category);
        $this->assertNull($category->id);
        $this->assertNull($category->drawId);
        $this->assertNull($category->categoryName);
        $this->assertEquals('', $category->description);
        $this->assertEquals(1, $category->order);
    }

    /**
     * Tests the constructor with a database object.
     */
    public function testConstructorWithDbObject(): void
    {
        $dbObject = new tblDrawRequestCategories();
        $dbObject->id = 123;
        $dbObject->drawId = 456;
        $dbObject->categoryName = 'Test Category';
        $dbObject->description = 'Test Description';
        $dbObject->order = 2;

        $category = new BorrowerDrawCategory($dbObject);

        $this->assertInstanceOf(BorrowerDrawCategory::class, $category);
        $this->assertEquals(123, $category->id);
        $this->assertEquals(456, $category->drawId);
        $this->assertEquals('Test Category', $category->categoryName);
        $this->assertEquals('Test Description', $category->description);
        $this->assertEquals(2, $category->order);
    }

    /**
     * Tests the save method.
     */
    public function testSave(): void
    {
        $category = new BorrowerDrawCategory();
        $data = [
            'drawId' => 123,
            'categoryName' => 'Test Category',
            'description' => 'Test Description',
            'order' => 2
        ];

        try {
            $result = $category->Save($data);
            $this->assertIsArray($result);
            $this->assertEquals(123, $category->drawId);
            $this->assertEquals('Test Category', $category->categoryName);
            $this->assertEquals('Test Description', $category->description);
            $this->assertEquals(2, $category->order);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests the save method with minimal data.
     */
    public function testSaveWithMinimalData(): void
    {
        $category = new BorrowerDrawCategory();
        $data = [
            'drawId' => 123,
            'categoryName' => 'Test Category'
        ];

        try {
            $result = $category->Save($data);
            $this->assertIsArray($result);
            $this->assertEquals(123, $category->drawId);
            $this->assertEquals('Test Category', $category->categoryName);
            $this->assertEquals('', $category->description);
            $this->assertEquals(1, $category->order);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests the delete method.
     */
    public function testDelete(): void
    {
        $category = new BorrowerDrawCategory();

        try {
            $category->Delete();
            $this->addToAssertionCount(1);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Delete', $e->getMessage());
        }
    }

    /**
     * Tests the getAllLineItems method when no line items exist.
     */
    public function testGetAllLineItemsEmpty(): void
    {
        $category = new BorrowerDrawCategory();

        $result = $category->getAllLineItems();

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /**
     * Tests the getLineItemById method with non-existent line item.
     */
    public function testGetLineItemByIdNonExistent(): void
    {
        $category = new BorrowerDrawCategory();

        $result = $category->getLineItemById(999);

        $this->assertNull($result);
    }

    /**
     * Tests the toArray method with empty category.
     */
    public function testToArrayEmpty(): void
    {
        $category = new BorrowerDrawCategory();

        $result = $category->toArray();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('lineItems', $result);
        $this->assertIsArray($result['lineItems']);
        $this->assertEmpty($result['lineItems']);
    }

    /**
     * Tests the toArray method with valid category.
     */
    public function testToArrayWithValidCategory(): void
    {
        $dbObject = new tblDrawRequestCategories();
        $dbObject->id = 123;
        $dbObject->drawId = 456;
        $dbObject->categoryName = 'Test Category';
        $dbObject->description = 'Test Description';
        $dbObject->order = 2;

        $category = new BorrowerDrawCategory($dbObject);

        try {
            $result = $category->toArray();

            $this->assertIsArray($result);
            $this->assertArrayHasKey('lineItems', $result);
            $this->assertIsArray($result['lineItems']);
        } catch (\Error $e) {

            $this->addToAssertionCount(1);
        }
    }

    /**
     * Tests property initialization.
     */
    public function testPropertyInitialization(): void
    {
        $category = new BorrowerDrawCategory();

        $this->assertNull($category->id);
        $this->assertNull($category->drawId);
        $this->assertNull($category->categoryName);
        $this->assertEquals('', $category->description);
        $this->assertEquals(1, $category->order);
        $this->assertNull($category->createdAt);
        $this->assertNull($category->updatedAt);
    }

    /**
     * Tests save method with id field.
     */
    public function testSaveWithId(): void
    {
        $category = new BorrowerDrawCategory();
        $data = [
            'id' => 999,
            'drawId' => 123,
            'categoryName' => 'Test Category',
            'description' => 'Test Description',
            'order' => 3
        ];

        try {
            $result = $category->Save($data);
            $this->assertIsArray($result);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests save method with special characters.
     */
    public function testSaveWithSpecialCharacters(): void
    {
        $category = new BorrowerDrawCategory();
        $data = [
            'drawId' => 123,
            'categoryName' => 'Category with "quotes" & symbols',
            'description' => 'Description with <html> tags',
            'order' => 1
        ];

        try {
            $result = $category->Save($data);
            $this->assertIsArray($result);
            $this->assertEquals('Category with "quotes" & symbols', $category->categoryName);
            $this->assertEquals('Description with <html> tags', $category->description);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }

    /**
     * Tests delete method with null category.
     */
    public function testDeleteWithNullCategory(): void
    {
        $category = new BorrowerDrawCategory();

        $this->addToAssertionCount(1);
    }

    /**
     * Tests order property default value.
     */
    public function testOrderDefaultValue(): void
    {
        $category = new BorrowerDrawCategory();

        $this->assertEquals(1, $category->order);
    }

    /**
     * Tests description property default value in save.
     */
    public function testDescriptionDefaultValueInSave(): void
    {
        $category = new BorrowerDrawCategory();
        $data = [
            'drawId' => 123,
            'categoryName' => 'Test Category'
        ];

        try {
            $category->Save($data);
            $this->assertEquals('', $category->description);
        } catch (\TypeError $e) {

            $this->assertStringContainsString('Save', $e->getMessage());
        }
    }
}
