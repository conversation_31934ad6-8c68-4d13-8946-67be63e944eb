CREATE TABLE `tblDrawRequests_h` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `drawId` INT NOT NULL,
    `status` ENUM('pending', 'approved', 'rejected') NOT NULL,
    `submittedAt` D<PERSON><PERSON>IME NOT NULL,
    `approvedAt` <PERSON><PERSON><PERSON><PERSON>E NULL DEFAULT NULL,
    `requestedAmount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `approvedAmount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `wireAmount` DECIMAL(15,2) NULL DEFAULT NULL,
    `wireSentDate` DATE NULL DEFAULT NULL,
    `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX IX_DrawRequests_H_DrawId (drawId),
    CONSTRAINT FK_DrawRequests_H_Draw FOREIGN KEY (drawId)
        REFERENCES tblDrawRequests(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;
