CREATE TABLE `tblDrawRequestLineItems_h` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `recordId` INT NOT NULL,
    `lineItemId` INT NOT NULL,
    `completedAmount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `completedPercent` DECIMAL(5,2) NOT NULL DEFAULT 0.00,
    `requestedAmount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `disbursedAmount` DECIMAL(15,2) NOT NULL DEFAULT 0.00,
    `notes` TEXT NULL,
    `lenderNotes` TEXT NULL,
    `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    INDEX IX_DrawRequestLineItemsHistory_RecordId (recordId),
    INDEX IX_DrawRequestLineItemsHistory_LineItemId (lineItemId),
    CONSTRAINT FK_DrawRequestLineItemsHistory_Record FOREIGN KEY (recordId)
        REFERENCES tblDrawRequests_h(id) ON DELETE CASCADE,
    CONSTRAINT FK_DrawRequestLineItems_H_LineItem FOREIGN KEY (lineItemId)
        REFERENCES tblDrawRequestLineItems(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;
