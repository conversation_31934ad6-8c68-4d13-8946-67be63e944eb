DROP TABLE IF EXISTS `tblDrawRequestLineItems_h`;
DROP TABLE IF EXISTS `tblDrawRequests_h`;
DROP TABLE IF EXISTS `tblDrawTemplateLineItems`;
DROP TABLE IF EXISTS `tblDrawTemplateCategories`;
DROP TABLE IF EXISTS `tblProcessingCompanyDrawTemplateSettings`;
DROP TABLE IF EXISTS `tblPcDrawTemplateSettings`;

DROP TABLE IF EXISTS `tblDrawRequestLineItems`;
DROP TABLE IF EXISTS `tblDrawRequestCategories`;
DROP TABLE IF EXISTS `tblDrawRequests`;

DELIMITER //

CREATE PROCEDURE DropColumnIfExistsSafe(
    IN p_table_name VARCHAR(255),
    IN p_column_name VARCHAR(255)
)
BEGIN
    DECLARE table_exists INT;
    DECLARE column_exists_in_table INT;

    SELECT COUNT(*)
    INTO table_exists
    FROM information_schema.tables
    WHERE table_schema = DATABASE() AND table_name = p_table_name;

    IF table_exists > 0 THEN
        SELECT COUNT(*)
        INTO column_exists_in_table
        FROM information_schema.columns
        WHERE table_schema = DATABASE() AND table_name = p_table_name AND column_name = p_column_name;

        IF column_exists_in_table > 0 THEN
            SET @ddl = CONCAT('ALTER TABLE `', p_table_name, '` DROP COLUMN `', p_column_name, '`');
            PREPARE stmt FROM @ddl;
            EXECUTE stmt;
            DEALLOCATE PREPARE stmt;
        END IF;
    END IF;
END //

DELIMITER ;

CALL DropColumnIfExistsSafe('tblAdminUsers', 'allowToManageDraws');
CALL DropColumnIfExistsSafe('tblAgent', 'allowToManageDraws');
CALL DropColumnIfExistsSafe('tblBranch', 'allowToManageDraws');
CALL DropColumnIfExistsSafe('tblProcessingCompany', 'drawManagement');
CALL DropColumnIfExistsSafe('tblProcessingCompany', 'enableDrawManagementV2');

DROP PROCEDURE DropColumnIfExistsSafe;
