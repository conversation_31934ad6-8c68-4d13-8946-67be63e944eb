CREATE TABLE `tblDrawTemplateLineItems` (
    `id` INT AUTO_INCREMENT PRIMARY KEY,
    `templateId` INT NOT NULL,
    `categoryId` INT NOT NULL,
    `name` VARCHAR(255) NOT NULL,
    `order` INT UNSIGNED NOT NULL DEFAULT 1,
    `description` TEXT NULL,
    `createdAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updatedAt` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX IX_tblDrawTemplateLineItems_PcDrawTemplateID_LineItemOrder (templateId, `order`),
    INDEX IX_tblDrawTemplateLineItems_DrawTemplateCategoryID_LineItemOrder (categoryId, `order`),
    CONSTRAINT FK_tblDrawTemplateLineItems_DrawTemplateCategory FOREIGN KEY (categoryId)
        REFERENCES tblDrawTemplateCategories(id) ON DELETE CASCADE,
    CONSTRAINT FK_tblDrawTemplateLineItems_PcDrawTemplateSettings FOREIGN KEY (templateId)
        REFERENCES tblProcessingCompanyDrawTemplateSettings(id) ON DELETE CASCADE
) ENGINE = InnoDB DEFAULT CHARSET = utf8mb4 COLLATE = utf8mb4_unicode_520_ci;
