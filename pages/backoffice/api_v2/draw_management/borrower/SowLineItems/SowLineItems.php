<?php

namespace pages\backoffice\api_v2\draw_management\borrower\SowLineItems;

use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;
use models\composite\oDrawManagement\dto\request\SaveLineItemsRequest;

/**
 * Class SowLineItems
 *
 * API endpoint for updating and fetching SOW line items
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class SowLineItems extends DrawManagementApiBase
{
    /**
     * Handle GET requests to fetch SOW line items
     *
     * @return void
     */
    public static function Get(): void
    {
        parent::Init();

        $LMRId = static::getLMRId($_GET['lmrid'] ?? null);

        static::handleGetRequest(
            $LMRId,
            function($LMRId) {
                // Validate loan file exists
                static::validateLoanFile($LMRId);

                // Get draw request manager and fetch data
                $drawRequestManager = static::getDrawRequestManager($LMRId);
                return $drawRequestManager->getDrawRequestDataArray();
            },
            'Line items fetched successfully',
            'No line items found'
        );
    }

    /**
     * Handle POST requests to update SOW line items
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = static::parseJsonInput();
        if (!$postData) return; // parseJsonInput handles error response

        // Process lmrid before creating DTO to ensure it's a proper integer
        if (isset($postData['lmrid'])) {
            $postData['lmrid'] = static::getLMRId($postData['lmrid']);
        }

        static::handlePostRequest(
            SaveLineItemsRequest::class,
            $postData,
            function($dto) {
                $LMRId = $dto->lmrid;

                // Validate loan file exists
                static::validateLoanFile($LMRId);

                // Get draw request manager
                $drawRequestManager = static::getDrawRequestManager($LMRId);

                // Convert DTO to array format for existing manager
                $lineItemsData = [];
                foreach ($dto->lineItems as $categoryId => $categoryLineItems) {
                    $lineItemsData[$categoryId] = [];
                    foreach ($categoryLineItems as $lineItem) {
                        $lineItemsData[$categoryId][] = $lineItem->toArray();
                    }
                }

                // Save line items
                $success = $drawRequestManager->saveLineItems($lineItemsData, $dto->isDraft);

                if (!$success) {
                    return ['success' => false, 'data' => null];
                }

                // Get updated data
                $updatedData = $drawRequestManager->getDrawRequestDataArray();
                return ['success' => true, 'data' => $updatedData];
            },
            'Line items saved successfully',
            'Failed to save line items'
        );
    }
}
