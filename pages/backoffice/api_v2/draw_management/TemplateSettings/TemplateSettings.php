<?php
namespace pages\backoffice\api_v2\draw_management\TemplateSettings;

use models\portals\BackofficePage;
use models\standard\HTTP;
use models\composite\oDrawManagement\SowTemplate;
use models\lendingwise\tblProcessingCompanyDrawTemplateSettings;
use models\lendingwise\db\tblProcessingCompanyDrawTemplateSettings_db;
use models\cypher;
use models\Request;

class TemplateSettings extends BackofficePage
{
    public static function Post()
    {
        try {
            parent::Init();

            $pcId = Request::GetClean('pcid');
            if ($pcId && !is_numeric($pcId)) $pcId = cypher::decrypt($pcId);
            $pcId = (int)$pcId;

            $settingsData = [
                'allowBorrowersAddEditCategories' => Request::GetClean('allowBorrowersAddEditCategories'),
                'allowBorrowersDeleteCategories' => Request::GetClean('allowBorrowersDeleteCategories'),
                'allowBorrowersAddEditLineItems' => Request::GetClean('allowBorrowersAddEditLineItems'),
                'allowBorrowersDeleteLineItems' => Request::GetClean('allowBorrowersDeleteLineItems'),
                'allowBorrowersSOWRevisions' => Request::GetClean('allowBorrowersSOWRevisions'),
                'allowBorrowersExceedFinancedRehabCostOnRevision' => Request::GetClean('allowBorrowersExceedFinancedRehabCostOnRevision'),
                'drawFee' => Request::GetClean('drawFee')
            ];

            $sowTemplate = SowTemplate::Get([tblProcessingCompanyDrawTemplateSettings_db::COLUMN_PCID => $pcId]);
            $sowTemplate->save($settingsData);

            // Return JSON response
            HTTP::ExitJSON(["success" => true, "message" => "Settings saved successfully."]);
        } catch (\Exception $e) {
            HTTP::ExitJSON(["success" => false, "message" => "An error occurred: " . $e->getMessage()]);
        }
    }
}
