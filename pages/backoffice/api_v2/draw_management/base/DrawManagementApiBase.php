<?php

namespace pages\backoffice\api_v2\draw_management\base;

use models\portals\BackofficePage;
use models\standard\HTTP;
use models\cypher;
use models\composite\oDrawManagement\dto\response\ApiResponse;
use models\composite\oDrawManagement\validation\PropertyValidator;
use models\composite\oDrawManagement\dto\base\ValidatableDTO;
use models\lendingwise\tblFile;
use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\SowTemplateManager;

/**
 * Base class for Draw Management API endpoints
 *
 * Provides common functionality for all draw management API classes including:
 * - ID validation and decryption
 * - Loan file validation
 * - Standardized error handling
 * - DTO validation patterns
 * - Response formatting
 */
abstract class DrawManagementApiBase extends BackofficePage
{
    /**
     * Parse JSON input from request body
     *
     * @return array|null Parsed JSON data or null on failure
     */
    protected static function parseJsonInput(): ?array
    {
        $postData = file_get_contents("php://input");
        $decoded = json_decode($postData, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $response = ApiResponse::error("Invalid JSON input: " . json_last_error_msg());
            HTTP::ExitJSON($response->toArray());
            return null;
        }

        return $decoded;
    }

    /**
     * Get and validate LMRId from request data
     *
     * @param mixed $LMRId Raw LMRId value
     * @return int Validated LMRId
     */
    protected static function getLMRId($LMRId): int
    {
        if ($LMRId && !is_numeric($LMRId)) {
            $LMRId = cypher::myDecryption($LMRId);
        }
        $LMRId = (int)$LMRId;

        if (!$LMRId) {
            $response = ApiResponse::error("LMRId is required.");
            HTTP::ExitJSON($response->toArray());
        }
        return $LMRId;
    }

    /**
     * Get and validate PCId from request data
     *
     * @param mixed $PCId Raw PCId value
     * @return int Validated PCId
     */
    protected static function getPCId($PCId): int
    {
        if ($PCId && !is_numeric($PCId)) {
            $PCId = cypher::decrypt($PCId);
        }
        $PCId = (int)$PCId;

        if (!$PCId) {
            $response = ApiResponse::error("PCId is required.");
            HTTP::ExitJSON($response->toArray());
            return 0; // This won't be reached due to HTTP::ExitJSON
        }
        return $PCId;
    }

    /**
     * Validate that a loan file exists
     *
     * @param int $LMRId Loan file ID
     * @return object|null Loan file object or null if not found
     */
    protected static function validateLoanFile(int $LMRId): ?object
    {
        $loanFile = tblFile::Get(['LMRId' => $LMRId]);
        if (!$loanFile) {
            $response = ApiResponse::error("Loan file with ID {$LMRId} not found.");
            HTTP::ExitJSON($response->toArray());
            return null;
        }
        return $loanFile;
    }

    /**
     * Create and validate a DTO from request data
     *
     * @param string $dtoClass DTO class name
     * @param array $data Request data
     * @param bool $validate Whether to run validation
     * @return ValidatableDTO Validated DTO instance
     */
    protected static function createAndValidateDTO(string $dtoClass, array $data, bool $validate = true): ValidatableDTO
    {
        try {
            // Create DTO from array
            $dto = $dtoClass::fromArray($data, $validate);

            if ($validate) {
                // Additional validation using PropertyValidator
                $validator = new PropertyValidator();
                if (!$validator->validate($dto)) {
                    $response = ApiResponse::validationError($validator->getErrors());
                    HTTP::ExitJSON($response->toArray());
                }
            }

            return $dto;
        } catch (\InvalidArgumentException $e) {
            $response = ApiResponse::validationError([], $e->getMessage());
            HTTP::ExitJSON($response->toArray());
        }
    }

    /**
     * Execute business rule validation and handle errors
     *
     * @param callable $validationCallback Callback that returns array of errors
     * @return void
     */
    protected static function validateBusinessRules(callable $validationCallback): void
    {
        $validationErrors = $validationCallback();

        if (!empty($validationErrors)) {
            $response = ApiResponse::validationError($validationErrors);
            HTTP::ExitJSON($response->toArray());
        }
    }

    /**
     * Get DrawRequestManager instance for a loan file
     *
     * @param int $LMRId Loan file ID
     * @return DrawRequestManager
     */
    protected static function getDrawRequestManager(int $LMRId): DrawRequestManager
    {
        return DrawRequestManager::forLoanFile($LMRId);
    }

    /**
     * Get SowTemplateManager instance for a processing company
     *
     * @param int $PCId Processing company ID
     * @return SowTemplateManager
     */
    protected static function getSowTemplateManager(int $PCId): SowTemplateManager
    {
        return SowTemplateManager::forProcessingCompany($PCId);
    }

    /**
     * Send success response with data
     *
     * @param mixed $data Response data
     * @param string $message Success message
     * @return void
     */
    protected static function sendSuccessResponse($data = null, string $message = 'Operation completed successfully'): void
    {
        $response = ApiResponse::success($data, $message);
        HTTP::ExitJSON($response->toArray());
    }

    /**
     * Send error response
     *
     * @param string $message Error message
     * @param array $errors Detailed errors
     * @param mixed $data Optional data
     * @return void
     */
    protected static function sendErrorResponse(string $message, array $errors = [], $data = null): void
    {
        $response = ApiResponse::error($message, $errors, $data);
        HTTP::ExitJSON($response->toArray());
    }

    /**
     * Send validation error response
     *
     * @param array $validationErrors Validation errors
     * @param string $message Error message
     * @return void
     */
    protected static function sendValidationErrorResponse(array $validationErrors, string $message = 'Validation failed'): void
    {
        $response = ApiResponse::validationError($validationErrors, $message);
        HTTP::ExitJSON($response->toArray());
    }

    /**
     * Execute operation with standardized error handling
     *
     * @param callable $operation Operation to execute
     * @param string $errorMessage Default error message
     * @return void
     */
    protected static function executeWithErrorHandling(callable $operation, string $errorMessage = 'An error occurred'): void
    {
        try {
            $operation();
        } catch (\InvalidArgumentException $e) {
            static::sendValidationErrorResponse([], $e->getMessage());
        } catch (\Exception $e) {
            static::sendErrorResponse($errorMessage . ': ' . $e->getMessage());
        }
    }

    /**
     * Validate and process request data with DTO
     *
     * @param string $dtoClass DTO class name
     * @param array $requestData Request data
     * @param callable|null $businessValidation Optional business validation callback
     * @return ValidatableDTO Validated DTO
     */
    protected static function processRequestWithDTO(
        string $dtoClass,
        array $requestData,
        ?callable $businessValidation = null
    ): ValidatableDTO {
        // Create and validate DTO
        $dto = static::createAndValidateDTO($dtoClass, $requestData, true);

        // Run business validation if provided
        if ($businessValidation) {
            static::validateBusinessRules($businessValidation);
        }

        return $dto;
    }

    /**
     * Convert DTO to array format for manager classes
     *
     * @param ValidatableDTO $dto DTO to convert
     * @param bool $includeNulls Whether to include null values
     * @return array Converted array
     */
    protected static function dtoToArray(ValidatableDTO $dto, bool $includeNulls = true): array
    {
        return $dto->toArray($includeNulls);
    }

    /**
     * Handle common GET request pattern for fetching data
     *
     * @param int $identifier ID (LMRId or PCId)
     * @param callable $dataFetcher Callback to fetch data
     * @param string $successMessage Success message
     * @param string $emptyMessage Message when no data found
     * @return void
     */
    protected static function handleGetRequest(
        int $identifier,
        callable $dataFetcher,
        string $successMessage = 'Data fetched successfully',
        string $emptyMessage = 'No data found'
    ): void {
        static::executeWithErrorHandling(function() use ($identifier, $dataFetcher, $successMessage, $emptyMessage) {
            $data = $dataFetcher($identifier);

            if (empty($data)) {
                static::sendSuccessResponse([], $emptyMessage);
                return;
            }

            static::sendSuccessResponse($data, $successMessage);
        });
    }

    /**
     * Handle common POST request pattern for saving data
     *
     * @param string $dtoClass DTO class name
     * @param array $requestData Request data
     * @param callable $dataSaver Callback to save data
     * @param callable|null $businessValidation Optional business validation
     * @param string $successMessage Success message
     * @param string $errorMessage Error message
     * @return void
     */
    protected static function handlePostRequest(
        string $dtoClass,
        array $requestData,
        callable $dataSaver,
        string $successMessage = 'Data saved successfully',
        string $errorMessage = 'Failed to save data'
    ): void {
        static::executeWithErrorHandling(function() use (
            $dtoClass, $requestData, $dataSaver, $successMessage, $errorMessage
        ) {
            // Process request with DTO validation
            $dto = static::processRequestWithDTO($dtoClass, $requestData);

            // Save data
            $result = $dataSaver($dto);

            if (!$result['success']) {
                static::sendErrorResponse($errorMessage);
                return;
            }

            static::sendSuccessResponse($result['data'], $successMessage);
        });
    }
}
