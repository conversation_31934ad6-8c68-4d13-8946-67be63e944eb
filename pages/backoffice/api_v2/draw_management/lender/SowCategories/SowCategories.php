<?php

namespace pages\backoffice\api_v2\draw_management\lender\SowCategories;

use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;
use models\composite\oDrawManagement\dto\request\SaveCategoriesRequest;

/**
 * Class SowCategories
 *
 * API endpoint for updating and fetching SOW categories
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class SowCategories extends DrawManagementApiBase
{
    /**
     * Handle GET requests to fetch SOW categories
     *
     * @return void
     */
    public static function Get(): void
    {
        parent::Init();

        $PCId = static::getPCId($_GET['pcid']);

        static::handleGetRequest(
            $PCId,
            function($PCId) {
                // Get SOW template manager and fetch data
                $sowTemplateManager = static::getSowTemplateManager($PCId);
                return $sowTemplateManager->getTemplateDataArray();
            },
            'Categories fetched successfully',
            'No categories found'
        );
    }

    /**
     * Handle POST requests to update SOW categories
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = static::parseJsonInput();
        if (!$postData) return; // parseJsonInput handles error response

        // Process pcid before creating DTO to ensure it's a proper integer
        if (isset($postData['pcid'])) {
            $postData['pcid'] = static::getPCId($postData['pcid']);
        }

        static::handlePostRequest(
            SaveCategoriesRequest::class,
            $postData,
            function($dto) {
                $PCId = $dto->pcid; // Already processed by getPCId above

                // Get SOW template manager
                $sowTemplateManager = static::getSowTemplateManager($PCId);

                // Convert DTO categories to array format for existing manager
                $categoriesData = [];
                foreach ($dto->categories as $category) {
                    $categoriesData[] = $category->toArray();
                }

                // Save data
                $result = $sowTemplateManager->saveCategories($categoriesData);

                if (!$result) {
                    return ['success' => false, 'data' => null];
                }

                // Get updated data
                $updatedData = $sowTemplateManager->getTemplateDataArray();
                return ['success' => true, 'data' => $updatedData];
            },
            'Categories saved successfully',
            'Failed to save categories'
        );
    }
}
