<?php

namespace pages\backoffice\api_v2\draw_management\lender\SowLineItems;

use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;
use models\composite\oDrawManagement\dto\request\SaveLineItemsRequest;

/**
 * Class SowLineItems
 *
 * API endpoint for updating and fetching SOW line items
 *
 * @package pages\backoffice\api_v2\draw_management
 */
class SowLineItems extends DrawManagementApiBase
{
    /**
     * Handle GET requests to fetch SOW line items
     *
     * @return void
     */
    public static function Get(): void
    {
        parent::Init();

        $PCId = static::getPCId($_GET['pcid'] ?? null);

        static::handleGetRequest(
            $PCId,
            function($PCId) {
                // Get SOW template manager and fetch data
                $sowTemplateManager = static::getSowTemplateManager($PCId);
                return $sowTemplateManager->getTemplateDataArray();
            },
            'Line items fetched successfully',
            'No line items found'
        );
    }

    /**
     * Handle POST requests to update SOW line items
     *
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        $postData = static::parseJsonInput();
        if (!$postData) return; // parseJsonInput handles error response

        // Process pcid before creating DTO to ensure it's a proper integer
        if (isset($postData['pcid'])) {
            $postData['pcid'] = static::getPCId($postData['pcid']);
        }

        static::handlePostRequest(
            SaveLineItemsRequest::class,
            $postData,
            function($dto) {
                $PCId = $dto->pcid;

                // Get SOW template manager
                $sowTemplateManager = static::getSowTemplateManager($PCId);

                // Convert DTO to array format for existing manager
                $lineItemsData = [];
                foreach ($dto->lineItems as $categoryId => $categoryLineItems) {
                    $lineItemsData[$categoryId] = [];
                    foreach ($categoryLineItems as $lineItem) {
                        $lineItemsData[$categoryId][] = $lineItem->toArray();
                    }
                }

                // Save line items
                $success = $sowTemplateManager->saveLineItems($lineItemsData);

                if (!$success) {
                    return ['success' => false, 'data' => null];
                }

                // Get updated data
                $updatedData = $sowTemplateManager->getTemplateDataArray();
                return ['success' => true, 'data' => $updatedData];
            },
            'Line items saved successfully',
            'Failed to save line items'
        );
    }
}
