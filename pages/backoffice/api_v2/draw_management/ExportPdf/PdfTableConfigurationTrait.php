<?php

namespace pages\backoffice\api_v2\draw_management\ExportPdf;

/**
 * Trait PdfTableConfigurationTrait
 *
 * Contains table configuration definitions for PDF export functionality
 * Separates configuration concerns from the main ExportPdf class
 *
 * @package pages\backoffice\api_v2\draw_management\ExportPdf
 */
trait PdfTableConfigurationTrait
{
    /**
     * Get table configuration based on table type
     *
     * @param string $tableType The type of table: 'draw_request', 'scope_of_work', or 'history'
     * @return array Table configuration array with headers, data_fields, and formatting options
     */
    private static function getTableConfiguration(string $tableType): array
    {
        $configurations = [
            'draw_request' => self::getDrawRequestTableConfig(),
            'scope_of_work' => self::getScopeOfWorkTableConfig(),
            'history' => self::getHistoryTableConfig()
        ];

        return $configurations[$tableType] ?? $configurations['draw_request'];
    }

    /**
     * Get draw request table configuration
     *
     * @return array Configuration for draw request table (8 columns)
     */
    private static function getDrawRequestTableConfig(): array
    {
        return [
            'headers' => [
                'Line Item' => ['width' => 40, 'align' => 'L'],
                'Total Budget' => ['width' => 25, 'align' => 'R', 'format' => 'currency'],
                'Completed Renovations' => ['width' => 30, 'align' => 'R', 'format' => 'currency'],
                'Prv. Disbursed' => ['width' => 25, 'align' => 'R', 'format' => 'currency'],
                '% Completed' => ['width' => 20, 'align' => 'C', 'format' => 'percentage'],
                'Requested This Draw' => ['width' => 35, 'align' => 'C', 'format' => 'percentage_with_currency'],
                'Borrower Notes' => ['width' => 44.5, 'align' => 'L', 'format' => 'truncate', 'length' => 50, 'font_size' => 8, 'min_height' => 15],
                'Lender Notes' => ['width' => 42.5, 'align' => 'L', 'format' => 'truncate', 'length' => 50, 'font_size' => 8, 'min_height' => 15]
            ],
            'data_fields' => [
                'Line Item' => 'name',
                'Total Budget' => 'cost',
                'Completed Renovations' => 'completedAmount',
                'Prv. Disbursed' => 'disbursedAmount',
                '% Completed' => 'completedPercent',
                'Requested This Draw' => ['requestedAmount', 'cost'],
                'Borrower Notes' => 'notes',
                'Lender Notes' => 'lenderNotes'
            ],
            'header_height' => 12,
            'use_multicell_headers' => true
        ];
    }

    /**
     * Get scope of work table configuration
     *
     * @return array Configuration for scope of work table (7 columns)
     */
    private static function getScopeOfWorkTableConfig(): array
    {
        return [
            'headers' => [
                'Line Item' => ['width' => 50, 'align' => 'L'],
                'Total Budget' => ['width' => 35, 'align' => 'R', 'format' => 'currency'],
                'Completed Renovations' => ['width' => 40, 'align' => 'R', 'format' => 'currency'],
                '% Completed' => ['width' => 25, 'align' => 'C', 'format' => 'percentage'],
                'Borrower Notes' => ['width' => 50, 'align' => 'L', 'format' => 'truncate', 'length' => 60, 'font_size' => 8, 'min_height' => 15],
                'Lender Notes' => ['width' => 47, 'align' => 'L', 'format' => 'truncate', 'length' => 60, 'font_size' => 8, 'min_height' => 15]
            ],
            'data_fields' => [
                'Line Item' => 'name',
                'Total Budget' => 'cost',
                'Completed Renovations' => 'completedAmount',
                '% Completed' => 'completedPercent',
                'Borrower Notes' => 'notes',
                'Lender Notes' => 'lenderNotes'
            ],
            'header_height' => 12,
            'use_multicell_headers' => true
        ];
    }

    /**
     * Get history table configuration
     *
     * @return array Configuration for history table (8 columns) with totals
     */
    private static function getHistoryTableConfig(): array
    {
        return [
            'headers' => [
                'Line Item' => ['width' => 35, 'align' => 'L'],
                'Total Budget' => ['width' => 30, 'align' => 'R', 'format' => 'currency'],
                'Completed Renovations' => ['width' => 35, 'align' => 'R', 'format' => 'currency'],
                'Disbursed Amount' => ['width' => 32, 'align' => 'R', 'format' => 'currency'],
                '% Completed' => ['width' => 22, 'align' => 'C', 'format' => 'percentage'],
                'Requested Amount' => ['width' => 35, 'align' => 'R', 'format' => 'currency'],
                'Borrower Notes' => ['width' => 40, 'align' => 'L', 'format' => 'truncate', 'length' => 50, 'font_size' => 8, 'min_height' => 15],
                'Lender Notes' => ['width' => 38, 'align' => 'L', 'format' => 'truncate', 'length' => 50, 'font_size' => 8, 'min_height' => 15]
            ],
            'data_fields' => [
                'Line Item' => 'name',
                'Total Budget' => 'cost',
                'Completed Renovations' => 'completedAmount',
                'Disbursed Amount' => 'disbursedAmount',
                '% Completed' => 'completedPercent',
                'Requested Amount' => 'requestedAmount',
                'Borrower Notes' => 'notes',
                'Lender Notes' => 'lenderNotes'
            ],
            'header_height' => 12,
            'use_multicell_headers' => true,
            'show_totals' => true
        ];
    }

    /**
     * Get available table types
     *
     * @return array List of available table configuration types
     */
    private static function getAvailableTableTypes(): array
    {
        return ['draw_request', 'scope_of_work', 'history'];
    }

    /**
     * Validate table configuration
     *
     * @param array $config Table configuration to validate
     * @return bool True if configuration is valid
     */
    private static function validateTableConfiguration(array $config): bool
    {
        $requiredKeys = ['headers', 'data_fields', 'header_height'];

        foreach ($requiredKeys as $key) {
            if (!isset($config[$key])) {
                return false;
            }
        }

        // Validate headers structure
        if (!is_array($config['headers']) || empty($config['headers'])) {
            return false;
        }

        foreach ($config['headers'] as $header => $headerConfig) {
            if (!is_array($headerConfig) || !isset($headerConfig['width'], $headerConfig['align'])) {
                return false;
            }
        }

        // Validate data_fields structure
        if (!is_array($config['data_fields']) || empty($config['data_fields'])) {
            return false;
        }

        // Ensure data_fields keys match headers keys
        if (array_keys($config['headers']) !== array_keys($config['data_fields'])) {
            return false;
        }

        return true;
    }
}
