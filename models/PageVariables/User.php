<?php

namespace models\PageVariables;

use models\composite\oBranch\getBranchesForAgent;
use models\constants\accessRestrictionPC;
use models\constants\gl\glRAMAccessPC;
use models\constants\gl\glUserGroup;
use models\constants\gl\glUserRole;
use models\lendingwise\db\tblAdminUsers_db;
use models\lendingwise\db\tblAgent_db;
use models\lendingwise\db\tblBranch_db;
use models\lendingwise\db\tblBranchLMRClientType_db;
use models\lendingwise\db\tblBranchModules_db;
use models\lendingwise\db\tblClient_db;
use models\lendingwise\tblAdminUsers;
use models\lendingwise\tblAgent;
use models\lendingwise\tblBranch;
use models\lendingwise\tblBranchLMRClientType;
use models\lendingwise\tblBranchModules;
use models\lendingwise\tblClient;
use models\types\strongType;

class User extends strongType
{
    public ?int $allowToSeeAlowareTab = null;
    public ?int $allowToEditMyFile = null;
    public ?int $allowToUpdateFileAdminSection = null;
    public ?int $allowToSeeBillingSectionForFile = null;
    public ?int $userSeeBilling = null;
    public ?int $allowToAccessDocs = null;
    public ?int $allowToDeleteUploadedDocs = null;
    public ?int $allowToCreateFiles = null;
    public ?int $allowToCreateTasks = null;
    public ?int $permissionToREST = null;
    public ?int $allowExcelDownload = null;
    public ?int $viewPrivateNotes = null;
    public ?int $allowToEditOwnNotes = null;
    public ?int $changeDIYPlan = null;
    public ?int $allowToEditCommission = null;
    public ?int $allowToSeeCommission = null;
    public ?int $PCAllowToCreateBranch = null;
    public ?int $allowToViewAllFiles = null;
    public ?int $allowEmailCampaign = null;
    public ?int $allowedToSendHomeownerLink = null;
    public ?int $allowToLASubmitForPC = null;
    public ?int $allowToLASubmission = null;
    public ?int $subscribeToHOME = null;
    public ?int $subscribePCToHOME = null;
    public ?int $allowToCFPBSubmitForPC = null;
    public ?int $allowToCFPBSubmission = null;
    public ?int $allowPCUserToSubmitCFPB = null;
    public ?int $allowToViewCFPBPipeline = null;
    public ?int $allowToAccessAdminReqDocs = null;
    public ?int $allowToViewCreditScreening = null;
    public ?int $isPCAllowEmailCampaign = null;
    public ?int $allowToSelectFromEmail = null;
    public ?int $allowToSendFax = null;
    public ?int $allowToAccessRAM = null;
    public ?int $thirdPartyServices = null;
    public ?int $glThirdPartyServices = null;
    public ?int $allowPeerstreet = null;
    public ?int $viewSubmitOfferTab = null;
    public ?int $allowDashboard = null;
    public ?int $shareThisFile = null;
    public ?string $userTimeZone = null;
    public ?string $allowToLockLoanFile = null;
    public ?string $allowToViewMarketPlace = null;
    public ?string $clientSelectRefferalCode = null;
    public ?string $allowedToSendFileDesignation = null;
    public ?string $viewPublicNotes = null;
    public ?string $allowUserToAccessRAM = null;
    public ?string $allowAgentWorkflowEdit = null;
    public ?string $allowToupdateFileAndClient = null;
    public ?string $acqualifyOptStatus = null;
    public ?string $avatar = null;
    public ?string $externalBroker = null;
    public ?string $secondaryBrokerNumber = null;
    public ?string $userPriceEngineStatus = null;
    public ?string $allowToAccessInternalLoanProgram = null;
    public ?string $allowToCopyFile = null;
    public ?string $allowToSeeAllBrokers = null;
    public ?int $allowToMassUpdate = null;
    public ?string $allowToOnOffAgentLogin = null;
    public ?string $allowBranchToEditAgentProfile = null;
    public ?string $AEUserType = null;
    public ?string $allowBranchManagerToLogin = null;
    public ?string $allowBranchWorkflowEdit = null;
    public ?string $allowBranchToAddAgent;
    public ?array $myServicesArray = null;
    public ?array $myModulesArray = null;
    public ?string $allowToChangeOrAssignBranchForFile = null;
    public ?string $allowEmpToCreateBranch = null;
    public ?string $convertNewBRIntoEmpOwnBR = null;
    public ?string $allowEmpToCreateAgent = null;
    public ?string $allowEmpToSeeAgent = null;
    public ?string $allowToViewContactsList = null;
    public ?string $allowUserToSendMsgToBorrower = null;
    public ?string $allowEmpToCopyFile = null;
    public ?string $allowToSeeFeesInDashboard = null;
    public ?string $loggedInEmail = null;
    public ?int $manualMERS = null;
    public ?int $allowToEditLoanStage = null;

    public ?tblClient $tblClient = null;
    public ?tblAgent $tblAgent = null;
    public ?tblBranch $tblBranch = null;
    public ?tblAdminUsers $tblAdminUser = null;

    public ?array $agentBranchArray = null;
    public ?int $thirdPartyServicesLegalDocs = null;
    public ?int $allowToViewAutomationPopup = null;
    public ?int $allowToControlAutomationPopup = null;
    public ?int $allowToManageDraws = null;

    public static function Init(
        int    &$PCID,
        string $userGroup,
        string $userRole,
        int    $userNumber,
        ?int   $publicUser
    ): self
    {
        $glRAMAccessPC = glRAMAccessPC::$glRAMAccessPC;
        $accessRestrictionPC = accessRestrictionPC::$accessRestrictionPC;

        $res = new self();

        $res->allowToSeeBillingSectionForFile = 0;
        if ($userGroup == glUserGroup::USER_GROUP_SUPER || $userRole == glUserRole::USER_ROLE_REST) {

            $res->tblAdminUser = tblAdminUsers::Get([
                tblAdminUsers_db::COLUMN_AID => $userNumber,
            ]);

            $PCID = $res->tblAdminUser->processingCompanyId;

            $res->allowToSeeAlowareTab = 1;
            $res->allowToEditMyFile = 1;
            $res->allowToUpdateFileAdminSection = 1;
            $res->userSeeBilling = 1;
            $res->allowToAccessDocs = 1;
            $res->allowToDeleteUploadedDocs = 1;
            $res->allowToCreateFiles = 1;
            $res->allowToCreateTasks = 1;
            $res->permissionToREST = 1;
            $res->allowExcelDownload = 1;
            $res->viewPrivateNotes = 1;
            $res->viewPublicNotes = 1;
            $res->allowToEditOwnNotes = 1;
            $res->changeDIYPlan = 1;
            $res->allowToEditCommission = 1;
            $res->allowToSeeCommission = 1;
            $res->PCAllowToCreateBranch = 1;
            $res->allowToViewAllFiles = 1;
            $res->allowEmailCampaign = 0;
            $res->allowedToSendHomeownerLink = 1;
            $res->allowToLASubmitForPC = 1;
            $res->allowToLASubmission = 1;
            $res->subscribeToHOME = 1;
            $res->subscribePCToHOME = 1;
            $res->allowToCFPBSubmitForPC = 1;
            $res->allowToCFPBSubmission = 1;
            $res->allowPCUserToSubmitCFPB = 1;
            $res->allowToViewCFPBPipeline = 1;
            $res->allowToAccessAdminReqDocs = 1;
            $res->allowToViewCreditScreening = 1;
            $res->isPCAllowEmailCampaign = 1;
            $res->allowToSelectFromEmail = 1;
            $res->allowToEditLoanStage = 1;

            if ($userGroup == glUserGroup::USER_GROUP_SUPER) {
                $res->allowToSendFax = 1;
                $res->allowToAccessRAM = 1;
                $res->thirdPartyServices = 1;
                $res->glThirdPartyServices = 1;
                $res->allowPeerstreet = 1;
                $res->viewSubmitOfferTab = 1;
                $res->allowToManageDraws = 1;
            }

            if ($userRole == glUserRole::USER_ROLE_REST) {
                $res->allowDashboard = 0;
            } else {
                $res->allowDashboard = 1;
            }
            $res->allowToViewAutomationPopup = $res->tblAdminUser->allowToViewAutomationPopup;
            $res->allowToControlAutomationPopup = $res->tblAdminUser->allowToControlAutomationPopup;
        } elseif ($userGroup == glUserGroup::USER_GROUP_SALES) {
            $res->allowDashboard = 1;
        } elseif ($userGroup == glUserGroup::USER_GROUP_CLIENT) {
            $res->allowToCreateTasks = 1;
            $res->viewPrivateNotes = 0;

            $res->tblClient = tblClient::Get([
                tblClient_db::COLUMN_ACTIVESTATUS => 1,
                tblClient_db::COLUMN_CID          => $userNumber,
            ]);

            $res->loggedInEmail = $res->tblClient->clientEmail;

            $res->allowToAccessDocs = 0;
            $res->shareThisFile = 0; //NEVER show Share This File
            if ($res->tblClient) {
                $res->userTimeZone = trim($res->tblClient->timeZone);
                $res->allowToLockLoanFile = trim($res->tblClient->allowToLockLoanFileClient);
                $res->allowToViewMarketPlace = trim($res->tblClient->allowToViewMarketPlace);
                $res->viewSubmitOfferTab = trim($res->tblClient->allowToSubmitOffer);
                $res->clientSelectRefferalCode = trim($res->tblClient->referralCode);

            }

        } elseif ($userGroup == glUserGroup::USER_GROUP_AGENT && $userNumber > 0) {

            $res->tblAgent = tblAgent::Get([
                tblAgent_db::COLUMN_STATUS     => 1,
                tblAgent_db::COLUMN_USERNUMBER => $userNumber,
            ]);

            if ($res->tblAgent) {
                $res->loggedInEmail = $res->tblAgent->email;

                $res->allowToSeeAlowareTab = trim($res->tblAgent->allowToCreateAloware);
                $res->allowToEditMyFile = trim($res->tblAgent->allowAgentToEditLMRFile);
                $res->allowToUpdateFileAdminSection = trim($res->tblAgent->allowedToUpdateFiles);
                $res->userSeeBilling = trim($res->tblAgent->seeBilling);
                $res->allowToDeleteUploadedDocs = trim($res->tblAgent->allowedToDeleteUplodedDocs);
                $res->allowDashboard = trim($res->tblAgent->allowAgentToSeeDashboard);
                $res->allowToCreateFiles = trim($res->tblAgent->allowAgentToCreateFiles);
                $res->allowToCreateTasks = trim($res->tblAgent->allowAgentToCreateTasks);
                $res->changeDIYPlan = trim($res->tblAgent->changeDIYPlan);
                $res->allowToAccessDocs = trim($res->tblAgent->allowAgentToAccessLMRDocs);
                $res->allowedToSendHomeownerLink = trim($res->tblAgent->allowAgentToSendHomeownerLink);
                $res->viewPrivateNotes = trim($res->tblAgent->allowToAccessPrivateNotes);
                $res->allowToEditOwnNotes = trim($res->tblAgent->allowedToEditOwnNotes);
                $res->permissionToREST = trim($res->tblAgent->permissionToREST);
                $res->allowExcelDownload = trim($res->tblAgent->allowedToExcelReport);
                $res->userTimeZone = trim($res->tblAgent->timeZone);
                $res->allowToLASubmission = trim($res->tblAgent->allowToLASubmit);
                $res->subscribeToHOME = trim($res->tblAgent->subscribeToHOME);
                $res->allowEmailCampaign = trim($res->tblAgent->allowEmailCampaign);
                $res->allowToSendFax = trim($res->tblAgent->allowToSendFax);

                $res->allowToEditCommission = trim($res->tblAgent->allowAgentToEditCommission);
                $res->allowToSeeCommission = trim($res->tblAgent->allowAgentToSeeCommission);
                $res->allowedToSendFileDesignation = trim($res->tblAgent->allowToSendFileDesignation);
                $res->viewPublicNotes = trim($res->tblAgent->allowAgentToSeePublicNotes);

                $res->allowToCFPBSubmission = trim($res->tblAgent->allowToCFPBSubmit);
                $res->allowToViewCFPBPipeline = trim($res->tblAgent->allowToViewCFPBPipeline);
                $res->allowUserToAccessRAM = trim($res->tblAgent->allowToAccessRAM);
                $res->allowAgentWorkflowEdit = trim($res->tblAgent->allowWorkflowEdit);
                $res->allowToLockLoanFile = trim($res->tblAgent->allowToLockLoanFileAgent);
                $res->allowToViewMarketPlace = trim($res->tblAgent->allowToViewMarketPlace);
                $res->allowToupdateFileAndClient = trim($res->tblAgent->allowToupdateFileAndClient);
                $res->thirdPartyServices = trim($res->tblAgent->thirdPartyServices);
                $res->shareThisFile = trim($res->tblAgent->shareThisFile);
                $res->allowToViewCreditScreening = trim($res->tblAgent->allowToViewCreditScreening);
                $res->acqualifyOptStatus = trim($res->tblAgent->acqualifyOptStatus);
                $res->avatar = trim($res->tblAgent->avatar);

                $res->externalBroker = trim($res->tblAgent->externalBroker); //Loan officer Flag
                if ($res->externalBroker == 1) {
                    $res->secondaryBrokerNumber = $userNumber;// assigning Loan officer value as $secondary Broker number
                }

                $res->viewSubmitOfferTab = trim($res->tblAgent->allowToSubmitOffer);
                $res->userPriceEngineStatus = trim($res->tblAgent->userPriceEngineStatus);
                $res->allowToAccessInternalLoanProgram = trim($res->tblAgent->allowToAccessInternalLoanProgram);
                $res->allowToCopyFile = trim($res->tblAgent->allowToCopyFile);
                $res->allowToSeeAllBrokers = trim($res->tblAgent->allowToSeeAllBrokers);
                $res->allowToMassUpdate = (int)trim($res->tblAgent->allowToMassUpdate);
                $res->allowToViewContactsList = (int)trim($res->tblAgent->allowToViewContactsList);
                $res->allowToViewAutomationPopup = (int)$res->tblAgent->allowToViewAutomationPopup;
                $res->allowToEditLoanStage = 0;
                $res->allowToManageDraws = (int)$res->tblAgent->allowToManageDraws;

            }

            $res->agentBranchArray = [];
            $crtPage = basename(trim($_SERVER['PHP_SELF']));
            if ($crtPage == 'myPipeline.php' || $crtPage == 'getFileDetails.php' || $crtPage == 'exportClientFiles2.php') {
                $rs = getBranchesForAgent::getReport(['brokerNumber' => $userNumber]);
                foreach ($rs as $item) {
                    $res->agentBranchArray[$item['executiveId']] = $item;
                }
            }
        } elseif ($userGroup == glUserGroup::USER_GROUP_BRANCH && $userNumber > 0) {

            $res->tblBranch = tblBranch::Get([
                tblBranch_db::COLUMN_EXECUTIVEID  => $userNumber,
                tblBranch_db::COLUMN_ACTIVESTATUS => 1,
            ]);

            if ($res->tblBranch) {
                $res->loggedInEmail = $res->tblBranch->executiveEmail;

                $res->allowToSeeAlowareTab = trim($res->tblBranch->AllowToCreateAloware);
                $res->allowToEditMyFile = trim($res->tblBranch->allowLMRAEToEditFile);
                $res->allowToUpdateFileAdminSection = trim($res->tblBranch->allowToUpdateFileAdminSection);
                $res->allowExcelDownload = trim($res->tblBranch->allowedToExcelReport);
                $res->permissionToREST = trim($res->tblBranch->permissionToREST);
                $res->viewPrivateNotes = trim($res->tblBranch->seePrivate);
                $res->allowToEditOwnNotes = trim($res->tblBranch->allowedToEditOwnNotes);
                $res->allowToEditCommission = trim($res->tblBranch->allowLMRAEToEditCommission);
                $res->allowToSeeCommission = trim($res->tblBranch->allowBranchToSeeCommission);
                $res->allowToAccessDocs = trim($res->tblBranch->allowLMRAEToAccessDocs);
                $res->allowToDeleteUploadedDocs = trim($res->tblBranch->allowedToDeleteUplodedDocs);
                $res->allowDashboard = trim($res->tblBranch->allowBranchToSeeDashboard);
                $res->allowToCreateFiles = trim($res->tblBranch->allowBranchToCreateFiles);
                $res->allowToCreateTasks = trim($res->tblBranch->allowBranchToCreateTasks);
                $res->changeDIYPlan = trim($res->tblBranch->changeDIYPlan);
                $res->allowToOnOffAgentLogin = trim($res->tblBranch->allowLMRToOnOffAgentLogin);
                $res->allowBranchToEditAgentProfile = trim($res->tblBranch->allowLMRToEditAgentProfile);
                $res->allowedToSendHomeownerLink = trim($res->tblBranch->allowToSendHomeownerLink);
                $res->AEUserType = trim($res->tblBranch->userType);
                $res->userTimeZone = trim($res->tblBranch->timeZone);
                $res->allowToLASubmission = trim($res->tblBranch->allowToLASubmit);
                $res->subscribeToHOME = trim($res->tblBranch->subscribeToHOME);
                $res->allowEmailCampaign = trim($res->tblBranch->allowEmailCampaign);
                $res->allowToSendFax = trim($res->tblBranch->allowToSendFax);
                $res->allowedToSendFileDesignation = trim($res->tblBranch->allowToSendFileDesignation);
                $res->viewPublicNotes = trim($res->tblBranch->allowBranchToSeePublicNotes);
                $res->allowToCFPBSubmission = trim($res->tblBranch->allowToCFPBSubmit);

                $res->allowToViewCFPBPipeline = trim($res->tblBranch->allowToViewCFPBPipeline);
                $res->allowUserToAccessRAM = trim($res->tblBranch->allowToAccessRAM);
                $res->allowBranchManagerToLogin = trim($res->tblBranch->allowBranchManagerToLogin);
                $res->allowBranchWorkflowEdit = trim($res->tblBranch->allowWorkflowEdit);
                $res->allowToLockLoanFile = trim($res->tblBranch->allowToLockLoanFileBranch);
                $res->allowBranchToAddAgent = trim($res->tblBranch->allowToAddAgent);
                $res->allowToViewMarketPlace = trim($res->tblBranch->allowToViewMarketPlace);
                $res->allowToupdateFileAndClient = trim($res->tblBranch->allowToupdateFileAndClient);
                $res->thirdPartyServices = trim($res->tblBranch->thirdPartyServices);
                $res->shareThisFile = trim($res->tblBranch->shareThisFile);
                $res->viewSubmitOfferTab = trim($res->tblBranch->allowToSubmitOffer);
                $res->allowToViewCreditScreening = trim($res->tblBranch->allowToViewCreditScreening);
                $res->acqualifyOptStatus = trim($res->tblBranch->acqualifyOptStatus);
                $res->userPriceEngineStatus = trim($res->tblBranch->userPriceEngineStatus);
                $res->allowToAccessInternalLoanProgram = trim($res->tblBranch->allowToAccessInternalLoanProgram);
                $res->allowToCopyFile = trim($res->tblBranch->allowToCopyFile);
                $res->allowToMassUpdate = (int)trim($res->tblBranch->allowToMassUpdate);
                $res->avatar = trim($res->tblBranch->avatar);
                $res->allowToViewAutomationPopup = trim($res->tblBranch->allowToViewAutomationPopup);
                $res->allowToEditLoanStage = 0;
                $res->allowToManageDraws = $res->tblBranch->allowToManageDraws;
            }

            // $allowEmailCampaign = 0;
            $res->userSeeBilling = 1;

            $services = tblBranchLMRClientType::GetAll([
                tblBranchLMRClientType_db::COLUMN_BRANCHID => $userNumber,
            ]);

            foreach ($services as $service) {
                $res->myServicesArray[] = trim($service->LMRClientType);
            }


            $modules = tblBranchModules::GetAll([
                tblBranchModules_db::COLUMN_BRANCHID => $userNumber,
            ]);

            foreach ($modules as $module) {
                $res->myModulesArray[] = trim($module->moduleCode);
            }
        } elseif (($userGroup == glUserGroup::USER_GROUP_EMPLOYEE
                || $userGroup == glUserGroup::USER_GROUP_AUDITOR
                || $userGroup == glUserGroup::USER_GROUP_CFPB_AUDITOR
                || $userGroup == glUserGroup::USER_GROUP_AUDITOR_MANAGER
            ) && $userNumber > 0) {

            $res->tblAdminUser = tblAdminUsers::Get([
                tblAdminUsers_db::COLUMN_AID          => $userNumber,
                tblAdminUsers_db::COLUMN_ACTIVESTATUS => 1,
            ]);

            if ($res->tblAdminUser) {
                $res->loggedInEmail = $res->tblAdminUser->email;

                $res->manualMERS = $res->tblAdminUser->manualMERS;

                $res->allowToSeeAlowareTab = trim($res->tblAdminUser->allowEmpToCreateAloware);
                $res->allowToEditMyFile = trim($res->tblAdminUser->allowBOToEditLMRFile);
                $res->allowExcelDownload = trim($res->tblAdminUser->allowExcelDownload);
                $res->allowToViewAllFiles = trim($res->tblAdminUser->allowToViewAllFiles);
                $res->allowToEditOwnNotes = trim($res->tblAdminUser->allowedToEditOwnNotes);
                $res->userSeeBilling = trim($res->tblAdminUser->seeBilling);
                $res->allowEmailCampaign = trim($res->tblAdminUser->allowEmailCampaign);
                $res->allowToDeleteUploadedDocs = trim($res->tblAdminUser->allowedToDeleteUplodedDocs);
                $res->allowDashboard = trim($res->tblAdminUser->allowEmpToSeeDashboard);
                $res->allowToCreateFiles = trim($res->tblAdminUser->allowEmpToCreateFiles);
                $res->allowToCreateTasks = trim($res->tblAdminUser->allowEmpToCreateTasks);
                $res->permissionToREST = trim($res->tblAdminUser->permissionToREST);
                $res->viewPrivateNotes = trim($res->tblAdminUser->seePrivate);
                $res->changeDIYPlan = trim($res->tblAdminUser->changeDIYPlan);
                $res->allowedToSendHomeownerLink = trim($res->tblAdminUser->allowToSendHomeownerLink);
                $res->allowToUpdateFileAdminSection = trim($res->tblAdminUser->allowToUpdateFileAdminSection);
                $res->userTimeZone = trim($res->tblAdminUser->timeZone);
                $res->allowToLASubmission = trim($res->tblAdminUser->allowToLASubmit);
                $res->subscribeToHOME = trim($res->tblAdminUser->subscribeToHOME);
                $res->allowToSendFax = trim($res->tblAdminUser->allowToSendFax);

                $res->allowToEditCommission = trim($res->tblAdminUser->allowEmployeeToEditCommission);
                $res->allowToSeeCommission = trim($res->tblAdminUser->allowEmployeeToSeeCommission);
                $res->allowedToSendFileDesignation = trim($res->tblAdminUser->allowToSendFileDesignation);
                $res->viewPublicNotes = trim($res->tblAdminUser->allowEmpToSeePublicNotes);

                $res->allowToChangeOrAssignBranchForFile = trim($res->tblAdminUser->allowToChangeOrAssignBranchForFile);
                $res->allowToCFPBSubmission = trim($res->tblAdminUser->allowToCFPBSubmit);
                $res->allowToViewCFPBPipeline = trim($res->tblAdminUser->allowToViewCFPBPipeline);
                $res->allowUserToAccessRAM = trim($res->tblAdminUser->allowToAccessRAM);
                $res->allowEmpToCreateBranch = trim($res->tblAdminUser->allowEmpToCreateBranch);
                $res->convertNewBRIntoEmpOwnBR = trim($res->tblAdminUser->convertNewBRIntoEmpOwnBR);
                $res->allowToSeeBillingSectionForFile = trim($res->tblAdminUser->allowToSeeBillingSectionForFile);
                $res->allowEmpToCreateAgent = trim($res->tblAdminUser->allowEmpToCreateAgent);
                $res->allowEmpToSeeAgent = trim($res->tblAdminUser->allowEmpToSeeAgent);
                $res->allowToLockLoanFile = trim($res->tblAdminUser->allowToLockLoanFileEmpl);
                $res->allowToViewMarketPlace = trim($res->tblAdminUser->allowToViewMarketPlace);
                $res->allowToupdateFileAndClient = trim($res->tblAdminUser->allowToupdateFileAndClient ?? '');
                $res->thirdPartyServices = trim($res->tblAdminUser->thirdPartyServices);
                $res->shareThisFile = trim($res->tblAdminUser->shareThisFile);
                $res->allowToViewContactsList = trim($res->tblAdminUser->allowToViewContactsList);
                $res->viewSubmitOfferTab = trim($res->tblAdminUser->allowToSubmitOffer);
                $res->allowToViewCreditScreening = trim($res->tblAdminUser->allowToViewCreditScreening);
                $res->acqualifyOptStatus = trim($res->tblAdminUser->acqualifyOptStatus);
                $res->userPriceEngineStatus = trim($res->tblAdminUser->userPriceEngineStatus);
                $res->allowUserToSendMsgToBorrower = trim($res->tblAdminUser->allowUserToSendMsgToBorrower);
                $res->allowEmpToCopyFile = trim($res->tblAdminUser->allowEmpToCopyFile);
                $res->allowToSelectFromEmail = $res->tblAdminUser->allowToSelectFromEmail;

                $res->allowToMassUpdate = (int)$res->tblAdminUser->allowToMassUpdate;
                $res->avatar = trim($res->tblAdminUser->avatar);
                $res->allowToSeeFeesInDashboard = trim($res->tblAdminUser->allowToSeeFeesInDashboard);
                $res->allowToViewAutomationPopup = $res->tblAdminUser->allowToViewAutomationPopup;
                $res->allowToControlAutomationPopup = $res->tblAdminUser->allowToControlAutomationPopup;
                $res->allowToEditLoanStage = trim($res->tblAdminUser->allowToEditLoanStage);
                $res->allowToManageDraws = $res->tblAdminUser->allowToManageDraws;

            }

            if ($userRole == 'Manager') {
                $res->allowToEditOwnNotes = 1;
                $res->allowToAccessDocs = 1;
                $res->allowToDeleteUploadedDocs = 1;
            } else {
                $res->allowToAccessDocs = $res->allowToDeleteUploadedDocs;
            }

        } elseif ($publicUser == 1) {
            $res->allowToEditMyFile = 1;
            /** To Edit file for the users who are tried to submit from Iframe version **/
        }

        if ($userGroup == glUserGroup::USER_GROUP_AUDITOR) {
            $res->allowToLASubmission = 1;
            $res->allowToLASubmitForPC = 1;
        }
        if ($userGroup == glUserGroup::USER_GROUP_CFPB_AUDITOR
            || $userGroup == glUserGroup::USER_GROUP_AUDITOR_MANAGER
        ) {
            $res->allowToCFPBSubmission = 1;
            $res->allowToCFPBSubmitForPC = 1;
            $res->allowToViewCFPBPipeline = 1;
            $res->allowPCUserToSubmitCFPB = 1;
            $res->allowToCreateFiles = 0;
        }

        if (in_array($PCID, $accessRestrictionPC)) {
            $res->allowExcelDownload = 0;
            if ($userNumber == 6844 || $userNumber == 7485 || $userNumber == 7437) {
                $res->allowExcelDownload = 1;
            }
        }

        if (in_array($PCID, $glRAMAccessPC) && $res->allowUserToAccessRAM == 1) {
            /** Show RAM tab ONLY if the PC is allowed to access RAM **/
            $res->allowToAccessRAM = 1;
        }

        return $res;
    }
}
