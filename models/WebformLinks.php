<?php

namespace models;

use models\Controllers\backoffice\LMRequest;
use models\standard\HTTP;
use models\types\strongType;

class WebformLinks extends strongType
{
    public const LinkName = [
        'fullAppLinkBorrower'                   => 'Full App for Borrower',
        'fullAppLinkBorrowerMulti'              => 'Full App for Borrower (Multi-Step)',
        'fullAppReadOnly'                       => 'Full App Read Only',
        'fullAppBorrowerRedacted'               => 'Full App Borrower Info Redacted',
        'fullAppReadOnlyUploadDocs'             => 'Full App Read Only with Access to Uploaded Docs',
        'fullAppReadOnlySubmitOffer'            => 'Link with Offer Submission',
        'fullAppShareLinkSubmitOffer'           => 'Link with Offer Submission',
        'fullAppShareLinkAUDSubmitOffer'        => 'Link with Offer Submission',
        'fullAppLinkBroker'                     => 'Full App for Broker',
        'quickAppLinkBorrower'                  => 'Quick App for Borrower',
        'quickAppLinkCoBorrower'                => 'Quick App for Co-Borrower',
        'fullAppLinkCoBorrower'                 => 'Full App for Co-Borrower',
        'quickAppLinkCoBorrowerMulti'           => 'Quick App for Co-Borrower (Multi-Step)',
        'fullAppLinkCoBorrowerMulti'            => 'Full App for Co-Borrower (Multi-Step)',
        'quickAppLinkBorrowerMulti'             => 'Quick App for Borrower (Multi-Step)',
        'quickAppLinkReadOnly'                  => 'Quick App Read Only',
        'quickAppBorrowerRedacted'              => 'Quick App-Borrower Info Redacted',
        'quickAppReadOnlyUploadDocs'            => 'Quick App Read Only with Access to Uploaded Docs',
        'quickAppRedactedOfferSubmission'       => 'Link with Offer Submission',
        'quickAppLinkReadOnlySubmitOffer'       => 'Link with Offer Submission',
        'quickAppShareLinkAUDSubmitOffer'       => 'Link with Offer Submission',
        'quickAppLinkBroker'                    => 'Quick App for Broker',
        'requiredDocsBorrower'                  => 'Loan Status & Required Docs Upload Portal-for Borrower',
        'requiredDocsBorrowerBrokerLogo'        => 'Loan Status & Required Docs Upload Portal-for Borrower (With Broker Logo)',
        'requiredDocsBroker'                    => 'Loan Status & Required Docs Upload Portal-for Broker',
        'requiredDocsLoanOfficer'               => 'Loan Status & Required Docs Upload Portal-for Loan Officer',
        'quickAppLinkBrokerMulti'               => 'Quick App for Broker(Multi-Step)',
        'fullAppLinkBrokerMulti'                => 'Full App for Broker(Multi-Step)',
        'rentRollWebFormBO'                     => 'Rent Roll-Non Sign',
        'rentRollWebFormBOSign'                 => 'Rent Roll-Sign',
        'rentRollWebFormBOReadOnly'             => 'Read Only Rent Roll',
        'uniformResLoanApp'                     => 'Uniform Residential Loan Application-Non Sign',
        'uniformResLoanAppSign'                 => 'Uniform Residential Loan Application-Sign',
        'uniformResLoanAppReadOnly'             => 'Uniform Residential Loan Application Read Only',
        'PFSWebformBO'                          => 'PFS Webform-Non Sign',
        'PFSWebformBOSign'                      => 'PFS Webform-Sign',
        'PFSWebformBOReadOnly'                  => 'PFS Webform Read Only',
        'dealStatusInfo'                        => 'Share Links for 3rd Parties (Lenders, Investors, Title)',
        'CashFlowWebformBO'                     => 'Subject Property Cash Flow Webform-Non Sign',
        'CashFlowWebformBOSign'                 => 'Subject Property Cash Flow-Sign',
        'CashFlowWebformBOReadOnly'             => 'Subject Property Cash Flow Read Only',
        'paymentInfoWebFormBO'                  => 'ACH/Check -Non Sign',
        'paymentInfoWebFormBOSign'              => 'ACH/Check -Sign',
        'paymentInfoWebFormBOReadOnly'          => 'ACH/Check Read Only',
        'creditPaymentInfoWebFormBO'            => 'Credit Card Info  -Non Sign',
        'creditPaymentInfoWebFormBOSign'        => 'Credit Card Info  -Sign',
        'creditPaymentInfoWebFormBOReadOnly'    => 'Credit Card Info  Read Only',
        'ACHCreditPaymentInfoWebFormBO'         => 'ACH/Check/CreditCard  -Non Sign',
        'ACHCreditPaymentInfoWebFormBOSign'     => 'ACH/Check/CreditCard  -Sign',
        'ACHCreditPaymentInfoWebFormBOReadOnly' => 'ACH/Check/CreditCard  Read Only',
        'additionalGuarantors'                  => 'Additional Guarantors -Non Sign',
        'additionalGuarantorsSign'              => 'Additional Guarantors -Sign',
        'additionalGuarantorsReadOnly'          => 'Additional Guarantors Ready Only',
        'submitReviseSow'                       => 'Submit/Revise Scope of Work',
    ];


    public const BASE_URL = CONST_SITE_URL;
    public const UPLOAD_URL = CONST_SITE_URL;
    public const PSF_URL = CONST_SITE_URL;
    public const PAYMENT_URL = CONST_SITE_URL;
    public const RENT_ROLL_URL = CONST_SITE_URL;
    public const CASH_FLOW_URL = CONST_SITE_URL;
    public const ADDITIONAL_GUARANTORS_URL = CONST_SITE_URL;
    public static ?int $branchReferralCode = null;
    public static ?int $agentReferralCode = null;
    public static ?int $LMRId = null;
    public static ?string $pointOfContactInfo = null;
    public static ?string $fileType = null;
    public static ?array $fileInfo = null;
    public static ?int $PCID = null;
    public static bool $linkWithOfferSubmission = true;
    public static ?string $fullAppLinkBorrower = null;
    public static ?string $fullAppLinkBorrowerMulti = null;
    public static ?string $fullAppReadOnly = null;
    public static ?string $fullAppBorrowerRedacted = null;
    public static ?string $fullAppReadOnlyUploadDocs = null;
    public static ?string $fullAppReadOnlySubmitOffer = null;
    public static ?string $fullAppShareLinkSubmitOffer = null;
    public static ?string $fullAppShareLinkAUDSubmitOffer = null;
    public static ?string $fullAppLinkBroker = null;
    public static ?string $quickAppLinkBorrower = null;
    public static ?string $quickAppLinkCoBorrower = null;
    public static ?string $fullAppLinkCoBorrower = null;
    public static ?string $quickAppLinkCoBorrowerMulti = null;
    public static ?string $fullAppLinkCoBorrowerMulti = null;
    public static ?string $quickAppLinkBorrowerMulti = null;
    public static ?string $quickAppLinkReadOnly = null;
    public static ?string $quickAppBorrowerRedacted = null;
    public static ?string $quickAppReadOnlyUploadDocs = null;
    public static ?string $quickAppRedactedOfferSubmission = null;
    public static ?string $quickAppLinkReadOnlySubmitOffer = null;
    public static ?string $quickAppShareLinkAUDSubmitOffer = null;
    public static ?string $quickAppLinkBroker = null;
    public static ?string $requiredDocsBorrower = null;
    public static ?string $requiredDocsBorrowerBrokerLogo = null;
    public static ?string $requiredDocsBroker = null;
    public static ?string $requiredDocsLoanOfficer = null;
    public static ?string $quickAppLinkBrokerMulti = null;
    public static ?string $fullAppLinkBrokerMulti = null;
    public static ?string $rentRollWebFormBO = null;
    public static ?string $rentRollWebFormBOSign = null;
    public static ?string $rentRollWebFormBOReadOnly = null;
    public static ?string $uniformResLoanApp = null;
    public static ?string $uniformResLoanAppSign = null;
    public static ?string $uniformResLoanAppReadOnly = null;
    public static ?string $PFSWebformBO = null;
    public static ?string $PFSWebformBOSign = null;
    public static ?string $PFSWebformBOReadOnly = null;
    public static ?string $dealStatusInfo = null;
    public static ?string $CashFlowWebformBO = null;
    public static ?string $CashFlowWebformBOSign = null;
    public static ?string $CashFlowWebformBOReadOnly = null;
    public static ?string $paymentInfoWebFormBO = null;
    public static ?string $paymentInfoWebFormBOSign = null;
    public static ?string $paymentInfoWebFormBOReadOnly = null;
    public static ?string $creditPaymentInfoWebFormBO = null;
    public static ?string $creditPaymentInfoWebFormBOSign = null;
    public static ?string $creditPaymentInfoWebFormBOReadOnly = null;
    public static ?string $ACHCreditPaymentInfoWebFormBO = null;


    // public const BASE_URL = CONST_SITE_URL . 'backoffice/loan/web_form/HMLO?';
    public static ?string $ACHCreditPaymentInfoWebFormBOSign = null;
    public static ?string $ACHCreditPaymentInfoWebFormBOReadOnly = null;
    public static ?string $pointOfContactEmail = null;
    public static ?string $pointOfContactName = null;
    public static ?string $additionalGuarantors = null;
    public static ?string $additionalGuarantorsSign = null;
    public static ?string $additionalGuarantorsReadOnly = null;
    public static ?string $submitReviseSow = null;

    public static function getLink(
        string $name,
        string $toolTip
    ): string
    {
        $href = self::${$name} ?? null;

        if (!$href) {
            return '';
        }

        $display = self::LinkName[$name] ?? null;

        return '
        <a      href="' . $href . '"
                target="_blank"
                data-toggle="tooltip"
                data-placement="top"
                title="' . $toolTip . '">
            ' . $display . '
        </a>
        <span class="float-right">
            <i  class="fa fa-copy copy-link tooltipClass text-primary ml-5"
                id="' . htmlentities($href) . '"
                title="Click to Copy Link"></i>
        </span>
        ';
    }

    public static function InitLinks(
        int     $LMRId,
        ?string $executiveEmail,
        ?array  $LMRPromoCodeArray,
        ?string $brokerEmail,
        ?array  $agentLMRAffiliateInfoArray,
        ?string $borrowerName,
        ?string $borrowerEmail,
        ?string $brokerName,
        ?string $loanOfficerName,
        ?string $loanOfficerEmail,
        ?string $moduleCode,
        ?string $listingType,
        ?string $pointOfContactEmail,
        ?string $pointOfContactName
    )
    {

        if ($listingType == 'CL') {
            self::$linkWithOfferSubmission = false; // Hide "Link with Offer Submission" Links for Deal Status = Closed Loan.
            self::$dealStatusInfo = "<p class='h4 text-danger font-weight-bolder'>Note: <em>The Deal Status of this file is Closed.</em></p>";
        }

        self::$pointOfContactEmail = $pointOfContactEmail;
        self::$pointOfContactName = $pointOfContactName;


        if (in_array($moduleCode, ['HMLO', 'loc'])) { // Only for HMLO & Business Funding
            if ($pointOfContactName && $pointOfContactEmail) {
                self::$pointOfContactInfo = '<h4>No point of contact set.<span style="margin-left: 10px"><a class="btn btn-primary openPopup">Set Point of Contact</a></span></h4><br>';
            } else {
                $dealStatus = '';
                if ($listingType == 'OFF') {
                    $dealStatus = 'Open For Funding';
                } elseif ($listingType == 'NFS') {
                    $dealStatus = 'Note for Sale';
                } elseif ($listingType == 'CL') {
                    $dealStatus = 'Closed Loan';
                } elseif ($listingType == 'PFS') {
                    $dealStatus = 'Property For Sale(Coming Soon)';
                }
                self::$pointOfContactInfo = '<h4>Point of Contact: <em><strong>' . $pointOfContactName . '</strong></em> Deal Status: <em><strong>' . $dealStatus . '</strong></em><span style="margin-left: 10px"><a class="btn btn-sm btn-primary openPopup">Edit Details</a></span></h4><br>';
            }
        } else {
            self::$linkWithOfferSubmission = false; // Hide "Link with Offer Submission" Links for File Type Other than HMLO & Business Funding
        }

        if (count($LMRPromoCodeArray) > 0) {
            $LMRPromoCodeArray = array_change_key_case($LMRPromoCodeArray);
            if (array_key_exists(strtolower($executiveEmail), $LMRPromoCodeArray)) {
                self::$branchReferralCode = intval($LMRPromoCodeArray[strtolower($executiveEmail)]);
            }
        }

        $branchParameters = [
            'bRc'  => cypher::myEncryption(self::$branchReferralCode),
            'fOpt' => cypher::myEncryption('branch'),
            'lid'  => cypher::myEncryption($LMRId),
        ];

        $commonParameters = array_merge($branchParameters, [
            'opt' => cypher::myEncryption('Email'),
            'ft'  => $moduleCode,
        ]);

        $BASE_URL = LMRequest::myFileInfo()->PCInfo()->isPLO && LMRequest::myFileInfo()->PCInfo()->vhost
            ? LMRequest::myFileInfo()->PCInfo()->vhost : self::BASE_URL;
        $BASE_URL = HTTP::formatUrl($BASE_URL);

        $PSF_URL = LMRequest::myFileInfo()->PCInfo()->isPLO && LMRequest::myFileInfo()->PCInfo()->vhost
            ? LMRequest::myFileInfo()->PCInfo()->vhost : self::PSF_URL;
        $PSF_URL = HTTP::formatUrl($PSF_URL);

        $PAYMENT_URL = LMRequest::myFileInfo()->PCInfo()->isPLO && LMRequest::myFileInfo()->PCInfo()->vhost
            ? LMRequest::myFileInfo()->PCInfo()->vhost : self::PAYMENT_URL;
        $PAYMENT_URL = HTTP::formatUrl($PAYMENT_URL);

        $RENT_ROLL_URL = LMRequest::myFileInfo()->PCInfo()->isPLO && LMRequest::myFileInfo()->PCInfo()->vhost
            ? LMRequest::myFileInfo()->PCInfo()->vhost : self::RENT_ROLL_URL;
        $RENT_ROLL_URL = HTTP::formatUrl($RENT_ROLL_URL);

        $CASH_FLOW_URL = LMRequest::myFileInfo()->PCInfo()->isPLO && LMRequest::myFileInfo()->PCInfo()->vhost
            ? LMRequest::myFileInfo()->PCInfo()->vhost : self::CASH_FLOW_URL;
        $CASH_FLOW_URL = HTTP::formatUrl($CASH_FLOW_URL);

        $ADDITIONAL_GUARANTORS_URL = LMRequest::myFileInfo()->PCInfo()->isPLO && LMRequest::myFileInfo()->PCInfo()->vhost ?
            LMRequest::myFileInfo()->PCInfo()->vhost : self::ADDITIONAL_GUARANTORS_URL;
        $ADDITIONAL_GUARANTORS_URL = HTTP::formatUrl($ADDITIONAL_GUARANTORS_URL);

        $UPLOAD_URL = LMRequest::myFileInfo()->PCInfo()->isPLO && LMRequest::myFileInfo()->PCInfo()->vhost
            ? LMRequest::myFileInfo()->PCInfo()->vhost : self::UPLOAD_URL;
        $UPLOAD_URL = HTTP::formatUrl($UPLOAD_URL);

        if ($executiveEmail) {


            self::$fullAppLinkBorrower = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'    => cypher::myEncryption('FA'),
                        'UType' => cypher::myEncryption('Borrower'),
                    ])
                );

            self::$quickAppLinkBorrower = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'    => cypher::myEncryption('QA'),
                        'UType' => cypher::myEncryption('Borrower'),
                    ])
                );

            self::$quickAppLinkBorrowerMulti = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'    => cypher::myEncryption('QA'),
                        'UType' => cypher::myEncryption('Borrower'),
                        'view'  => cypher::myEncryption('wizard'),
                    ])
                );

            self::$fullAppLinkBorrowerMulti = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'    => cypher::myEncryption('FA'),
                        'UType' => cypher::myEncryption('Borrower'),
                        'view'  => cypher::myEncryption('wizard'),
                    ])
                );

            //Quick App Read Only
            self::$quickAppLinkReadOnly = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op' => cypher::myEncryption('QA'),
                        'sl' => cypher::myEncryption('shareLink'),
                    ])
                );

            //Full App Read Only
            self::$fullAppReadOnly = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op' => cypher::myEncryption('FA'),
                        'sl' => cypher::myEncryption('shareLink'),
                    ])
                );


            //Quick App-Borrower Info Redacted
            self::$quickAppBorrowerRedacted = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'  => cypher::myEncryption('QA'),
                        'sl'  => cypher::myEncryption('shareLink'),
                        'bir' => cypher::myEncryption('yes'),
                    ])
                );


            //Full App Borrower Info Redacted
            self::$fullAppBorrowerRedacted = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'  => cypher::myEncryption('FA'),
                        'sl'  => cypher::myEncryption('shareLink'),
                        'bir' => cypher::myEncryption('yes'),
                    ])
                );

            //Quick App Read Only with Access to Uploaded Docs
            self::$quickAppReadOnlyUploadDocs = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'  => cypher::myEncryption('QA'),
                        'sl'  => cypher::myEncryption('shareLink'),
                        'aud' => cypher::myEncryption('yes'),
                    ])
                );

            //Full App Read Only with Access to Uploaded Docs
            self::$fullAppReadOnlyUploadDocs = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'  => cypher::myEncryption('FA'),
                        'sl'  => cypher::myEncryption('shareLink'),
                        'aud' => cypher::myEncryption('yes'),
                    ])
                );

            //Quick App-Borrower Info Redacted (Link with Offer Submission)
            self::$quickAppRedactedOfferSubmission = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'  => cypher::myEncryption('QA'),
                        'sl'  => cypher::myEncryption('shareLink'),
                        'bir' => cypher::myEncryption('yes'),
                        'so'  => cypher::myEncryption('yes'),
                    ])
                );


            //Quick App Read Only (Link with Offer Submission)
            self::$quickAppLinkReadOnlySubmitOffer = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op' => cypher::myEncryption('QA'),
                        'sl' => cypher::myEncryption('shareLink'),
                        'so' => cypher::myEncryption('yes'),
                    ])
                );


            //Quick App Read Only with Access to Uploaded Docs (Link with Offer Submission)
            self::$quickAppShareLinkAUDSubmitOffer = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'  => cypher::myEncryption('QA'),
                        'sl'  => cypher::myEncryption('shareLink'),
                        'aud' => cypher::myEncryption('yes'),
                        'so'  => cypher::myEncryption('yes'),
                    ])
                );


            //Full App Borrower Info Redacted (Link with Offer Submission)
            self::$fullAppShareLinkSubmitOffer = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'  => cypher::myEncryption('FA'),
                        'sl'  => cypher::myEncryption('shareLink'),
                        'bir' => cypher::myEncryption('yes'),
                        'so'  => cypher::myEncryption('yes'),
                    ])
                );

            //Full App Read Only (Link with Offer Submission)
            self::$fullAppReadOnlySubmitOffer = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op' => cypher::myEncryption('FA'),
                        'sl' => cypher::myEncryption('shareLink'),
                        'so' => cypher::myEncryption('yes'),
                    ])
                );


            //Full App Read Only with Access to Uploaded Docs (Link with Offer Submission)
            self::$fullAppShareLinkAUDSubmitOffer = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'  => cypher::myEncryption('FA'),
                        'sl'  => cypher::myEncryption('shareLink'),
                        'aud' => cypher::myEncryption('yes'),
                        'so'  => cypher::myEncryption('yes'),
                    ])
                );

            self::$fullAppLinkCoBorrower = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'    => cypher::myEncryption('FA'),
                        'UType' => cypher::myEncryption('CoBorrower'),
                    ])
                );

            self::$quickAppLinkCoBorrower = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'    => cypher::myEncryption('QA'),
                        'UType' => cypher::myEncryption('CoBorrower'),
                    ])
                );

            self::$fullAppLinkCoBorrowerMulti = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'    => cypher::myEncryption('FA'),
                        'UType' => cypher::myEncryption('CoBorrower'),
                        'view'  => cypher::myEncryption('wizard'),
                    ])
                );

            self::$quickAppLinkCoBorrowerMulti = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                    array_merge($commonParameters, [
                        'op'    => cypher::myEncryption('QA'),
                        'UType' => cypher::myEncryption('CoBorrower'),
                        'view'  => cypher::myEncryption('wizard'),
                    ])
                );
        }

        if ($brokerEmail) {


            if (count($agentLMRAffiliateInfoArray) > 0) {
                $agentLMRAffiliateInfoArray = array_change_key_case($agentLMRAffiliateInfoArray);
                if (array_key_exists(strtolower(trim($brokerEmail)), $agentLMRAffiliateInfoArray)) {
                    self::$agentReferralCode = intval($agentLMRAffiliateInfoArray[strtolower(trim($brokerEmail))]);

                    $brokerCommonValues = [
                        'bRc'   => cypher::myEncryption(self::$branchReferralCode),
                        'aRc'   => cypher::myEncryption(self::$agentReferralCode),
                        'fOpt'  => cypher::myEncryption('agent'),
                        'lid'   => cypher::myEncryption($LMRId),
                        'ft'    => $moduleCode,
                        'opt'   => cypher::myEncryption('Email'),
                        'UType' => cypher::myEncryption('Agent/Broker'),
                    ];

                    self::$fullAppLinkBroker = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                            array_merge($brokerCommonValues, [
                                'op' => cypher::myEncryption('FA'),
                            ])
                        );
                    self::$quickAppLinkBroker = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                            array_merge($brokerCommonValues, [
                                'op' => cypher::myEncryption('QA'),
                            ])
                        );

                    self::$fullAppLinkBrokerMulti = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                            array_merge($brokerCommonValues, [
                                'op'   => cypher::myEncryption('FA'),
                                'view' => cypher::myEncryption('wizard'),
                            ])
                        );
                    self::$quickAppLinkBrokerMulti = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                            array_merge($brokerCommonValues, [
                                'op'   => cypher::myEncryption('QA'),
                                'view' => cypher::myEncryption('wizard'),
                            ])
                        );
                }
            }
        }

        $webFormRequiredUploadCommonValues = [
            'lid' => cypher::myEncryption($LMRId),
            'ft'  => $moduleCode,
        ];

        self::$requiredDocsBorrower = $UPLOAD_URL.'backoffice/uploadLMRDocs.php?' . http_build_query(
                array_merge($webFormRequiredUploadCommonValues, [
                    'UType' => cypher::myEncryption('Borrower'),
                    'UName' => cypher::myEncryption($borrowerName),
                    'email' => cypher::myEncryption($borrowerEmail),
                ]),
            );
        self::$requiredDocsBorrowerBrokerLogo = self::$requiredDocsBorrower . '&lg=' . cypher::myEncryption('brokerLogo');

        self::$requiredDocsBroker = $UPLOAD_URL.'backoffice/uploadLMRDocs.php?' . http_build_query(
                array_merge($webFormRequiredUploadCommonValues, [
                    'UType' => cypher::myEncryption('Agent/Broker'),
                    'UName' => cypher::myEncryption($brokerName),
                    'email' => cypher::myEncryption($brokerEmail),
                ]),
            );

        self::$requiredDocsLoanOfficer = $UPLOAD_URL.'backoffice/uploadLMRDocs.php?' . http_build_query(
                array_merge($webFormRequiredUploadCommonValues, [
                    'UType' => cypher::myEncryption('LoanOfficer'),
                    'UName' => cypher::myEncryption($loanOfficerName),
                    'email' => cypher::myEncryption($loanOfficerEmail),
                ]),
            );


        $commonParameters = [
            'lid' => cypher::myEncryption($LMRId),
            'op'  => cypher::myEncryption('BO'),
        ];

        self::$rentRollWebFormBO = $RENT_ROLL_URL.'rentRollWebForm.php?' . http_build_query(
                array_merge($commonParameters, [
                    'edit' => cypher::myEncryption('1'),
                ])
            );
        self::$rentRollWebFormBOSign = $RENT_ROLL_URL.'rentRollWebForm.php?' . http_build_query(
                array_merge($commonParameters, [
                    'edit' => cypher::myEncryption('1'),
                    's'    => cypher::myEncryption('1'),
                ])
            );
        self::$rentRollWebFormBOReadOnly = $RENT_ROLL_URL.'rentRollWebForm.php?' . http_build_query(
                array_merge($commonParameters, [
                    'edit' => cypher::myEncryption('0'),
                ])
            );


        $uniformResLoanAppCommonParameters = array_merge($branchParameters, [
            'op'     => cypher::myEncryption('1003'),
            'tabOpt' => 1003,
        ]);

        self::$uniformResLoanApp = $BASE_URL.'HMLOWebForm.php?' . http_build_query($uniformResLoanAppCommonParameters);

        self::$uniformResLoanAppSign = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                array_merge($uniformResLoanAppCommonParameters, [
                    's' => cypher::myEncryption('1'),
                ])
            );

        self::$uniformResLoanAppReadOnly = $BASE_URL.'HMLOWebForm.php?' . http_build_query(
                array_merge($uniformResLoanAppCommonParameters, [
                    'edit' => cypher::myEncryption('0'),
                ])
            );


        self::$PFSWebformBO = $PSF_URL.'PFSWebForm.php?' . http_build_query([
                'lid'  => cypher::myEncryption($LMRId),
                'op'   => cypher::myEncryption('BO'),
                'edit' => cypher::myEncryption('1'),
            ]);

        self::$PFSWebformBOSign = $PSF_URL.'PFSWebForm.php?' . http_build_query([
                'lid'  => cypher::myEncryption($LMRId),
                'op'   => cypher::myEncryption('BO'),
                'edit' => cypher::myEncryption('1'),
                's'    => cypher::myEncryption('1'),
            ]);

        self::$PFSWebformBOReadOnly = $PSF_URL.'PFSWebForm.php?' . http_build_query([
                'lid'  => cypher::myEncryption($LMRId),
                'op'   => cypher::myEncryption('BO'),
                'edit' => cypher::myEncryption('0'),
            ]);

        self::$CashFlowWebformBO = $CASH_FLOW_URL.'cashFlowWebform.php?' . http_build_query([
                'lid'  => cypher::myEncryption($LMRId),
                'op'   => cypher::myEncryption('BO'),
                'edit' => cypher::myEncryption('1'),
            ]);

        self::$CashFlowWebformBOSign = $CASH_FLOW_URL.'cashFlowWebform.php?' . http_build_query([
                'lid'  => cypher::myEncryption($LMRId),
                'op'   => cypher::myEncryption('BO'),
                'edit' => cypher::myEncryption('1'),
                's'    => cypher::myEncryption('1'),
            ]);

        self::$CashFlowWebformBOReadOnly = $CASH_FLOW_URL.'cashFlowWebform.php?' . http_build_query([
                'lid'  => cypher::myEncryption($LMRId),
                'op'   => cypher::myEncryption('BO'),
                'edit' => cypher::myEncryption('0'),
            ]);



        self::$paymentInfoWebFormBO = $PAYMENT_URL.'paymentInfoWebForm.php?' . http_build_query(
                [
                    'lid'              => cypher::myEncryption($LMRId),
                    'op'               => cypher::myEncryption('BO'),
                    'edit'             => cypher::myEncryption('1'),
                    'isPaymentWebform' => cypher::myEncryption('1'),
                    'paymentWebform'   => cypher::myEncryption('check'),
                ]
            );

        self::$paymentInfoWebFormBOSign = $PAYMENT_URL.'paymentInfoWebForm.php?' . http_build_query(
                [
                    'lid'              => cypher::myEncryption($LMRId),
                    'op'               => cypher::myEncryption('BO'),
                    'edit'             => cypher::myEncryption('1'),
                    's'                => cypher::myEncryption('1'),
                    'isPaymentWebform' => cypher::myEncryption('1'),
                    'paymentWebform'   => cypher::myEncryption('check'),
                ]
            );
        self::$paymentInfoWebFormBOReadOnly = $PAYMENT_URL.'paymentInfoWebForm.php?' . http_build_query(
                [
                    'lid'              => cypher::myEncryption($LMRId),
                    'op'               => cypher::myEncryption('BO'),
                    'edit'             => cypher::myEncryption('0'),
                    'isPaymentWebform' => cypher::myEncryption('1'),
                    'paymentWebform'   => cypher::myEncryption('check'),
                ]
            );
        self::$creditPaymentInfoWebFormBO = $PAYMENT_URL.'paymentInfoWebForm.php?' . http_build_query(
                [
                    'lid'              => cypher::myEncryption($LMRId),
                    'op'               => cypher::myEncryption('BO'),
                    'edit'             => cypher::myEncryption('1'),
                    'isPaymentWebform' => cypher::myEncryption('1'),
                    'paymentWebform'   => cypher::myEncryption('credit'),
                ]
            );
        self::$creditPaymentInfoWebFormBOSign = $PAYMENT_URL.'paymentInfoWebForm.php?' . http_build_query(
                [
                    'lid'              => cypher::myEncryption($LMRId),
                    'op'               => cypher::myEncryption('BO'),
                    'edit'             => cypher::myEncryption('1'),
                    's'                => cypher::myEncryption('1'),
                    'isPaymentWebform' => cypher::myEncryption('1'),
                    'paymentWebform'   => cypher::myEncryption('credit'),
                ]
            );
        self::$creditPaymentInfoWebFormBOReadOnly = $PAYMENT_URL.'paymentInfoWebForm.php?' . http_build_query(
                [
                    'lid'              => cypher::myEncryption($LMRId),
                    'op'               => cypher::myEncryption('BO'),
                    'edit'             => cypher::myEncryption('0'),
                    'isPaymentWebform' => cypher::myEncryption('1'),
                    'paymentWebform'   => cypher::myEncryption('credit'),
                ]
            );
        self::$ACHCreditPaymentInfoWebFormBO = $PAYMENT_URL.'paymentInfoWebForm.php?' . http_build_query(
                [
                    'lid'              => cypher::myEncryption($LMRId),
                    'op'               => cypher::myEncryption('BO'),
                    'edit'             => cypher::myEncryption('1'),
                    'isPaymentWebform' => cypher::myEncryption('1'),
                    'paymentWebform'   => cypher::myEncryption('both'),
                ]
            );
        self::$ACHCreditPaymentInfoWebFormBOSign = $PAYMENT_URL.'paymentInfoWebForm.php?' . http_build_query(
                [
                    'lid'              => cypher::myEncryption($LMRId),
                    'op'               => cypher::myEncryption('BO'),
                    'edit'             => cypher::myEncryption('1'),
                    's'                => cypher::myEncryption('1'),
                    'isPaymentWebform' => cypher::myEncryption('1'),
                    'paymentWebform'   => cypher::myEncryption('both'),
                ]
            );
        self::$ACHCreditPaymentInfoWebFormBOReadOnly = $PAYMENT_URL.'paymentInfoWebForm.php?' . http_build_query(
                [
                    'lid'              => cypher::myEncryption($LMRId),
                    'op'               => cypher::myEncryption('BO'),
                    'edit'             => cypher::myEncryption('0'),
                    'isPaymentWebform' => cypher::myEncryption('1'),
                    'paymentWebform'   => cypher::myEncryption('both'),
                ]
            );

        self::$additionalGuarantors = $ADDITIONAL_GUARANTORS_URL.'webForm/guarantor/?' . http_build_query(
                array_merge($commonParameters, [
                    'edit' => cypher::myEncryption('1'),
                ])
            );
        self::$additionalGuarantorsSign = $ADDITIONAL_GUARANTORS_URL.'webForm/guarantor/?' . http_build_query(
                array_merge($commonParameters, [
                    'edit' => cypher::myEncryption('1'),
                    's'    => cypher::myEncryption('1'),
                ])
            );
        self::$additionalGuarantorsReadOnly = $ADDITIONAL_GUARANTORS_URL.'webForm/guarantor/?' . http_build_query(
                array_merge($commonParameters, [
                    'edit' => cypher::myEncryption('0'),
                ])
            );
        self::$submitReviseSow = $BASE_URL.'drawManagement/submitReviseSow.php?'. http_build_query(
                array_merge($commonParameters, [
                    'op' => cypher::myEncryption('SOW'),
                    'UType' => cypher::myEncryption('Borrower'),
                    'lmrid' => cypher::myEncryption($LMRId),
                    'pcid' => cypher::myEncryption(self::$PCID),
                ])
            );
    }
}
