<?php

namespace models\pops\exportClientFiles;

use models\APIHelper;
use models\Database2;
use models\types\strongType;

class notes extends strongType
{
    public ?int $LMRId = null;
    public ?string $signExecutiveName = null;
    public ?int $private = null;
    public ?int $executiveId = null;
    public ?int $brokerNumber = null;
    public ?int $employeeId = null;
    public ?int $clientId = null;
    public ?string $processorComments = null; 
    public ?string $notesDate = null; 
    public ?string $displayIn = null; 
    public ?string $notesType = null; 
    public ?string $employeeName = null; 
    public ?string $agentName = null; 
    public ?string $branchName = null; 
    public ?string $clientName = null;
    public ?string $employeeEmail = null; 
    public ?string $agentEmail = null; 
    public ?string $branchEmail = null; 
    public ?string $clientEmail = null; 
    public ?string $propertyAddress = null; 
    public ?string $propertyCity = null; 
    public ?string $propertyState = null; 
    public ?string $propertyZipCode = null;

    /**
     * @param string $SQL_LMRId
     * @param array $params
     * @param int|null $viewPrivateNotes
     * @param int|null $viewPublicNotes
     * @return self[]
     */
    public static function getReport(string $SQL_LMRId, array $params, ?int $viewPrivateNotes, ?int $viewPublicNotes): array
    {
        $params['viewPrivateNotes'] = $viewPrivateNotes;
        $params['viewPublicNotes'] = $viewPublicNotes;
        $sql = APIHelper::getSQL(__DIR__ . '/sql/notes.sql');
        $sql = str_ireplace('\'--LMRIDs--\'', $SQL_LMRId, $sql);

        return Database2::getInstance()->queryData($sql, $params, function ($row) {
            $row['processorComments'] = urldecode($row['processorComments']);
            return new self($row);
        });
    }
}