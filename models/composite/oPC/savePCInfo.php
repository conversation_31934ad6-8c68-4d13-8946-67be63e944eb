<?php

namespace models\composite\oPC;

use models\constants\gl\glUserRole;
use models\cypher;
use models\lendingwise\tblProcessingCompany;
use models\MERS;
use models\PageVariables;
use models\Request;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;

/**
 *
 */
class savePCInfo extends strongType
{
    /**
     * @param $ip
     * @return array
     */
    public static function getReport($ip): array
    {
        $resultArray = [];

        $processingCompanyId = $ip['processingCompanyId'] ?? null;
        $fax = $ip['fax'] ?? null;
        $attorneyEmail = $ip['attorneyEmail'] ?? null;
        $attorneyCell = $ip['attorneyCell'] ?? '';
        $notesTypeInfoArray = $ip['notesTypeInfoArray'] ?? '';
        $recordDate = Dates::Timestamp();

        $processingCompany = $processingCompanyId ? tblProcessingCompany::Get(['PCID' => $processingCompanyId]) : new tblProcessingCompany();
        $processingCompany->allowToViewFSSF = Request::isset('allowToViewFSSF') ? Request::GetClean('allowToViewFSSF') : ($processingCompanyId ? $processingCompany->allowToViewFSSF : 0);
        $processingCompany->loadMenu = Request::GetClean('loadMenu') ? Request::GetClean('loadMenu') : ($processingCompanyId ? $processingCompany->loadMenu : 'v');
        $processingCompany->allowWebformToPDF = Request::isset('allowWebformToPDF') ? Request::GetClean('allowWebformToPDF') : ($processingCompanyId ? $processingCompany->allowWebformToPDF : 1);
        $processingCompany->processingCompanyName = Request::isset('processingCompanyName') ? Strings::stripQuote(Request::GetClean('processingCompanyName')) : ($processingCompanyId ? $processingCompany->processingCompanyName : null);
        $processingCompany->attorneyFName = Request::isset('attorneyFName') ? Strings::stripQuote(Request::GetClean('attorneyFName')) : null;
        $processingCompany->attorneyLName = Request::isset('attorneyLName') ? Strings::stripQuote(Request::GetClean('attorneyLName')) : null;
        $processingCompany->attorneyMName = Request::isset('attorneyMName') ? Strings::stripQuote(Request::GetClean('attorneyMName')) : null;

        $processingCompany->attorneyTelephone = Request::isset('phoneNumber')
            ? str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], Request::GetClean('phoneNumber'))
            : ($processingCompanyId ? $processingCompany->attorneyTelephone : null);


        $processingCompany->servicerPhone = Request::isset('servicePhone')
            ? str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], Request::GetClean('servicePhone'))
            : ($processingCompanyId ? $processingCompany->servicerPhone : null);


        $processingCompany->payoffPhoneNo = Request::isset('payoffPhoneNo')
            ? str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], Request::GetClean('payoffPhoneNo'))
            : ($processingCompanyId ? $processingCompany->payoffPhoneNo : null);

        if (array_key_exists('attorneyCell', $ip)) {
            $processingCompany->attorneyCell = $attorneyCell;
        }
        $processingCompany->attorneyFascimile = $fax;

        $processingCompany->attorneyAddress = Request::isset('address1') ? Request::GetClean('address1') : ($processingCompanyId ? $processingCompany->attorneyAddress : null);
        $processingCompany->attorneyCity = Request::isset('city') ? Request::GetClean('city') : ($processingCompanyId ? $processingCompany->attorneyCity : null);
        $processingCompany->attorneyState = Request::isset('state') ? Request::GetClean('state') : ($processingCompanyId ? $processingCompany->attorneyState : null);
        $processingCompany->county = Request::isset('county') ? Request::GetClean('county') : ($processingCompanyId ? $processingCompany->county : null);
        $processingCompany->attorneyZipCode = Request::isset('zipCode') ? Request::GetClean('zipCode') : ($processingCompanyId ? $processingCompany->attorneyZipCode : null);
        if (!$processingCompanyId) {
            $processingCompany->recordDate = $recordDate;
        }
        if ($processingCompanyId) {
            $processingCompany->updatedAt = $recordDate;
            $processingCompany->updatedBy = PageVariables::$userName ?? 'chargebee';
        }
        $processingCompany->attorneyEmail = $attorneyEmail;
        $processingCompany->processingCompanyWebsite = Request::isset('processingCompanyWebsite') ? Request::GetClean('processingCompanyWebsite') : ($processingCompanyId ? $processingCompany->processingCompanyWebsite : null);
        $processingCompany->allowToCreateBranch = Request::isset('allowToCreateBranch')
            ? Request::GetClean('allowToCreateBranch') : ($processingCompanyId ? $processingCompany->allowToCreateBranch : 1);

        $processingCompany->allowToCreateEmployee = Request::isset('allowToCreateEmployee')
            ? intval(Request::GetClean('allowToCreateEmployee')) :
            ($processingCompanyId ? $processingCompany->allowToCreateEmployee : 1);

        $processingCompany->timeZone = Request::isset('timeZone')
            ? Request::GetClean('timeZone') :
            ($processingCompanyId ? $processingCompany->timeZone : '');

        $processingCompany->PCARBDuration = Request::isset('PCARBDuration')
            ? Request::GetClean('PCARBDuration') :
            ($processingCompanyId ? $processingCompany->PCARBDuration : null);

        $processingCompany->allowToOnOffAgentLogin = Request::isset('allowToOnOffAgentLogin')
            ? intval(Request::GetClean('allowToOnOffAgentLogin')) :
            ($processingCompanyId ? $processingCompany->allowToOnOffAgentLogin : 1);

        $processingCompany->notesType = $notesTypeInfoArray;

        $processingCompany->showSysGenNote = Request::isset('showSysGenNote')
            ? intval(Request::GetClean('showSysGenNote')) :
            ($processingCompanyId ? $processingCompany->showSysGenNote : 1);

        $processingCompany->useMyNameAndEmail = Request::isset('useMyNameAndEmail')
            ? intval(Request::GetClean('useMyNameAndEmail')) :
            ($processingCompanyId ? $processingCompany->useMyNameAndEmail : 0);

        $processingCompany->regnFor = Request::isset('regnFor')
            ? Request::GetClean('regnFor') :
            ($processingCompanyId ? $processingCompany->regnFor : null);

        $processingCompany->doingLM = Request::isset('doingLM')
            ? Request::GetClean('doingLM') :
            ($processingCompanyId ? $processingCompany->doingLM : null);

        $processingCompany->totalPotentialUsers = Request::isset('totalUsers')
            ? intval(Request::GetClean('totalUsers')) :
            ($processingCompanyId ? $processingCompany->totalPotentialUsers : 0);

        $processingCompany->companyGoals = Request::isset('companyGoals')
            ? Request::GetClean('companyGoals') :
            ($processingCompanyId ? $processingCompany->companyGoals : null);

        $processingCompany->nUsers = Request::isset('nUsers')
            ? intval(Request::GetClean('nUsers')) :
            ($processingCompanyId ? $processingCompany->nUsers : 0);

        $processingCompany->salesRep = Request::isset('salesRep')
            ? Request::GetClean('salesRep') :
            ($processingCompanyId ? $processingCompany->salesRep : null);

        $processingCompany->serverInfoUserSetting = Request::isset('serverInfoUserSetting')
            ? intval(Request::GetClean('serverInfoUserSetting')) :
            ($processingCompanyId ? $processingCompany->serverInfoUserSetting : 0);

        $processingCompany->subscriberID = Request::isset('subscriberID')
            ? Request::GetClean('subscriberID') :
            ($processingCompanyId ? $processingCompany->subscriberID : null);

        $processingCompany->customerID = Request::isset('customerID')
            ? Request::GetClean('customerID') :
            ($processingCompanyId ? $processingCompany->customerID : null);

        $processingCompany->allowToEditLenderList = Request::isset('allowToEditLenderList')
            ? intval(Request::GetClean('allowToEditLenderList')) :
            ($processingCompanyId ? $processingCompany->allowToEditLenderList : 0);

        $processingCompany->allowPCToViewAddOn = Request::isset('allowPCToViewAddOn')
            ? intval(Request::GetClean('allowPCToViewAddOn')) :
            ($processingCompanyId ? $processingCompany->allowPCToViewAddOn : 1);

        $processingCompany->allowESignService = Request::isset('allowESignService')
            ? intval(Request::GetClean('allowESignService')) :
            ($processingCompanyId ? $processingCompany->allowESignService : 0);

        $processingCompany->sendMassEmailToClient = Request::isset('sendMassEmailToClient')
            ? intval(Request::GetClean('sendMassEmailToClient')) :
            ($processingCompanyId ? $processingCompany->sendMassEmailToClient : 0);

        $processingCompany->allowUBAToUpAndDowngradeClient = Request::isset('allowUBAToUpAndDowngradeClient')
            ? intval(Request::GetClean('allowUBAToUpAndDowngradeClient')) :
            ($processingCompanyId ? $processingCompany->allowUBAToUpAndDowngradeClient : 1);

        $processingCompany->EINNumber = Request::isset('EINNumber')
            ? Strings::NumbersOnly(Request::GetClean('EINNumber')) :
            ($processingCompanyId ? $processingCompany->EINNumber : null);


        $processingCompany->websiteTemplate = Request::isset('websiteTemplate')
            ? Request::GetClean('websiteTemplate') :
            ($processingCompanyId ? $processingCompany->websiteTemplate : null);

        $processingCompany->privateLabeled = Request::isset('privateLabeled')
            ? Request::GetClean('privateLabeled') :
            ($processingCompanyId ? $processingCompany->privateLabeled : null);

        $processingCompany->sendFax = Request::isset('sendFax')
            ? intval(Request::GetClean('sendFax')) :
            ($processingCompanyId ? $processingCompany->sendFax : 0);

        $processingCompany->allowEmailCampaign = Request::isset('emailCampaign')
            ? intval(Request::GetClean('emailCampaign')) :
            ($processingCompanyId ? $processingCompany->allowEmailCampaign : 0);

        $processingCompany->isPLO = Request::isset('isPLO')
            ? intval(Request::GetClean('isPLO')) :
            ($processingCompanyId ? $processingCompany->isPLO : 0);

        $processingCompany->isWebsite = Request::isset('isWebsite')
            ? intval(Request::GetClean('isWebsite')) :
            ($processingCompanyId ? $processingCompany->isWebsite : 0);

        $processingCompany->pcAcqualifyStatus = Request::isset('pcAcqualifyStatus')
            ? intval(Request::GetClean('pcAcqualifyStatus')) :
            ($processingCompanyId ? $processingCompany->pcAcqualifyStatus : 0);

        $processingCompany->pcPriceEngineStatus = Request::isset('pcPriceEngineStatus')
            ? intval(Request::GetClean('pcPriceEngineStatus')) :
            ($processingCompanyId ? $processingCompany->pcPriceEngineStatus : 0);

        $processingCompany->allowAutomation = Request::isset('allowAutomation')
            ? intval(Request::GetClean('allowAutomation')) :
            ($processingCompanyId ? $processingCompany->allowAutomation : 0);


        $processingCompany->allowServicing = Request::isset('allowServicing')
            ? intval(Request::GetClean('allowServicing')) :
            ($processingCompanyId ? $processingCompany->allowServicing : 0);

        $processingCompany->subscribeToREST = Request::isset('subscribeToREST')
            ? intval(Request::GetClean('subscribeToREST')) :
            ($processingCompanyId ? $processingCompany->subscribeToREST : 0);

        $processingCompany->allow3WayCall = Request::isset('allow3WayCall')
            ? intval(Request::GetClean('allow3WayCall')) :
            ($processingCompanyId ? $processingCompany->allow3WayCall : 1);

        $processingCompany->faxInfoUserSetting = Request::isset('faxInfoUserSetting')
            ? intval(Request::GetClean('faxInfoUserSetting')) :
            ($processingCompanyId ? $processingCompany->faxInfoUserSetting : 0);

        $processingCompany->allowToLASubmitForPC = Request::isset('allowToLASubmitForPC')
            ? intval(Request::GetClean('allowToLASubmitForPC')) :
            ($processingCompanyId ? $processingCompany->allowToLASubmitForPC : 0);

        $processingCompany->allowToCFPBSubmitForPC = Request::isset('allowToCFPBSubmitForPC')
            ? intval(Request::GetClean('allowToCFPBSubmitForPC')) :
            ($processingCompanyId ? $processingCompany->allowToCFPBSubmitForPC : 0);

        $processingCompany->allowPCUserToSubmitCFPB = Request::isset('allowPCUserToSubmitCFPB')
            ? intval(Request::GetClean('allowPCUserToSubmitCFPB')) :
            ($processingCompanyId ? $processingCompany->allowPCUserToSubmitCFPB : 1);

        $processingCompany->subscribeToHOME = Request::isset('subscribeToHOME')
            ? intval(Request::GetClean('subscribeToHOME')) :
            ($processingCompanyId ? $processingCompany->subscribeToHOME : 0);

        $processingCompany->HRFee = Request::isset('homeReportFee')
            ? intval(Request::GetClean('homeReportFee')) :
            ($processingCompanyId ? $processingCompany->HRFee : 0);

        $processingCompany->theme = Request::isset('theme')
            ? intval(Request::GetClean('theme')) :
            ($processingCompanyId ? $processingCompany->theme : 1);

        $processingCompany->isSysNotesPrivate = Request::isset('isSysNotesPrivate')
            ? intval(Request::GetClean('isSysNotesPrivate')) :
            ($processingCompanyId ? $processingCompany->isSysNotesPrivate : 0);

        $processingCompany->allowPCToSendCustomText = Request::isset('allowPCToSendCustomText')
            ? intval(Request::GetClean('allowPCToSendCustomText')) :
            ($processingCompanyId ? $processingCompany->allowPCToSendCustomText : 0);

        $processingCompany->lenderPayableInfo = Request::isset('lenderPayableInfo')
            ? rawurlencode(Strings::stripQuote(Request::GetClean('lenderPayableInfo'))) :
            ($processingCompanyId ? $processingCompany->lenderPayableInfo : null);

        $processingCompany->showFSCntInDash = Request::isset('showFSCntInDash')
            ? intval(Request::GetClean('showFSCntInDash')) :
            ($processingCompanyId ? $processingCompany->showFSCntInDash : 0);

        $processingCompany->showStateMapInDash = Request::isset('showStateMapInDash')
            ? intval(Request::GetClean('showStateMapInDash')) :
            ($processingCompanyId ? $processingCompany->showStateMapInDash : 1);

        $processingCompany->stateOfIncorporation = Request::isset('stateOfIncorporation')
            ? Request::GetClean('stateOfIncorporation') :
            ($processingCompanyId ? $processingCompany->stateOfIncorporation : null);

        $processingCompany->servicerName = Request::isset('servicerName')
            ? Request::GetClean('servicerName') :
            ($processingCompanyId ? $processingCompany->servicerName : null);

        $processingCompany->servicerAddress = Request::isset('servicerAddress')
            ? Request::GetClean('servicerAddress') :
            ($processingCompanyId ? $processingCompany->servicerAddress : null);

        $processingCompany->servicerEmail = Request::isset('servicerEmail')
            ? Request::GetClean('servicerEmail') :
            ($processingCompanyId ? $processingCompany->servicerEmail : null);

        $processingCompany->payOffRequestEmail = Request::isset('payOffRequestEmail')
            ? Request::GetClean('payOffRequestEmail') :
            ($processingCompanyId ? $processingCompany->payOffRequestEmail : null);

        $processingCompany->borrowerLoginURL = Request::isset('borrowerLoginURL')
            ? Strings::stripQuote(Request::GetClean('borrowerLoginURL')) :
            ($processingCompanyId ? $processingCompany->borrowerLoginURL : null);

        $processingCompany->NMLSID = Request::isset('NMLSID')
            ? Request::GetClean('NMLSID') :
            ($processingCompanyId ? $processingCompany->NMLSID : null);

        $processingCompany->MERSID = Request::isset('MERSID')
            ? Request::GetClean('MERSID') :
            ($processingCompanyId ? $processingCompany->MERSID : null);

        $processingCompany->legalEntityIdentifier = Request::isset('legalEntityIdentifier')
            ? Request::GetClean('legalEntityIdentifier') :
            ($processingCompanyId ? $processingCompany->legalEntityIdentifier : null);

        $processingCompany->loanNumberPrefix = Request::isset('loanNumberPrefix')
            ? intval(Request::GetClean('loanNumberPrefix')) :
            ($processingCompanyId ? $processingCompany->loanNumberPrefix : 0);

        $processingCompany->showStartLoanNumber = Request::isset('showStartLoanNumber')
            ? intval(Request::GetClean('showStartLoanNumber')) :
            ($processingCompanyId ? $processingCompany->showStartLoanNumber : 0);

        $processingCompany->adminUserName = Request::isset('adminUserName')
            ? Request::GetClean('adminUserName') :
            ($processingCompanyId ? $processingCompany->adminUserName : null);

        $processingCompany->adminUserTitle = Request::isset('adminUserTitle')
            ? Request::GetClean('adminUserTitle') :
            ($processingCompanyId ? $processingCompany->adminUserTitle : null);

        $processingCompany->userSignEmail = Request::isset('userSignEmail')
            ? intval(Request::GetClean('userSignEmail')) :
            ($processingCompanyId ? $processingCompany->userSignEmail : 0);

        $processingCompany->leadPostingAPI = Request::isset('leadPostingAPI')
            ? intval(Request::GetClean('leadPostingAPI')) :
            ($processingCompanyId ? $processingCompany->leadPostingAPI : 0);

        $processingCompany->pcGlobalEmails = Request::isset('pcGlobalEmails')
            ? Strings::stripQuote(Request::GetClean('pcGlobalEmails')) : ($processingCompanyId ? $processingCompany->pcGlobalEmails : '');

        $processingCompany->allowPCToMarketPlace = Request::isset('allowPCToMarketPlace')
            ? intval(Request::GetClean('allowPCToMarketPlace')) :
            ($processingCompanyId ? $processingCompany->allowPCToMarketPlace : 1);

        $processingCompany->allowPCToMarketPlacePublic = Request::isset('allowPCToMarketPlacePublic')
            ? intval(Request::GetClean('allowPCToMarketPlacePublic')) :
            ($processingCompanyId ? $processingCompany->allowPCToMarketPlacePublic : 0);

        $processingCompany->allowPCToAccessPublicMarketPlaceLoanPrograms = Request::isset('allowPCToAccessPublicMarketPlaceLoanPrograms')
            ? intval(Request::GetClean('allowPCToAccessPublicMarketPlaceLoanPrograms')) :
            ($processingCompanyId ? $processingCompany->allowPCToAccessPublicMarketPlaceLoanPrograms : 1);

        if (Request::isset('allowCustomFields')) {
            $processingCompany->allowCustomFields = intval(Request::GetClean('allowCustomFields') ?? null);
        }
        if (Request::isset('nEmps')) {
            $processingCompany->nEmps = intval(Request::GetClean('nEmps') ?? null);
        }
        if (Request::isset('nBranches')) {
            $processingCompany->nBranches = intval(Request::GetClean('nBranches') ?? null);
        }
        if (Request::isset('nDIYs')) {
            $processingCompany->nDIYs = intval(Request::GetClean('nDIYS') ?? null);
        }
        if (Request::isset('nAgents')) {
            $processingCompany->nAgents = intval(Request::GetClean('nAgents') ?? 0);
        }
        if (Request::isset('userRegEmail')) {
            $processingCompany->userRegEmail = Request::GetClean('userRegEmail') ?? null;
        }

        if (array_key_exists('ws', $ip)) {
            $processingCompany->dStatus = 1;
        }
        if (Request::isset('client_id')) {
            $processingCompany->client_id = Request::GetClean('client_id');
        }
        if (Request::isset('client_secret')) {
            $processingCompany->client_secret = Request::GetClean('client_secret');
        }
        if (Request::isset('allowPeerstreet')) {
            $processingCompany->allowPeerstreet = intval(Request::GetClean('allowPeerstreet') ?? null);
        }
        if (Request::isset('allowToCreateAloware')) {
            $processingCompany->allowToCreateAloware = intval(Request::GetClean('allowToCreateAloware') ?? 0);
        }
        if (Request::isset('thirdPartyServices')) {
            $processingCompany->thirdPartyServices = intval(Request::GetClean('thirdPartyServices'));
        }
        if (Request::isset('thirdPartyServiceCSR')) {
            $processingCompany->thirdPartyServiceCSR = implode(',', Request::GetClean('thirdPartyServiceCSR'));
        }
        if (Request::isset('thirdPartyServicesProducts')) {
            $processingCompany->thirdPartyServicesProducts = implode(',', Request::GetClean('thirdPartyServicesProducts'));
        }
        $processingCompany->nUsersAgent = Request::isset('nUsersAgent') ? intval(Request::GetClean('nUsersAgent')) : ($processingCompanyId ? $processingCompany->nUsersAgent : 0);
        if (Request::isset('zohoLink')) {
            $processingCompany->zohoLink = Request::GetClean('zohoLink') ?? null;
        }
        if (Request::isset('drive')) {
            $processingCompany->drive = Request::GetClean('drive') ?? null;
        }
        if (Request::isset('planType')) {
            $processingCompany->planType = Request::GetClean('planType') ?? null;
        }
        if (Request::isset('captcha')) {
            $processingCompany->captcha = intval(Request::GetClean('captcha') ?? 0);
        }
        if (Request::isset('allowPropertyAddressAutoLookUp')) {
            $processingCompany->allowPropertyAddressAutoLookUp = intval(Request::GetClean('allowPropertyAddressAutoLookUp') ?? null);
        }
        if (Request::isset('nonInclusivePerDiem')) {
            $processingCompany->nonInclusivePerDiem = intval(Request::GetClean('nonInclusivePerDiem') ?? null);
        }
        if (Request::isset('allowNestedEntityMembers')) {
            $processingCompany->allowNestedEntityMembers = intval(Request::GetClean('allowNestedEntityMembers') ?? null);
        }
        if (Request::isset('docWizard')) {
            $processingCompany->docWizard = intval(Request::GetClean('docWizard') ?? null);
        }
        if (Request::isset('VIPSupport')) {
            $processingCompany->VIPSupport = intval(Request::GetClean('VIPSupport') ?? null);
        }
        if (Request::isset('GHL')) {
            $processingCompany->GHL = Request::GetClean('GHL') ?? null;
        }
        if (Request::isset('CSMRep')) {
            $processingCompany->CSMRep = Request::GetClean('CSMRep') ?? null;
        }
        if (Request::isset('identifierForMinAutoGeneration')) {
            $processingCompany->identifierForMinAutoGeneration = intval(Request::GetClean('identifierForMinAutoGeneration') ?? null);
        }
        if (Request::isset('trackerEnabled')) {
            $processingCompany->enableLoanTracker = intval(Request::GetClean('trackerEnabled') ?? 0);
        }
        if (Request::isset('drawManagement')) {
            $processingCompany->drawManagement = intval(Request::GetClean('drawManagement') ?? null);
        }
        if (Request::isset('enableDrawManagementV2')) {
            $processingCompany->enableDrawManagementV2 = intval(Request::GetClean('enableDrawManagementV2') ?? null);
        }
        $vhost = Request::isset('vhost') ? Request::GetClean('vhost') : ($processingCompanyId ? $processingCompany->vhost : CONST_SITE_URL);
        $processingCompany->vhost = Strings::validateVhost($vhost);
        $processingCompany->Save();
        $resultArray['insertCount'] = $processingCompanyId ? 0 : 1;
        $resultArray['updateCount'] = $processingCompanyId ? 1 : 0;
        $resultArray['isInsert'] = $processingCompanyId ? 'No' : 'Yes';
        $resultArray['PCID'] = $processingCompany->PCID;

        return $resultArray;
    }
}
