<?php

namespace models\composite\oBroker;

use models\composite\oThirdPartyServices\saveThirdPartyServicesUserDetails;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\Database2;
use models\composite\oEmployee\saveUserAssignedWorkflow;
use models\lendingwise\tblAgent;
use models\Request;
use models\standard\Strings;
use models\types\strongType;


/**
 *
 */
class saveAgentInfo extends strongType
{

    private static function setAgentValues(tblAgent $tblAgent, array $ip)
    {
        $tblAgent->company = Strings::stripQuote($ip['company']);
        $tblAgent->city = $ip['city'];
        $tblAgent->firstName = Strings::stripQuote($ip['firstName']);
        $tblAgent->email = trim($ip['email']);
        if ($ip['pwd']) {
            $tblAgent->pwd = trim($ip['pwd']);
        }
        $tblAgent->promoCode = trim($ip['promoCode']);
        $tblAgent->registerDate = trim($ip['registerDate']);
        $tblAgent->agreedTC = intval($ip['agreedTC']) ?? 0;


        if (array_key_exists('lastName', $ip)) {
            $tblAgent->lastName = trim(Strings::stripQuote($ip['lastName']));
        } else {
            $tblAgent->lastName = $tblAgent->lastName ?? '';
        }
        if (array_key_exists('phoneNumber', $ip)) {
            $tblAgent->phoneNumber = trim($ip['phoneNumber']);
        } else {
            $tblAgent->phoneNumber = $tblAgent->phoneNumber ?? '';
        }
        if (array_key_exists('cellNumber', $ip)) {
            $tblAgent->cellNumber = trim($ip['cellNumber']);
        } else {
            $tblAgent->cellNumber = $tblAgent->cellNumber ?? '';
        }
        if (array_key_exists('fax', $ip)) {
            $tblAgent->fax = trim($ip['fax']);
        } else {
            $tblAgent->fax = $tblAgent->fax ?? '';
        }
        if (array_key_exists('address', $ip)) {
            $tblAgent->addr = trim($ip['address']);
        } else {
            $tblAgent->addr = $tblAgent->addr ?? '';
        }
        if (array_key_exists('addr', $ip)) {
            $tblAgent->addr = trim($ip['addr']);
        } else {
            $tblAgent->addr = $tblAgent->addr ?? '';
        }
        if (array_key_exists('suiteNumber', $ip)) {
            $tblAgent->suiteNumber = trim($ip['suiteNumber']);
        } else {
            $tblAgent->suiteNumber = $tblAgent->suiteNumber ?? '';
        }
        if (array_key_exists('state', $ip)) {
            $tblAgent->state = trim($ip['state']);
        } else {
            $tblAgent->state = $tblAgent->state ?? '';
        }
        if (array_key_exists('zipCode', $ip)) {
            $tblAgent->zipCode = trim($ip['zipCode']);
        } else {
            $tblAgent->zipCode = $tblAgent->zipCode ?? '';
        }
        if (array_key_exists('serviceProvider', $ip)) {
            $tblAgent->serviceProvider = trim($ip['serviceProvider']);
        } else {
            $tblAgent->serviceProvider = $tblAgent->serviceProvider ?? '';
        }
        if (array_key_exists('allowToCreateAloware', $ip)) {
            $tblAgent->allowToCreateAloware = intval($ip['allowToCreateAloware']);
        } else {
            $tblAgent->allowToCreateAloware = $tblAgent->allowToCreateAloware ?? 0;
        }
        if (array_key_exists('allowedToUpdateFiles', $ip)) {
            $tblAgent->allowedToUpdateFiles = intval($ip['allowedToUpdateFiles']);
        } else {
            $tblAgent->allowedToUpdateFiles = $tblAgent->allowedToUpdateFiles ?? 0;
        }
        if (array_key_exists('allowToAccessPrivateNotes', $ip)) {
            $tblAgent->allowToAccessPrivateNotes = intval($ip['allowToAccessPrivateNotes']);
        } else {
            $tblAgent->allowToAccessPrivateNotes = $tblAgent->allowToAccessPrivateNotes ?? 0;
        }
        if (array_key_exists('allowAgentToLogin', $ip)) {
            $tblAgent->allowAgentToLogin = intval($ip['allowAgentToLogin']);
        } else {
            $tblAgent->allowAgentToLogin = $tblAgent->allowAgentToLogin ?? 0;
        }
        if (array_key_exists('webAddress', $ip)) {
            $tblAgent->url = trim($ip['webAddress']);
        } else {
            $tblAgent->url = $tblAgent->url ?? '';
        }
        if (array_key_exists('allowFreeLMR', $ip)) {
            $tblAgent->allowFreeLMR = intval($ip['allowFreeLMR']);
        } else {
            $tblAgent->allowFreeLMR = $tblAgent->allowFreeLMR ?? 0;
        }
        if (array_key_exists('sendNewDealAlert', $ip)) {
            $tblAgent->sendNewDealAlert = intval($ip['sendNewDealAlert']);
        } else {
            $tblAgent->sendNewDealAlert = $tblAgent->sendNewDealAlert ?? 1;
        }
        if (array_key_exists('receiveUpdates', $ip)) {
            $tblAgent->receiveUpdates = intval($ip['receiveUpdates']);
        } else {
            $tblAgent->receiveUpdates = $tblAgent->receiveUpdates ?? 1;
        }
        if (array_key_exists('allowToSendMassEmail', $ip)) {
            $tblAgent->allowToSendMassEmail = intval($ip['allowToSendMassEmail']);
        } else {
            $tblAgent->allowToSendMassEmail = $tblAgent->allowToSendMassEmail ?? 1;
        }
        if (array_key_exists('allowAgentToEditLMRFile', $ip)) {
            $tblAgent->allowAgentToEditLMRFile = intval($ip['allowAgentToEditLMRFile']);
        } else {
            $tblAgent->allowAgentToEditLMRFile = $tblAgent->allowAgentToEditLMRFile ?? 0;
        }
        if (array_key_exists('allowAgentToAccessLMRDocs', $ip)) {
            $tblAgent->allowAgentToAccessLMRDocs = intval($ip['allowAgentToAccessLMRDocs']);
        } else {
            $tblAgent->allowAgentToAccessLMRDocs = $tblAgent->allowAgentToAccessLMRDocs ?? 1;
        }
        if (array_key_exists('allowAgentToSendHomeownerLink', $ip)) {
            $tblAgent->allowAgentToSendHomeownerLink = intval($ip['listAllAgents']);
        } else {
            $tblAgent->allowAgentToSendHomeownerLink = $tblAgent->allowAgentToSendHomeownerLink ?? 1;
        }
        if (array_key_exists('allowAgentToCreateFiles', $ip)) {
            $tblAgent->allowAgentToCreateFiles = intval($ip['allowAgentToCreateFiles']);
        } else {
            $tblAgent->allowAgentToCreateFiles = $tblAgent->allowAgentToCreateFiles ?? 1;
        }
        if (array_key_exists('allowAgentToCreateTasks', $ip)) {
            $tblAgent->allowAgentToCreateTasks = intval($ip['allowAgentToCreateTasks']);
        } else {
            $tblAgent->allowAgentToCreateTasks = $tblAgent->allowAgentToCreateTasks ?? 1;
        }
        if (array_key_exists('allowAgentToSeeDashboard', $ip)) {
            $tblAgent->allowAgentToSeeDashboard = intval($ip['allowAgentToSeeDashboard']);
        } else {
            $tblAgent->allowAgentToSeeDashboard = $tblAgent->allowAgentToSeeDashboard ?? 1;
        }
        if (array_key_exists('accessRestriction', $ip)) {
            $tblAgent->accessRestriction = intval($ip['accessRestriction']);
        } else {
            $tblAgent->accessRestriction = $tblAgent->accessRestriction ?? 0;
        }
        if (array_key_exists('allowedToDeleteUplodedDocs', $ip)) {
            $tblAgent->allowedToDeleteUplodedDocs = intval($ip['allowedToDeleteUplodedDocs']);
        } else {
            $tblAgent->allowedToDeleteUplodedDocs = $tblAgent->allowedToDeleteUplodedDocs ?? 1;
        }
        if (array_key_exists('allowedToEditOwnNotes', $ip)) {
            $tblAgent->allowedToEditOwnNotes = intval($ip['allowedToEditOwnNotes']);
        } else {
            $tblAgent->allowedToEditOwnNotes = $tblAgent->allowedToEditOwnNotes ?? 1;
        }
        if (array_key_exists('seeBilling', $ip)) {
            $tblAgent->seeBilling = intval($ip['seeBilling']);
        } else {
            $tblAgent->seeBilling = $tblAgent->seeBilling ?? 1;
        }
        if (array_key_exists('allowToEditCCInfo', $ip)) {
            $tblAgent->allowToEditCCInfo = intval($ip['allowToEditCCInfo']);
        } else {
            $tblAgent->allowToEditCCInfo = $tblAgent->allowToEditCCInfo ?? 0;
        }
        if (array_key_exists('permissionToREST', $ip)) {
            $tblAgent->permissionToREST = intval($ip['permissionToREST']);
        } else {
            $tblAgent->permissionToREST = $tblAgent->permissionToREST ?? 0;
        }
        if (array_key_exists('subscribeToHOME', $ip)) {
            $tblAgent->subscribeToHOME = intval($ip['subscribeToHOME']);
        } else {
            $tblAgent->subscribeToHOME = $tblAgent->subscribeToHOME ?? 0;
        }
        if (array_key_exists('allowedToExcelReport', $ip)) {
            $tblAgent->allowedToExcelReport = intval($ip['allowedToExcelReport']);
        } else {
            $tblAgent->allowedToExcelReport = $tblAgent->allowedToExcelReport ?? 0;
        }
        if (array_key_exists('changeDIYPlan', $ip)) {
            $tblAgent->changeDIYPlan = intval($ip['changeDIYPlan']);
        } else {
            $tblAgent->changeDIYPlan = $tblAgent->changeDIYPlan ?? 1;
        }
        if (array_key_exists('timeZone', $ip)) {
            $tblAgent->timeZone = trim($ip['timeZone']);
        } else {
            $tblAgent->timeZone = $tblAgent->timeZone ?? 'EST';
        }
        if (array_key_exists('isPrimary', $ip)) {
            $tblAgent->isPrimary = intval($ip['isPrimary']);
        } else {
            $tblAgent->isPrimary = $tblAgent->isPrimary ?? 0;
        }
        if (array_key_exists('allowToLASubmit', $ip)) {
            $tblAgent->allowToLASubmit = intval($ip['allowToLASubmit']);
        } else {
            $tblAgent->allowToLASubmit = $tblAgent->allowToLASubmit ?? 0;
        }

        if (array_key_exists('allowToCFPBSubmit', $ip)) {
            $tblAgent->allowToCFPBSubmit = intval($ip['allowToCFPBSubmit']);
        } else {
            $tblAgent->allowToCFPBSubmit = $tblAgent->allowToCFPBSubmit ?? 0;
        }
        if (array_key_exists('allowEmailCampaign', $ip)) {
            $tblAgent->allowEmailCampaign = intval($ip['allowEmailCampaign']);
        } else {
            $tblAgent->allowEmailCampaign = $tblAgent->allowEmailCampaign ?? 1;
        }
        if (array_key_exists('allowToSendFax', $ip)) {
            $tblAgent->allowToSendFax = intval($ip['allowToSendFax']);
        } else {
            $tblAgent->allowToSendFax = $tblAgent->allowToSendFax ?? 0;
        }

        if (array_key_exists('allowAgentToEditCommission', $ip)) {
            $tblAgent->allowAgentToEditCommission = intval($ip['allowAgentToEditCommission']);
        } else {
            $tblAgent->allowAgentToEditCommission = $tblAgent->allowAgentToEditCommission ?? 1;
        }
        if (array_key_exists('allowAgentToSeeCommission', $ip)) {
            $tblAgent->allowAgentToSeeCommission = intval($ip['allowAgentToSeeCommission']);
        } else {
            $tblAgent->allowAgentToSeeCommission = $tblAgent->allowAgentToSeeCommission ?? 1;
        }
        if (array_key_exists('allowToSendFileDesignation', $ip)) {
            $tblAgent->allowToSendFileDesignation = intval($ip['allowToSendFileDesignation']);
        } else {
            $tblAgent->allowToSendFileDesignation = $tblAgent->allowToSendFileDesignation ?? 1;
        }
        if (array_key_exists('allowAgentToSeePublicNotes', $ip)) {
            $tblAgent->allowAgentToSeePublicNotes = intval($ip['allowAgentToSeePublicNotes']);
        } else {
            $tblAgent->allowAgentToSeePublicNotes = $tblAgent->allowAgentToSeePublicNotes ?? 1;
        }
        if (array_key_exists('allowToViewCFPBPipeline', $ip)) {
            $tblAgent->allowToViewCFPBPipeline = intval($ip['allowToViewCFPBPipeline']);
        } else {
            $tblAgent->allowToViewCFPBPipeline = $tblAgent->allowToViewCFPBPipeline ?? 0;
        }
        if (array_key_exists('allowToAccessRAM', $ip)) {
            $tblAgent->allowToAccessRAM = intval($ip['allowToAccessRAM']);
        } else {
            $tblAgent->allowToAccessRAM = $tblAgent->allowToAccessRAM ?? 0;
        }
        if (array_key_exists('allowToSendMarketingEmailForBRBO', $ip)) {
            $tblAgent->allowToSendMarketingEmailForBRBO = intval($ip['allowToSendMarketingEmailForBRBO']);
        } else {
            $tblAgent->allowToSendMarketingEmailForBRBO = $tblAgent->allowToSendMarketingEmailForBRBO ?? 0;
        }

        if (array_key_exists('allowToSeeWebForms', $ip)) {
            $tblAgent->allowToSeeWebForms = intval($ip['allowToSeeWebForms']);
        } else {
            $tblAgent->allowToSeeWebForms = $tblAgent->allowToSeeWebForms ?? 0;
        }

        if (array_key_exists('TAC', $ip)) {
            $tblAgent->TAC = trim($ip['TAC']);
        } else {
            $tblAgent->TAC = $tblAgent->TAC ?? '';
        }
        if (array_key_exists('license', $ip)) {
            $tblAgent->license = trim($ip['license']);
        } else {
            $tblAgent->license = $tblAgent->license ?? '';
        }
        if (array_key_exists('eniNumber', $ip)) {
            $tblAgent->eniNumber = trim($ip['eniNumber']);
        } else {
            $tblAgent->eniNumber = $tblAgent->eniNumber ?? '';
        }
        if (array_key_exists('brokerPartnerType', $ip)) {
            $tblAgent->brokerPartnerType = trim($ip['brokerPartnerType']);
        } else {
            $tblAgent->brokerPartnerType = $tblAgent->brokerPartnerType ?? '';
        }


        if (array_key_exists('prefCommunication', $ip)) {
            $tblAgent->prefCommunication = trim($ip['prefCommunication']);
        } else {
            $tblAgent->prefCommunication = $tblAgent->prefCommunication ?? '';
        }
        if (array_key_exists('allowEditToIR', $ip)) {
            $tblAgent->allowEditToIR = intval($ip['allowEditToIR']);
        } else {
            $tblAgent->allowEditToIR = $tblAgent->allowEditToIR ?? 0;
        }
        if (array_key_exists('allowWorkflowEdit', $ip)) {
            $tblAgent->allowWorkflowEdit = intval($ip['allowWorkflowEdit']);
        } else {
            $tblAgent->allowWorkflowEdit = $tblAgent->allowWorkflowEdit ?? 1;
        }

        if (array_key_exists('allowAgentToGetBorrowerUploadDocsNotification', $ip)) {
            $tblAgent->allowAgentToGetBorrowerUploadDocsNotification = intval($ip['allowAgentToGetBorrowerUploadDocsNotification']);
        } else {
            $tblAgent->allowAgentToGetBorrowerUploadDocsNotification = $tblAgent->allowAgentToGetBorrowerUploadDocsNotification ?? 1;
        }

        if (array_key_exists('allowToViewMarketPlace', $ip)) {
            $tblAgent->allowToViewMarketPlace = intval($ip['allowToViewMarketPlace']);
        } else {
            $tblAgent->allowToViewMarketPlace = $tblAgent->allowToViewMarketPlace ?? 1;
        }

        if (array_key_exists('allowServicing', $ip)) {
            $tblAgent->allowServicing = intval($ip['allowServicing']);
        } else {
            $tblAgent->allowServicing = $tblAgent->allowServicing ?? 0;
        }

        if (array_key_exists('allowToLockLoanFileAgent', $ip)) {
            $tblAgent->allowToLockLoanFileAgent = intval($ip['allowToLockLoanFileAgent']);
        } else {
            $tblAgent->allowToLockLoanFileAgent = $tblAgent->allowToLockLoanFileAgent ?? 10;
        }

        if (isset($_REQUEST['allowToupdateFileAndClient'])
            || (Strings::GetSess('userRole') == 'Manager'
                || Strings::GetSess('userRole') == 'Super')
        ) {
            $val = $_REQUEST['allowToupdateFileAndClient'] ?? null;
            if (!is_array($val)) {
                $val = [$val];
            }
            $tblAgent->allowToupdateFileAndClient = implode(',', $val);
        } else {
            $tblAgent->allowToupdateFileAndClient = $tblAgent->allowToupdateFileAndClient ?? '';
        }
        if (isset($_REQUEST['thirdPartyServices'])) {
            $tblAgent->thirdPartyServices = intval($_REQUEST['thirdPartyServices'] ?? 0);
        } else {
            $tblAgent->thirdPartyServices = $tblAgent->thirdPartyServices ?? 0;
        }
        if (Request::isset('thirdPartyServicesLegalDocs')) {
            $tblAgent->thirdPartyServicesLegalDocs = intval(Request::GetClean('thirdPartyServicesLegalDocs') ?? 0);
        } else {
            $tblAgent->thirdPartyServicesLegalDocs = $tblAgent->thirdPartyServices ?? 0;
        }
        if (Request::isset('allowToViewContactsList')) {
            $tblAgent->allowToViewContactsList = intval(Request::GetClean('allowToViewContactsList')) ?? null;
        }

        if (array_key_exists('website', $ip)) {
            $tblAgent->website = trim($ip['website']);
        } else {
            $tblAgent->website = $tblAgent->website ?? '';
        }
        if (array_key_exists('shareThisFile', $ip)) {
            $tblAgent->shareThisFile = intval($ip['shareThisFile']);
        } else {
            $tblAgent->shareThisFile = $tblAgent->shareThisFile ?? 0;
        }
        if (array_key_exists('NMLSLicense', $ip)) {
            $tblAgent->NMLSLicense = trim($ip['NMLSLicense']);
        } else {
            $tblAgent->NMLSLicense = $tblAgent->NMLSLicense ?? '';
        }
        if (array_key_exists('DRE', $ip)) {
            $tblAgent->DRE = trim($ip['DRE']);
        } else {
            $tblAgent->DRE = $tblAgent->DRE ?? '';
        }
        if (array_key_exists('externalBroker', $ip)) {
            $tblAgent->externalBroker = intval($ip['externalBroker']);
        } else {
            $tblAgent->externalBroker = $tblAgent->externalBroker ?? 0;
        }
        if (array_key_exists('allowToSubmitOffer', $ip)) {
            $tblAgent->allowToSubmitOffer = intval($ip['allowToSubmitOffer']);
        } else {
            $tblAgent->allowToSubmitOffer = $tblAgent->allowToSubmitOffer ?? 0;
        }
        if (array_key_exists('allowToViewCreditScreening', $ip)) {
            $tblAgent->allowToViewCreditScreening = intval($ip['allowToViewCreditScreening']);
        } else {
            $tblAgent->allowToViewCreditScreening = $tblAgent->allowToViewCreditScreening ?? 0;
        }

        if (array_key_exists('enable2FAAuthentication', $ip)) {
            $tblAgent->enable2FAAuthentication = intval($ip['enable2FAAuthentication']);
        } else {
            $tblAgent->enable2FAAuthentication = $tblAgent->enable2FAAuthentication ?? 0;
        }

        if (array_key_exists('TwoFAType', $ip)) $tblAgent->TwoFAType = trim($ip['TwoFAType']);
        if (array_key_exists('userPriceEngineStatus', $ip)) {
            $tblAgent->userPriceEngineStatus = intval($ip['userPriceEngineStatus']);
        } else {
            $tblAgent->userPriceEngineStatus = $tblAgent->userPriceEngineStatus ?? 0;
        }

        if (array_key_exists('notifyBODocUpload', $ip)) {
            $tblAgent->notifyBODocUpload = intval($ip['notifyBODocUpload']);
        } else {
            $tblAgent->notifyBODocUpload = $tblAgent->notifyBODocUpload ?? 0;
        }
        if (array_key_exists('notifyBranchDocUpload', $ip)) {
            $tblAgent->notifyBranchDocUpload = intval($ip['notifyBranchDocUpload']);
        } else {
            $tblAgent->notifyBranchDocUpload = $tblAgent->notifyBranchDocUpload ?? 0;
        }
        if (array_key_exists('notifyLODocUpload', $ip)) {
            $tblAgent->notifyLODocUpload = intval($ip['notifyLODocUpload']);
        } else {
            $tblAgent->notifyLODocUpload = $tblAgent->notifyLODocUpload ?? 0;
        }
        if (array_key_exists('notifyBrokerDocUpload', $ip)) {
            $tblAgent->notifyBrokerDocUpload = intval($ip['notifyBrokerDocUpload']);
        } else {
            $tblAgent->notifyBrokerDocUpload = $tblAgent->notifyBrokerDocUpload ?? 0;
        }
        if (array_key_exists('notifyDocUploadRequest', $ip)) {
            $tblAgent->notifyDocUploadRequest = intval($ip['notifyDocUploadRequest']);
        } else {
            $tblAgent->notifyDocUploadRequest = $tblAgent->notifyDocUploadRequest ?? 0;
        }

        if (array_key_exists('loanpassLogin', $ip)) {
            $tblAgent->loanpassLogin = trim($ip['loanpassLogin']);
        } else {
            $tblAgent->loanpassLogin = $tblAgent->loanpassLogin ?? '';
        }
        if (array_key_exists('loanpassPassword', $ip)) {
            $tblAgent->loanpassPassword = trim($ip['loanpassPassword']);
        } else {
            $tblAgent->loanpassPassword = $tblAgent->loanpassPassword ?? '';
        }

        if (array_key_exists('allowToAccessInternalLoanProgram', $ip)) {
            $tblAgent->allowToAccessInternalLoanProgram = intval($ip['allowToAccessInternalLoanProgram']);
        } else {
            $tblAgent->allowToAccessInternalLoanProgram = $tblAgent->allowToAccessInternalLoanProgram ?? 0;
        }
        if (array_key_exists('allowToViewAutomationPopup', $ip)) {
            $tblAgent->allowToViewAutomationPopup = intval($ip['allowToViewAutomationPopup']);
        } else {
            $tblAgent->allowToViewAutomationPopup = $tblAgent->allowToViewAutomationPopup ?? 0;
        }
        if (array_key_exists('allowToCopyFile', $ip)) {
            $tblAgent->allowToCopyFile = intval($ip['allowToCopyFile']);
        } else {
            $tblAgent->allowToCopyFile = $tblAgent->allowToCopyFile ?? 0;
        }
        if (array_key_exists('allowToAssignBOEmployee', $ip)) {
            $tblAgent->allowToAssignBOEmployee = intval($ip['allowToAssignBOEmployee']);
        } else {
            $tblAgent->allowToAssignBOEmployee = $tblAgent->allowToAssignBOEmployee ?? 0;
        }
        if (array_key_exists('ssnNumber', $ip)) {
            $tblAgent->ssnNumber = trim($ip['ssnNumber']);
        } else {
            $tblAgent->ssnNumber = $tblAgent->ssnNumber ?? '';
        }
        if (array_key_exists('linkedInURL', $ip)) {
            $tblAgent->linkedInURL = trim($ip['linkedInURL']);
        } else {
            $tblAgent->linkedInURL = $tblAgent->linkedInURL ?? '';
        }
        if (array_key_exists('ofEmployees', $ip)) {
            $tblAgent->ofEmployees = intval($ip['ofEmployees']);
        } else {
            $tblAgent->ofEmployees = $tblAgent->ofEmployees ?? 0;
        }
        if (array_key_exists('bestServeYourNeeds', $ip)) {
            $tblAgent->bestServeYourNeeds = trim($ip['bestServeYourNeeds']);
        } else {
            $tblAgent->bestServeYourNeeds = $tblAgent->bestServeYourNeeds ?? '';
        }
        if (array_key_exists('useSameContactInfo', $ip)) {
            $tblAgent->useSameContactInfo = trim($ip['useSameContactInfo']);
        } else {
            $tblAgent->useSameContactInfo = $tblAgent->useSameContactInfo ?? 0;
        }
        if (array_key_exists('primaryContactFName', $ip)) {
            $tblAgent->primaryContactFName = trim($ip['primaryContactFName']);
        } else {
            $tblAgent->primaryContactFName = $tblAgent->primaryContactFName ?? '';
        }
        if (array_key_exists('primaryContactLName', $ip)) {
            $tblAgent->primaryContactLName = trim($ip['primaryContactLName']);
        } else {
            $tblAgent->primaryContactLName = $tblAgent->primaryContactLName ?? '';
        }
        if (array_key_exists('primaryContactPhone', $ip)) {
            $tblAgent->primaryContactPhone = trim($ip['primaryContactPhone']);
        } else {
            $tblAgent->primaryContactPhone = $tblAgent->primaryContactPhone ?? '';
        }
        if (array_key_exists('primaryContactEmail', $ip)) {
            $tblAgent->primaryContactEmail = trim($ip['primaryContactEmail']);
        } else {
            $tblAgent->primaryContactEmail = $tblAgent->primaryContactEmail ?? '';
        }
        if (array_key_exists('allowToSeeAllBrokers', $ip)) {
            $tblAgent->allowToSeeAllBrokers = intval($ip['allowToSeeAllBrokers']);
        } else {
            $tblAgent->allowToSeeAllBrokers = $tblAgent->allowToSeeAllBrokers ?? 0;
        }
        if (array_key_exists('privateBroker', $ip)) {
            $tblAgent->privateBroker = intval($ip['privateBroker']);
        } else {
            $tblAgent->expireDate = $tblAgent->privateBroker ?? 0;
        }
        if (array_key_exists('expireDate', $ip)) {
            $tblAgent->expireDate = trim($ip['expireDate']);
        } else {
            $tblAgent->expireDate = $tblAgent->expireDate ?? '';
        }
        if (array_key_exists('activeStatus', $ip)) {
            $tblAgent->status = intval($ip['activeStatus']);
        } else {
            $tblAgent->status = $tblAgent->status ?? 1;
        }
        if (array_key_exists('paymentStatus', $ip)) {
            $tblAgent->paymentStatus = $ip['paymentStatus'];
        } else {
            $tblAgent->paymentStatus = $tblAgent->paymentStatus ?? 'Unpaid';
        }
        if (array_key_exists('regFrom', $ip)) {
            $tblAgent->regFrom = $ip['regFrom'];
        } else {
            $tblAgent->regFrom = $tblAgent->regFrom ?? 'TLP';
        }
        if (array_key_exists('membership', $ip)) {
            $tblAgent->membership = $ip['membership'];
        } else {
            $tblAgent->membership = $tblAgent->membership ?? 'B';
        }
        if (array_key_exists('fluidLayout', $ip)) {
            $tblAgent->fluidLayout = intval($ip['fluidLayout']);
        } else {
            $tblAgent->fluidLayout = $tblAgent->fluidLayout ?? 0;
        }
        if (array_key_exists('approvedReferralFee', $ip)) {
            $tblAgent->approvedReferralFee = intval($ip['approvedReferralFee']);
        }

        if (array_key_exists('allowToMassUpdate', $ip)) {
            $tblAgent->allowToMassUpdate = intval($ip['allowToMassUpdate']);
        }

        if (array_key_exists('allowToEditLoanStage', $ip)) {
            $tblAgent->allowToEditLoanStage = intval($ip['allowToEditLoanStage']);
        } else {
            $tblAgent->allowToEditLoanStage = $tblAgent->allowToEditLoanStage ?? 0;
        }

        if (array_key_exists('allowToManageDraws', $ip)) {
            $tblAgent->allowToManageDraws = intval($ip['allowToManageDraws']);
        }


        $tblAgent->planType = intval($tblAgent->planType);
        $tblAgent->noOfMortgageBropkers = intval($tblAgent->planType);
        $tblAgent->personalNMLSLicense = $ip['personalNMLSLicense'];
        $tblAgent->statesAuthorizedToOriginate = $ip['statesAuthorizedToOriginate'];
        $tblAgent->haveCompanyNMLS = $ip['haveCompanyNMLS'];
        $tblAgent->havePersonalNMLS = $ip['havePersonalNMLS'];
        $tblAgent->haveCompanyStateLicense = $ip['haveCompanyStateLicense'];
        $tblAgent->havePersonalStateLicense = $ip['havePersonalStateLicense'];
    }

    /**
     * @param array $ip
     * @return array
     */
    private static function insert(array $ip): array
    {
        $tblAgent = new tblAgent();

        self::setAgentValues($tblAgent, $ip);
        $tblAgent->Save();

        $brokerNumber = $tblAgent->userNumber;


        if ($brokerNumber && trim($ip['pwd']) == '') {
            $qry = '
                    UPDATE
                        tblAgent
                    set
                        pwd = :pwd
                    WHERE
                        userNumber = :userNumber
                    ';
            Database2::getInstance()->update($qry, [
                'pwd' => $brokerNumber,
                'userNumber' => $brokerNumber,
            ]);
        }
        return [
            'insertCount' => $brokerNumber > 0 ? 1 : 0
            , 'brokerNumber' => $brokerNumber,
        ];
    }

    /**
     * @param array $ip
     * @param int|null $brokerNumber
     * @return array
     */
    private static function update(
        array $ip,
        ?int  $brokerNumber
    ): array
    {

        if (!$brokerNumber) {
            return ['updateCount' => 0];
        }
        $tblAgent = tblAgent::Get(['userNumber' => $brokerNumber]);

        if (!$tblAgent) {
            $tblAgent = new tblAgent();
            return ['updateCount' => 0];
        }

        self::setAgentValues($tblAgent, $ip);

        $tblAgent->Save();
        return ['updateCount' => 1];

    }

    /**
     * @param $ip
     * @return array
     */
    public static function getReport($ip): array
    {
        $brokerNumber = $ip['brokerNumber'] ?? '';
        $WFIDsArray = $ip['WFIDs'] ?? [];
        $zipCode = $ip['zip'] ?? '';
        $zipCode = $ip['zipCode'] ?? $zipCode;
        $ip['zipCode'] = $zipCode;

        if (array_key_exists('prefCommunication', $ip)) {
            $prefCommunication = $ip['prefCommunication'];
            if (is_array($prefCommunication)) {
                $prefCommunication = implode(',', $prefCommunication);
                $ip['prefCommunication'] = $prefCommunication;
            }
        }
        if (array_key_exists('statesAuthorizedToOriginate', $ip)) {
            $ip['statesAuthorizedToOriginate'] = is_array($ip['statesAuthorizedToOriginate']) ? implode(',',$ip['statesAuthorizedToOriginate']) : '';
        }

        if ($brokerNumber > 0) {
            $brokerArray = self::update($ip, $brokerNumber);
        } else {
            $brokerArray = self::insert($ip);
            //CRB Reset Permissions
            $brokerNumber = $brokerArray['brokerNumber'];
            $PCID = $ip['PCID'] ?? 0;
            if (glCustomJobForProcessingCompany::isPC_CRB($PCID)) {
                $tblAgent = tblAgent::Get(['userNumber' => $brokerNumber]);
                $tblAgent->allowAgentToAccessLMRDocs = 0;
                $tblAgent->allowAgentToCreateFiles = 0;
                $tblAgent->allowAgentToSendHomeownerLink = 0;
                $tblAgent->allowAgentToEditLMRFile = 0;
                $tblAgent->allowAgentToLogin = 0;
                $tblAgent->allowedToUpdateFiles = 0;
                $tblAgent->allowToAccessPrivateNotes = 0;
                $tblAgent->sendNewDealAlert = 0;
                $tblAgent->allowToSendMassEmail = 0;
                $tblAgent->allowAgentToCreateTasks = 0;
                $tblAgent->allowAgentToSeeDashboard = 0;
                $tblAgent->allowedToDeleteUplodedDocs = 0;
                $tblAgent->allowedToEditOwnNotes = 0;
                $tblAgent->seeBilling = 0;
                $tblAgent->permissionToREST = 0;
                $tblAgent->allowedToExcelReport = 0;
                $tblAgent->allowToLASubmit = 0;
                $tblAgent->allowEmailCampaign = 0;
                $tblAgent->allowToSendFax = 0;
                $tblAgent->allowAgentToEditCommission = 0;
                $tblAgent->allowAgentToSeeCommission = 0;
                $tblAgent->allowToSendFileDesignation = 0;
                $tblAgent->allowAgentToSeePublicNotes = 0;
                $tblAgent->allowToCFPBSubmit = 0;
                $tblAgent->allowToViewCFPBPipeline = 0;
                $tblAgent->allowToAccessRAM = 0;
                $tblAgent->allowToSendMarketingEmailForBRBO = 0;
                $tblAgent->allowToSeeWebForms = 0;
                $tblAgent->allowToEditCCInfo = 0;
                $tblAgent->allowEditToIR = 0;
                $tblAgent->allowWorkflowEdit = 0;
                $tblAgent->allowAgentToGetBorrowerUploadDocsNotification = 1; // only this one is true
                //$tblAgent->allowToOverRideLockOnLoanAgent = 0;
                $tblAgent->allowToViewMarketPlace = 0;
                $tblAgent->allowServicing = 0;
                $tblAgent->allowToLockLoanFileAgent = 0;
                $tblAgent->allowToCreateAloware = 0;
                //$tblAgent->allowToupdateFileAndClient = 0;
                $tblAgent->shareThisFile = 0;
                $tblAgent->allowToSubmitOffer = 0;
                $tblAgent->allowToViewCreditScreening = 0;
                $tblAgent->acqualifyOptStatus = 0;
                $tblAgent->enable2FAAuthentication = 0;
                $tblAgent->userPriceEngineStatus = 0;
                $tblAgent->notifyBODocUpload = 0;
                $tblAgent->notifyBranchDocUpload = 0;
                $tblAgent->notifyLODocUpload = 0;
                $tblAgent->notifyBrokerDocUpload = 0;
                $tblAgent->notifyDocUploadRequest = 0;
                $tblAgent->allowToAccessInternalLoanProgram = 0;
                $tblAgent->allowToViewAutomationPopup = 0;
                $tblAgent->allowToCopyFile = 0;
                $tblAgent->allowToAssignBOEmployee = 0;
                $tblAgent->allowToSeeAllBrokers = 0;
                $tblAgent->allowToEditLoanStage = 0;
                $tblAgent->Save();
            }
        }

        if ($brokerNumber > 0 && isset($ip['WFIDs'])) {

            saveUserAssignedWorkflow::getReport([
                'WFIDs' => $WFIDsArray,
                'UID' => $brokerNumber,
                'UType' => 'Agent',
                'updatedBy' => $ip['userNumber'],
                'updatedType' => $ip['userGroup'],
            ]);
        }

        if (Request::isset('thirdPartyServices')) {
            saveThirdPartyServicesUserDetails::getReport($brokerNumber, 'Agent', $_REQUEST['thirdPartyServicesData']);
        }

        return $brokerArray;
    }
}
