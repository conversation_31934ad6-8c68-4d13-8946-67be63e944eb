<?php

namespace models\composite\oBranch;

use models\composite\oEmployee\saveUserAssignedWorkflow;
use models\cypher;
use models\lendingwise\db\tblBranch_db;
use models\lendingwise\tblBranch;
use models\Request;
use models\standard\Arrays;
use models\standard\Strings;
use models\types\strongType;
use models\composite\oThirdPartyServices\saveThirdPartyServicesUserDetails;


/**
 *
 */
class saveBranchInfo extends strongType
{
    /**
     * @param $userRole
     * @param $allowToCreateBranch
     * @param $userType
     * @param $infoArray
     * @param $LMRExecutive
     * @param $LMRFirstName
     * @param $LMRLastName
     * @param $email
     * @param $company
     * @param $pwd
     * @param $LMRBccEmail
     * @param $LMRCcEmail
     * @param $tollFree
     * @param $fax
     * @param $website
     * @param $branchAELoginUrl
     * @param $cellNumber
     * @param $address1
     * @param $city
     * @param $state
     * @param $zipCode
     * @param $paymentToCompany
     * @param $executiveCreatedDate
     * @param $allowLMRAEToEditFile
     * @param $allowAgentToSeeFile
     * @param $subscribedOption
     * @param $allowLMRAEToAccessDocs
     * @param $allowLMRToOnOffAgentLogin
     * @param $allowLMRAEToEditCommission
     * @param $serviceProvider
     * @param $allowAddOn
     * @param $timeZone
     * @param $allowToUpdateFileAdminSection
     * @param $sendMarketingEmail
     * @param $displayLinkToFICO
     * @param $processingCompanyId
     * @param $askReferralAgent
     * @param $askPaymentBeforeLMR
     * @param $allowClientToUploadDocs
     * @param $allowBranchToLogin
     * @param $allowLMRToEditAgentProfile
     * @param $allowToCopyFile
     * @param $allowedToDeleteUplodedDocs
     * @return array
     */
    private static function insert(
        $userRole,
        $allowToCreateBranch,
        $userType,
        $infoArray,
        $LMRExecutive,
        $LMRFirstName,
        $LMRLastName,
        $email,
        $company,
        $pwd,
        $LMRBccEmail,
        $LMRCcEmail,
        $tollFree,
        $fax,
        $website,
        $branchAELoginUrl,
        $cellNumber,
        $address1,
        $city,
        $state,
        $zipCode,
        $paymentToCompany,
        $executiveCreatedDate,
        $allowLMRAEToEditFile,
        $allowAgentToSeeFile,
        $subscribedOption,
        $allowLMRAEToAccessDocs,
        $allowLMRToOnOffAgentLogin,
        $allowLMRAEToEditCommission,
        $serviceProvider,
        $allowAddOn,
        $timeZone,
        $allowToUpdateFileAdminSection,
        $sendMarketingEmail,
        $displayLinkToFICO,
        $processingCompanyId,
        $askReferralAgent,
        $askPaymentBeforeLMR,
        $allowClientToUploadDocs,
        $allowBranchToLogin,
        $allowLMRToEditAgentProfile,
        $allowToCopyFile,
        $allowedToDeleteUplodedDocs
    ): array
    {

        $tblBranch = new tblBranch();

        $tblBranch->allowedToDeleteUplodedDocs = $allowedToDeleteUplodedDocs;
        $tblBranch->LMRExecutive = $LMRExecutive;
        $tblBranch->LMRExecutiveFirstName = $LMRFirstName;
        $tblBranch->LMRExecutiveLastName = $LMRLastName;
        $tblBranch->executiveEmail = $email;
        $tblBranch->company = $company;
        $tblBranch->pwd = $pwd;
        $tblBranch->LMRBccEmail = $LMRBccEmail;
        $tblBranch->LMRCcEmail = $LMRCcEmail;
        $tblBranch->tollFree = $tollFree;
        $tblBranch->fax = $fax;
        $tblBranch->website = $website;
        $tblBranch->branchAELoginUrl = $branchAELoginUrl;
        $tblBranch->cellNumber = $cellNumber;
        $tblBranch->address = $address1;
        $tblBranch->city = $city;
        $tblBranch->state = $state;
        $tblBranch->zipCode = $zipCode;
        $tblBranch->paymentToCompany = $paymentToCompany;
        $tblBranch->executiveCreatedDate = $executiveCreatedDate;
        $tblBranch->allowLMRAEToEditFile = $allowLMRAEToEditFile;
        $tblBranch->agentFileAccess = intval($allowAgentToSeeFile);
        $tblBranch->subscribedOption = $subscribedOption;
        $tblBranch->allowLMRAEToAccessDocs = $allowLMRAEToAccessDocs;
        $tblBranch->allowLMRToOnOffAgentLogin = $allowLMRToOnOffAgentLogin;
        $tblBranch->allowLMRAEToEditCommission = $allowLMRAEToEditCommission;
        $tblBranch->serviceProvider = $serviceProvider;
        $tblBranch->allowAddOn = $allowAddOn;
        $tblBranch->timeZone = $timeZone;
        $tblBranch->allowToUpdateFileAdminSection = $allowToUpdateFileAdminSection;
        $tblBranch->sendMarketingEmail = $sendMarketingEmail;
        $tblBranch->displayLinkToFICO = intval($displayLinkToFICO);
        $tblBranch->userType = $userType;
        $tblBranch->allowToCopyFile = $allowToCopyFile;

        if (($userRole == 'Super') || ($allowToCreateBranch == 1)) {
            $tblBranch->processingCompanyId = $processingCompanyId;
        }

        if ($userType == 'PLO') {
            $tblBranch->askReferralAgent = $askReferralAgent;
            $tblBranch->askPaymentBeforeLMR = $askPaymentBeforeLMR;
            $tblBranch->allowClientToUploadDocs = $allowClientToUploadDocs;
        }

        if (isset($infoArray['allowBranchToLogin'])) {
            $tblBranch->allowBranchToLogin = $allowBranchToLogin;
        }

        if (isset($infoArray['allowLMRToEditAgentProfile'])) {
            $tblBranch->allowLMRToEditAgentProfile = $allowLMRToEditAgentProfile;
        }

        if (isset($infoArray['phone'])) {
            $tblBranch->directPhone = $infoArray['phone'];
        }

        if (isset($infoArray['allowToAddAgent'])) {
            $tblBranch->allowToAddAgent = $infoArray['allowToAddAgent'];
        }

        if (isset($infoArray['useMyServerSetting'])) {
            $tblBranch->useMyServerSetting = $infoArray['useMyServerSetting'];
        }

        if (isset($infoArray['allowBranchToCreateFiles'])) {
            $tblBranch->allowBranchToCreateFiles = $infoArray['allowBranchToCreateFiles'];
        }

        if (isset($infoArray['allowBranchToCreateTasks'])) {
            $tblBranch->allowBranchToCreateTasks = $infoArray['allowBranchToCreateTasks'];
        }

        if (isset($infoArray['allowBranchToSeeDashboard'])) {
            $tblBranch->allowBranchToSeeDashboard = $infoArray['allowBranchToSeeDashboard'];
        }

        if (isset($infoArray['accessRestriction'])) {
            $tblBranch->accessRestriction = $infoArray['accessRestriction'];
        }

        if (isset($infoArray['AllowToCreateAloware'])) {
            $tblBranch->AllowToCreateAloware = $infoArray['AllowToCreateAloware'];
        }

        if (isset($infoArray['seePrivate'])) {
            $tblBranch->seePrivate = $infoArray['seePrivate'];
        }

        if (isset($infoArray['allowedToDeleteUplodedDocs'])) {
            $tblBranch->allowedToDeleteUplodedDocs = $infoArray['allowedToDeleteUplodedDocs'];
        }

        if (isset($infoArray['allowedToEditOwnNotes'])) {
            $tblBranch->allowedToEditOwnNotes = $infoArray['allowedToEditOwnNotes'];

        }

        if (isset($infoArray['allowEmailCampaign'])) {
            $tblBranch->allowEmailCampaign = $infoArray['allowEmailCampaign'];
        }

        if (isset($infoArray['permissionToREST'])) {
            $tblBranch->permissionToREST = $infoArray['permissionToREST'];
        }

        if (isset($infoArray['allowedToExcelReport'])) {
            $tblBranch->allowedToExcelReport = $infoArray['allowedToExcelReport'];
        }

        if (isset($infoArray['changeDIYPlan'])) {
            $tblBranch->changeDIYPlan = $infoArray['changeDIYPlan'];
        }

        if (isset($infoArray['globalAcctNo'])) {
            $tblBranch->globalAcctNo = $infoArray['globalAcctNo'];
        }

        if (isset($infoArray['sponsorName'])) {
            $tblBranch->sponsorName = $infoArray['sponsorName'];
        }

        if (isset($infoArray['activeStatus'])) {
            $tblBranch->activeStatus = $infoArray['activeStatus'];
        }

        if (isset($infoArray['allowToSendHomeownerLink'])) {
            $tblBranch->allowToSendHomeownerLink = $infoArray['allowToSendHomeownerLink'];
        }

        if (isset($infoArray['subscribeToHOME'])) {
            $tblBranch->subscribeToHOME = $infoArray['subscribeToHOME'];
        }

        if (isset($infoArray['isPrimary'])) {
            $tblBranch->isPrimary = $infoArray['isPrimary'];
        }

        if (isset($infoArray['allowToLASubmit'])) {
            $tblBranch->allowToLASubmit = $infoArray['allowToLASubmit'];
        }

        if (isset($infoArray['allowToCFPBSubmit'])) {
            $tblBranch->allowToCFPBSubmit = $infoArray['allowToCFPBSubmit'];
        }

        if (isset($infoArray['allowToSendFax'])) {
            $tblBranch->allowToSendFax = $infoArray['allowToSendFax'];
        }

        if (isset($infoArray['allowBranchToSeeCommission'])) {
            $tblBranch->allowBranchToSeeCommission = $infoArray['allowBranchToSeeCommission'];
        }

        if (isset($infoArray['allowClientToAccessDocs'])) {
            $tblBranch->allowClientToAccessDocs = $infoArray['allowClientToAccessDocs'];
        }

        if (isset($infoArray['allowToSendFileDesignation'])) {
            $tblBranch->allowToSendFileDesignation = $infoArray['allowToSendFileDesignation'];
        }

        if (isset($infoArray['allowBranchToSeePublicNotes'])) {
            $tblBranch->allowBranchToSeePublicNotes = $infoArray['allowBranchToSeePublicNotes'];
        }

        if (isset($infoArray['mailingAddress'])) {
            $tblBranch->mailingAddress = $infoArray['mailingAddress'];
        }

        if (isset($infoArray['mailingCity'])) {
            $tblBranch->mailingCity = $infoArray['mailingCity'];
        }

        if (isset($infoArray['mailingState'])) {
            $tblBranch->mailingState = $infoArray['mailingState'];
        }

        if (isset($infoArray['mailingZipCode'])) {
            $tblBranch->mailingZipCode = $infoArray['mailingZipCode'];
        }

        if (isset($infoArray['bankName'])) {
            $tblBranch->bankName = $infoArray['bankName'];
        }

        if (isset($infoArray['routingNumber'])) {
            $tblBranch->routingNumber = $infoArray['routingNumber'];
        }

        if (isset($infoArray['accountNumber'])) {
            $tblBranch->accountNumber = $infoArray['accountNumber'];
        }

        if (isset($infoArray['county'])) {
            $tblBranch->county = $infoArray['county'];
        }

        if (isset($infoArray['allowToViewCFPBPipeline'])) {
            $tblBranch->allowToViewCFPBPipeline = $infoArray['allowToViewCFPBPipeline'];
        }

        if (isset($infoArray['allowToAccessRAM'])) {
            $tblBranch->allowToAccessRAM = $infoArray['allowToAccessRAM'];
        }

        if (isset($infoArray['allowBranchManagerToLogin'])) {
            $tblBranch->allowBranchManagerToLogin = $infoArray['allowBranchManagerToLogin'];
        }

        if (isset($infoArray['allowEditToIR'])) {
            $tblBranch->allowEditToIR = $infoArray['allowEditToIR'];
        }

        if (isset($infoArray['allowWorkflowEdit'])) {
            $tblBranch->allowWorkflowEdit = $infoArray['allowWorkflowEdit'];
        }

        if (isset($infoArray['allowToLockLoanFileBranch'])) {
            $tblBranch->allowToLockLoanFileBranch = $infoArray['allowToLockLoanFileBranch'];
        }

        if (isset($infoArray['allowBranchToGetBorrowerUploadDocsNotification'])) {
            $tblBranch->allowBranchToGetBorrowerUploadDocsNotification = $infoArray['allowBranchToGetBorrowerUploadDocsNotification'];
        }

        if (isset($infoArray['allowToViewMarketPlace'])) {
            $tblBranch->allowToViewMarketPlace = $infoArray['allowToViewMarketPlace'];
        }

        if (isset($infoArray['allowcaptcha'])) {
            $tblBranch->allowcaptcha = $infoArray['allowcaptcha'];
        }

        if (isset($infoArray['shareThisFile'])) {
            $tblBranch->shareThisFile = $infoArray['shareThisFile'];
        }

        if (isset($infoArray['allowToSubmitOffer'])) {
            $tblBranch->allowToSubmitOffer = $infoArray['allowToSubmitOffer'];
        }

        if (isset($infoArray['allowToViewCreditScreening'])) {
            $tblBranch->allowToViewCreditScreening = $infoArray['allowToViewCreditScreening'];
        }

        if (isset($infoArray['enable2FAAuthentication'])) {
            $tblBranch->enable2FAAuthentication = $infoArray['enable2FAAuthentication'];
        }

        if (isset($infoArray['TwoFAType'])) {
            $tblBranch->TwoFAType = $infoArray['TwoFAType'];
        }

        if (isset($infoArray['userPriceEngineStatus'])) {
            $tblBranch->userPriceEngineStatus = $infoArray['userPriceEngineStatus'];
        }

        if (isset($_REQUEST['allowToupdateFileAndClient'])
            || (Strings::GetSess('userRole') == 'Manager'
                || Strings::GetSess('userRole') == 'Super')
        ) {
            $allowToupdateFileAndClient = Arrays::getArrayValue('allowToupdateFileAndClient', $_REQUEST);
            if (!is_array($allowToupdateFileAndClient)) {
                if ($allowToupdateFileAndClient) {
                    $allowToupdateFileAndClient = [$allowToupdateFileAndClient];
                } else {
                    $allowToupdateFileAndClient = [];
                }
            }
            $tblBranch->allowToupdateFileAndClient = implode(',', $allowToupdateFileAndClient);
        }

        if (isset($_REQUEST['thirdPartyServices'])) {
            $tblBranch->thirdPartyServices = $_REQUEST['thirdPartyServices'];
        }

        if (Request::isset('thirdPartyServicesLegalDocs')) {
            $tblBranch->thirdPartyServicesLegalDocs = Request::GetClean('thirdPartyServicesLegalDocs');
        }

        if (isset($infoArray['notifyBODocUpload'])) {
            $tblBranch->notifyBODocUpload = $infoArray['notifyBODocUpload'];
        }

        if (isset($infoArray['notifyLODocUpload'])) {
            $tblBranch->notifyLODocUpload = $infoArray['notifyLODocUpload'];
        }

        if (isset($infoArray['notifyBrokerDocUpload'])) {
            $tblBranch->notifyBrokerDocUpload = $infoArray['notifyBrokerDocUpload'];
        }

        if (isset($infoArray['notifyDocUploadRequest'])) {
            $tblBranch->notifyDocUploadRequest = $infoArray['notifyDocUploadRequest'];
        }
        if (isset($infoArray['notifyNewFileCreated'])) {
            $tblBranch->notifyNewFileCreated = $infoArray['notifyNewFileCreated'];
        }

        if (isset($infoArray['DocUploadBcc'])) {
            $tblBranch->DocUploadBcc = $infoArray['DocUploadBcc'];
        }

        if (isset($infoArray['DocUploadCc'])) {
            $tblBranch->DocUploadCc = $infoArray['DocUploadCc'];
        }

        if (isset($infoArray['loanpassLogin'])) {
            $tblBranch->loanpassLogin = $infoArray['loanpassLogin'];
        }

        if (isset($infoArray['loanpassPassword'])) {
            $tblBranch->loanpassPassword = $infoArray['loanpassPassword'];
        }

        if (isset($infoArray['allowToAccessInternalLoanProgram'])) {
            $tblBranch->allowToAccessInternalLoanProgram = $infoArray['allowToAccessInternalLoanProgram'];
        }

        if (isset($infoArray['allowToViewAutomationPopup'])) {
            $tblBranch->allowToViewAutomationPopup = $infoArray['allowToViewAutomationPopup'];
        }


        if (isset($infoArray['allowToMassUpdate'])) {
            $tblBranch->allowToMassUpdate = $infoArray['allowToMassUpdate'];
        }

        if (isset($infoArray['allowToEditLoanStage'])) {
            $tblBranch->allowToEditLoanStage = $infoArray['allowToEditLoanStage'];
        }

        if (Request::isset('allowToManageDraws')) {
            $tblBranch->allowToManageDraws = $infoArray['allowToManageDraws'];
        }

        if (Request::isset('allowUsersToSendEsignLinkToAll')) {
            $tblBranch->allowUsersToSendEsignLinkToAll = intval(Request::GetClean('allowUsersToSendEsignLinkToAll'));
        }

        $tblBranch->Save();
        $insertCount = 1;
        return [
            'executiveId' => $tblBranch->executiveId,
            'insertCount' => $insertCount,
        ];
    }

    /**
     * @param $executiveId
     * @param $infoArray
     * @param $LMRExecutive
     * @param $LMRFirstName
     * @param $LMRLastName
     * @param $company
     * @param $tollFree
     * @param $fax
     * @param $website
     * @param $cellNumber
     * @param $address1
     * @param $city
     * @param $state
     * @param $zipCode
     * @param $paymentToCompany
     * @param $userType
     * @param $allowLMRAEToEditFile
     * @param $allowAgentToSeeFile
     * @param $subscribedOption
     * @param $branchAELoginUrl
     * @param $allowLMRAEToAccessDocs
     * @param $allowLMRToOnOffAgentLogin
     * @param $allowLMRAEToEditCommission
     * @param $serviceProvider
     * @param $allowAddOn
     * @param $timeZone
     * @param $allowToUpdateFileAdminSection
     * @param $sendMarketingEmail
     * @param $displayLinkToFICO
     * @param $opt
     * @param $LMRBccEmail
     * @param $LMRCcEmail
     * @param $userRole
     * @param $processingCompanyId
     * @param $askReferralAgent
     * @param $askPaymentBeforeLMR
     * @param $allowClientToUploadDocs
     * @param $allowedToDeleteUplodedDocs
     * @param $allowToCopyFile
     * @return int
     */
    private static function update(
        $executiveId,
        $infoArray,
        $LMRExecutive,
        $LMRFirstName,
        $LMRLastName,
        $company,
        $tollFree,
        $fax,
        $website,
        $cellNumber,
        $address1,
        $city,
        $state,
        $zipCode,
        $paymentToCompany,
        $userType,
        $allowLMRAEToEditFile,
        $allowAgentToSeeFile,
        $subscribedOption,
        $branchAELoginUrl,
        $allowLMRAEToAccessDocs,
        $allowLMRToOnOffAgentLogin,
        $allowLMRAEToEditCommission,
        $serviceProvider,
        $allowAddOn,
        $timeZone,
        $allowToUpdateFileAdminSection,
        $sendMarketingEmail,
        $displayLinkToFICO,
        $opt,
        $LMRBccEmail,
        $LMRCcEmail,
        $userRole,
        $processingCompanyId,
        $askReferralAgent,
        $askPaymentBeforeLMR,
        $allowClientToUploadDocs,
        $allowedToDeleteUplodedDocs,
        $allowToCopyFile
    ): int
    {
        $tblBranch = tblBranch::Get([
            tblBranch_db::COLUMN_EXECUTIVEID => $executiveId,
        ]);

        if(!$tblBranch) {
            return 0;
        }

        $tblBranch->allowedToDeleteUplodedDocs = $allowedToDeleteUplodedDocs;

        $tblBranch->LMRExecutive = $LMRExecutive;
        $tblBranch->LMRExecutiveFirstName = $LMRFirstName;
        $tblBranch->LMRExecutiveLastName = $LMRLastName;
        $tblBranch->company = $company;
        $tblBranch->tollFree = $tollFree;
        $tblBranch->fax = $fax;
        $tblBranch->website = $website;
        $tblBranch->cellNumber = $cellNumber;
        $tblBranch->address = $address1;
        $tblBranch->city = $city;
        $tblBranch->state = $state;
        $tblBranch->zipCode = $zipCode;
        $tblBranch->paymentToCompany = $paymentToCompany;
        $tblBranch->userType = $userType;
        $tblBranch->allowLMRAEToEditFile = $allowLMRAEToEditFile;
        $tblBranch->agentFileAccess = intval($allowAgentToSeeFile);
        $tblBranch->subscribedOption = $subscribedOption;
        $tblBranch->branchAELoginUrl = $branchAELoginUrl;
        $tblBranch->allowLMRAEToAccessDocs = $allowLMRAEToAccessDocs;
        $tblBranch->allowLMRToOnOffAgentLogin = $allowLMRToOnOffAgentLogin;
        $tblBranch->allowLMRAEToEditCommission = $allowLMRAEToEditCommission;
        $tblBranch->serviceProvider = $serviceProvider;
        $tblBranch->allowAddOn = $allowAddOn;
        $tblBranch->timeZone = $timeZone;
        $tblBranch->allowToUpdateFileAdminSection = $allowToUpdateFileAdminSection;
        $tblBranch->sendMarketingEmail = $sendMarketingEmail;
        $tblBranch->displayLinkToFICO = intval($displayLinkToFICO);
        $tblBranch->allowToCopyFile = $allowToCopyFile;

        if ($opt != 'profile') {
            $tblBranch->LMRBccEmail = $LMRBccEmail;
            $tblBranch->LMRCcEmail = $LMRCcEmail;
        }

        if ($userRole == 'Super') {
            $tblBranch->processingCompanyId = $processingCompanyId;
        }
        if ($userType == 'PLO') {
            $tblBranch->askReferralAgent = $askReferralAgent;
            $tblBranch->askPaymentBeforeLMR = $askPaymentBeforeLMR;
            $tblBranch->allowClientToUploadDocs = $allowClientToUploadDocs;
        }
        if (isset($infoArray['allowBranchToLogin'])) {
            $tblBranch->allowBranchToLogin = $infoArray['allowBranchToLogin'];
        }
        if (isset($infoArray['allowLMRToEditAgentProfile'])) {
            $tblBranch->allowLMRToEditAgentProfile = $infoArray['allowLMRToEditAgentProfile'];
        }
        if (isset($infoArray['phone'])) {
            $tblBranch->directPhone = $infoArray['phone'];
        }
        if (isset($infoArray['allowToAddAgent'])) {
            $tblBranch->allowToAddAgent = $infoArray['allowToAddAgent'];
        }
        if (isset($infoArray['useMyServerSetting'])) {
            $tblBranch->useMyServerSetting = $infoArray['useMyServerSetting'];
        }
        if (isset($infoArray['allowBranchToCreateFiles'])) {
            $tblBranch->allowBranchToCreateFiles = $infoArray['allowBranchToCreateFiles'];
        }
        if (isset($infoArray['allowBranchToCreateTasks'])) {
            $tblBranch->allowBranchToCreateTasks = $infoArray['allowBranchToCreateTasks'];
        }
        if (isset($infoArray['allowBranchToSeeDashboard'])) {
            $tblBranch->allowBranchToSeeDashboard = $infoArray['allowBranchToSeeDashboard'];
        }
        if (isset($infoArray['accessRestriction'])) {
            $tblBranch->accessRestriction = $infoArray['accessRestriction'];
        }
        if (isset($infoArray['AllowToCreateAloware'])) {
            $tblBranch->AllowToCreateAloware = $infoArray['AllowToCreateAloware'];
        }
        if (isset($infoArray['seePrivate'])) {
            $tblBranch->seePrivate = $infoArray['seePrivate'];
        }

        if (isset($infoArray['allowedToEditOwnNotes'])) {
            $tblBranch->allowedToEditOwnNotes = $infoArray['allowedToEditOwnNotes'];
        }
        if (isset($infoArray['allowtoEditCCInfo'])) {
            $tblBranch->allowToEditCCInfo = $infoArray['allowtoEditCCInfo'];
        }
        if (isset($infoArray['allowEmailCampaign'])) {
            $tblBranch->allowEmailCampaign = $infoArray['allowEmailCampaign'];
        }
        if (isset($infoArray['permissionToREST'])) {
            $tblBranch->permissionToREST = $infoArray['permissionToREST'];
        }
        if (isset($infoArray['allowedToExcelReport'])) {
            $tblBranch->allowedToExcelReport = $infoArray['allowedToExcelReport'];
        }
        if (isset($infoArray['changeDIYPlan'])) {
            $tblBranch->changeDIYPlan = $infoArray['changeDIYPlan'];
        }
        if (isset($infoArray['globalAcctNo'])) {
            $tblBranch->globalAcctNo = $infoArray['globalAcctNo'];
        }
        if (isset($infoArray['sponsorName'])) {
            $tblBranch->sponsorName = $infoArray['sponsorName'];
        }
        if (isset($infoArray['allowToSendHomeownerLink'])) {
            $tblBranch->allowToSendHomeownerLink = $infoArray['allowToSendHomeownerLink'];
        }
        if (isset($infoArray['isPrimary'])) {
            $tblBranch->isPrimary = $infoArray['isPrimary'];
        }
        if (isset($infoArray['allowToLASubmit'])) {
            $tblBranch->allowToLASubmit = $infoArray['allowToLASubmit'];
        }
        if (isset($infoArray['allowToCFPBSubmit'])) {
            $tblBranch->allowToCFPBSubmit = $infoArray['allowToCFPBSubmit'];
        }
        if (isset($infoArray['subscribeToHOME'])) {
            $tblBranch->subscribeToHOME = $infoArray['subscribeToHOME'];
        }
        if (isset($infoArray['allowToSendFax'])) {
            $tblBranch->allowToSendFax = $infoArray['allowToSendFax'];
        }
        if (isset($infoArray['allowBranchToSeeCommission'])) {
            $tblBranch->allowBranchToSeeCommission = $infoArray['allowBranchToSeeCommission'];
        }
        if (isset($infoArray['allowClientToAccessDocs'])) {
            $tblBranch->allowClientToAccessDocs = $infoArray['allowClientToAccessDocs'];
        }
        if (isset($infoArray['allowToSendFileDesignation'])) {
            $tblBranch->allowToSendFileDesignation = $infoArray['allowToSendFileDesignation'];
        }
        if (isset($infoArray['allowBranchToSeePublicNotes'])) {
            $tblBranch->allowBranchToSeePublicNotes = $infoArray['allowBranchToSeePublicNotes'];
        }
        if (isset($infoArray['mailingAddress'])) {
            $tblBranch->mailingAddress = $infoArray['mailingAddress'];
        }
        if (isset($infoArray['mailingCity'])) {
            $tblBranch->mailingCity = $infoArray['mailingCity'];
        }
        if (isset($infoArray['mailingState'])) {
            $tblBranch->mailingState = $infoArray['mailingState'];
        }
        if (isset($infoArray['mailingZipCode'])) {
            $tblBranch->mailingZipCode = $infoArray['mailingZipCode'];
        }
        if (isset($infoArray['bankName'])) {
            $tblBranch->bankName = $infoArray['bankName'];
        }
        if (isset($infoArray['routingNumber'])) {
            $tblBranch->routingNumber = $infoArray['routingNumber'];
        }
        if (isset($infoArray['accountNumber'])) {
            $tblBranch->accountNumber = $infoArray['accountNumber'];
        }
        if (isset($infoArray['county'])) {
            $tblBranch->county = $infoArray['county'];
        }
        if (isset($infoArray['allowToViewCFPBPipeline'])) {
            $tblBranch->allowToViewCFPBPipeline = $infoArray['allowToViewCFPBPipeline'];
        }
        if (isset($infoArray['allowToAccessRAM'])) {
            $tblBranch->allowToAccessRAM = $infoArray['allowToAccessRAM'];
        }
        if (isset($infoArray['allowBranchManagerToLogin'])) {
            $tblBranch->allowBranchManagerToLogin = $infoArray['allowBranchManagerToLogin'];
        }
        if (isset($infoArray['allowEditToIR'])) {
            $tblBranch->allowEditToIR = $infoArray['allowEditToIR'];
        }
        if (isset($infoArray['allowWorkflowEdit'])) {
            $tblBranch->allowWorkflowEdit = $infoArray['allowWorkflowEdit'];
        }
        if (isset($infoArray['allowToLockLoanFileBranch'])) {
            $tblBranch->allowToLockLoanFileBranch = $infoArray['allowToLockLoanFileBranch'];
        }
        if (isset($infoArray['allowBranchToGetBorrowerUploadDocsNotification'])) {
            $tblBranch->allowBranchToGetBorrowerUploadDocsNotification = $infoArray['allowBranchToGetBorrowerUploadDocsNotification'];
        }
        if (isset($infoArray['allowToViewMarketPlace'])) {
            $tblBranch->allowToViewMarketPlace = $infoArray['allowToViewMarketPlace'];
        }
        if (isset($infoArray['allowcaptcha'])) {
            $tblBranch->allowcaptcha = $infoArray['allowcaptcha'];
        }
        if (isset($infoArray['shareThisFile'])) {
            $tblBranch->shareThisFile = $infoArray['shareThisFile'];
        }
        if (isset($infoArray['allowToSubmitOffer'])) {
            $tblBranch->allowToSubmitOffer = $infoArray['allowToSubmitOffer'];
        }
        if (isset($infoArray['allowToViewCreditScreening'])) {
            $tblBranch->allowToViewCreditScreening = $infoArray['allowToViewCreditScreening'];
        }
        if (isset($infoArray['enable2FAAuthentication'])) {
            $tblBranch->enable2FAAuthentication = $infoArray['enable2FAAuthentication'];
        }
        if (isset($infoArray['TwoFAType'])) {
            $tblBranch->TwoFAType = $infoArray['TwoFAType'];
        }
        if (isset($infoArray['userPriceEngineStatus'])) {
            $tblBranch->userPriceEngineStatus = $infoArray['userPriceEngineStatus'];
        }
        if (isset($_REQUEST['allowToupdateFileAndClient'])
            || (Strings::GetSess('userRole') == 'Manager'
                || Strings::GetSess('userRole') == 'Super')
        ) {
            $allowToupdateFileAndClient = Arrays::getArrayValue('allowToupdateFileAndClient', $_REQUEST);
            $tblBranch->allowToupdateFileAndClient = is_array($allowToupdateFileAndClient) ? implode(',', $allowToUpdateFileAdminSection) : $allowToupdateFileAndClient;
        }
        if (isset($_REQUEST['thirdPartyServices'])) {
            $tblBranch->thirdPartyServices = $_REQUEST['thirdPartyServices'];
        }
        if (Request::isset('thirdPartyServicesLegalDocs')) {
            $tblBranch->thirdPartyServicesLegalDocs = Request::GetClean('thirdPartyServicesLegalDocs');
        }

        if (isset($infoArray['notifyBODocUpload'])) {
            $tblBranch->notifyBODocUpload = $infoArray['notifyBODocUpload'];
        }
        if (isset($infoArray['notifyLODocUpload'])) {
            $tblBranch->notifyLODocUpload = $infoArray['notifyLODocUpload'];
        }
        if (isset($infoArray['notifyBrokerDocUpload'])) {
            $tblBranch->notifyBrokerDocUpload = $infoArray['notifyBrokerDocUpload'];
        }
        if (isset($infoArray['notifyDocUploadRequest'])) {
            $tblBranch->notifyDocUploadRequest = $infoArray['notifyDocUploadRequest'];
        }
        if (isset($infoArray['notifyNewFileCreated'])) {
            $tblBranch->notifyNewFileCreated = $infoArray['notifyNewFileCreated'];
        }
        if (isset($infoArray['DocUploadBcc'])) {
            $tblBranch->DocUploadBcc = $infoArray['DocUploadBcc'];
        }
        if (isset($infoArray['DocUploadCc'])) {
            $tblBranch->DocUploadCc = $infoArray['DocUploadCc'];
        }
        if (isset($infoArray['loanpassLogin'])) {
            $tblBranch->loanpassLogin = $infoArray['loanpassLogin'];
        }
        if (isset($infoArray['loanpassPassword'])) {
            $tblBranch->loanpassPassword = $infoArray['loanpassPassword'];
        }
        if (isset($infoArray['allowToAccessInternalLoanProgram'])) {
            $tblBranch->allowToAccessInternalLoanProgram = $infoArray['allowToAccessInternalLoanProgram'];
        }
        if (isset($infoArray['allowedToDeleteUplodedDocs'])) {
            $tblBranch->allowedToDeleteUplodedDocs = $infoArray['allowedToDeleteUplodedDocs'];
        }

        if (isset($infoArray['allowToViewAutomationPopup'])) {
            $tblBranch->allowToViewAutomationPopup = $infoArray['allowToViewAutomationPopup'];
        }

        if (isset($infoArray['allowToMassUpdate'])) {
            $tblBranch->allowToMassUpdate = $infoArray['allowToMassUpdate'];
        }

        if (isset($infoArray['allowToEditLoanStage'])) {
            $tblBranch->allowToEditLoanStage = $infoArray['allowToEditLoanStage'];
        }

        if (Request::isset('allowToManageDraws')) {
            $tblBranch->allowToManageDraws = $infoArray['allowToManageDraws'];
        }

        if (Request::isset('allowUsersToSendEsignLinkToAll')) {
            $tblBranch->allowUsersToSendEsignLinkToAll = intval(Request::GetClean('allowUsersToSendEsignLinkToAll'));
        }

        $tblBranch->Save();
        return 1;
    }

    /**
     * saveBranchInfo
     * Update LMR Executive in admin/ LMR Profile. Input is LMR info as array.
     *
     * @param mixed $infoArray
     * @access public
     *
     * <AUTHOR> <<EMAIL>>
     * @example
     */

    public static function getReport($infoArray): array
    {
        $opt = '';
        $executiveCreatedDate = date('Y-m-d');
        $updateCount = 0;
        $askReferralAgent = 0;
        $askPaymentBeforeLMR = 1;
        $allowClientToUploadDocs = 0;
        $insertCount = 0;

        $executiveId = $infoArray['executiveId'] ?? 0;
        $LMRExecutive = $infoArray['LMRExecutive'] ?? '';
        $LMRFirstName = $infoArray['LMRFirstName'] ?? '';
        $LMRLastName = $infoArray['LMRLastName'] ?? '';

        $pwd = $infoArray['pwd'] ?? '';
        $email = $infoArray['email'] ?? '';
        $company = $infoArray['company'] ?? '';
        $LMRBccEmail = '';
        $LMRCcEmail = '';
        if ($opt != 'profile') {
            $LMRBccEmail = $infoArray['LMRBccEmail'] ?? '';
            $LMRCcEmail = $infoArray['LMRCcEmail'] ?? '';
        }

        $tollFree = $infoArray['tollFree'] ?? '';
        $fax = $infoArray['fax'] ?? '';
        $website = $infoArray['website'] ?? '';

        $branchAELoginUrl = $infoArray['branchAELoginUrl'] ?? '';

        $cellNumber = $infoArray['cellNumber'] ?? '';
        $address1 = $infoArray['address1'] ?? '';
        $city = $infoArray['city'] ?? '';
        $state = $infoArray['state'] ?? '';
        $zipCode = $infoArray['zipCode'] ?? '';
        $paymentToCompany = $infoArray['paymentToCompany'] ?? '';
        $userRole = $infoArray['userRole'] ?? '';
        $processingCompanyId = $infoArray['processingCompanyId'] ?? '';
        $userType = $infoArray['userType'] ?? '';
        $allowLMRAEToEditFile = $infoArray['allowLMRAEToEditFile'] ?? '';
        $allowAgentToSeeFile = $infoArray['allowAgentToSeeFile'] ?? 1;

        if ($userType == 'PLO') {
            $askReferralAgent = $infoArray['askReferralAgent'] ?? '';
            $askPaymentBeforeLMR = $infoArray['askPaymentBeforeLMR'] ?? '';
            $allowClientToUploadDocs = $infoArray['allowClientToUploadDocs'] ?? '';
        }

        $subscribedOption = $infoArray['subscribedOption'] ?? 1;
        $serviceProvider = $infoArray['serviceProvider'] ?? '';
        $allowAddOn = $infoArray['allowAddOn'] ?? 0;
        $timeZone = $infoArray['timeZone'] ?? '';
        $sendMarketingEmail = $infoArray['sendMarketingEmail'] ?? 1;
        $displayLinkToFICO = $infoArray['displayLinkToFICO'] ?? 1;
        $allowBranchToLogin = $infoArray['allowBranchToLogin'] ?? 0;

        $allowLMRAEToAccessDocs = $infoArray['allowLMRAEToAccessDocs'];
        $allowLMRToOnOffAgentLogin = $infoArray['allowLMRToOnOffAgentLogin'];
        $allowLMRAEToEditCommission = $infoArray['allowLMRAEToEditCommission'];

        $allowToUpdateFileAdminSection = $infoArray['allowToUpdateFileAdminSection'] ?? 0;
        $allowToCopyFile = $infoArray['allowToCopyFile'] ?? 0;

        $allowLMRToEditAgentProfile = $infoArray['allowLMRToEditAgentProfile'] ?? 0;
        $allowToCreateBranch = $infoArray['allowToCreateBranch'] ?? 0;
        $WFIDsArray = $infoArray['WFIDs'] ?? [];
        $allowedToDeleteUplodedDocs = $infoArray['allowedToDeleteUplodedDocs'] ?? 0;
        if (isset($infoArray['routingNumber'])) {
            $infoArray['routingNumber'] = trim(cypher::myEncryption($infoArray['routingNumber']));
        }

        if (isset($infoArray['accountNumber'])) {
            $infoArray['accountNumber'] = trim(cypher::myEncryption($infoArray['accountNumber']));
        }

        if ($executiveId > 0) {
            $updateCount = self::update(
                $executiveId,
                $infoArray,
                $LMRExecutive,
                $LMRFirstName,
                $LMRLastName,
                $company,
                $tollFree,
                $fax,
                $website,
                $cellNumber,
                $address1,
                $city,
                $state,
                $zipCode,
                $paymentToCompany,
                $userType,
                $allowLMRAEToEditFile,
                $allowAgentToSeeFile,
                $subscribedOption,
                $branchAELoginUrl,
                $allowLMRAEToAccessDocs,
                $allowLMRToOnOffAgentLogin,
                $allowLMRAEToEditCommission,
                $serviceProvider,
                $allowAddOn,
                $timeZone,
                $allowToUpdateFileAdminSection,
                $sendMarketingEmail,
                $displayLinkToFICO,
                $opt,
                $LMRBccEmail,
                $LMRCcEmail,
                $userRole,
                $processingCompanyId,
                $askReferralAgent,
                $askPaymentBeforeLMR,
                $allowClientToUploadDocs,
                $allowedToDeleteUplodedDocs,
                $allowToCopyFile
            );
        } else {
            $res = self::insert(
                $userRole,
                $allowToCreateBranch,
                $userType,
                $infoArray,
                $LMRExecutive,
                $LMRFirstName,
                $LMRLastName,
                $email,
                $company,
                $pwd,
                $LMRBccEmail,
                $LMRCcEmail,
                $tollFree,
                $fax,
                $website,
                $branchAELoginUrl,
                $cellNumber,
                $address1,
                $city,
                $state,
                $zipCode,
                $paymentToCompany,
                $executiveCreatedDate,
                $allowLMRAEToEditFile,
                $allowAgentToSeeFile,
                $subscribedOption,
                $allowLMRAEToAccessDocs,
                $allowLMRToOnOffAgentLogin,
                $allowLMRAEToEditCommission,
                $serviceProvider,
                $allowAddOn,
                $timeZone,
                $allowToUpdateFileAdminSection,
                $sendMarketingEmail,
                $displayLinkToFICO,
                $processingCompanyId,
                $askReferralAgent,
                $askPaymentBeforeLMR,
                $allowClientToUploadDocs,
                $allowBranchToLogin,
                $allowLMRToEditAgentProfile,
                $allowToCopyFile,
                $allowedToDeleteUplodedDocs
            );
            $executiveId = $res['executiveId'];
            $insertCount = $res['insertCount'];
        }
        if ($executiveId && isset($infoArray['WFIDs'])) {

            saveUserAssignedWorkflow::getReport([
                'WFIDs' => $WFIDsArray,
                'UID' => $executiveId,
                'UType' => 'Branch',
                'updatedBy' => $infoArray['userNumber'],
                'updatedType' => $infoArray['userGroup'],
            ]);
        }

        if (Request::isset('thirdPartyServices')) {
            saveThirdPartyServicesUserDetails::getReport($executiveId, 'Branch', $_REQUEST['thirdPartyServicesData']);
        }


        return [
            'insertCount' => $insertCount,
            'updateCount' => $updateCount,
            'executiveId' => $executiveId
        ];
    }
}
