<?php

namespace models\composite\oEmployee;

use models\Database2;
use models\lendingwise\db\tblAdminUsers_db;
use models\lendingwise\tblAdminUsers;
use models\lendingwise\tblFromEmailUsers;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;
use models\types\strongType;
use models\composite\oThirdPartyServices\saveThirdPartyServicesUserDetails;

/**
 *
 */
class saveEmployeeInfo extends strongType
{
    /**
     * @param $infoArray
     * @return array
     */
    public static function getReport($infoArray): array
    {
        $empCreatedDate = date('Y-m-d');
        $newProcessorId = 0;
        $updateCount = 0;
        $insertCount = 0;

        $email = $infoArray['email'];
        $processorName = $infoArray['processorName'];
        $processorLName = $infoArray['processorLName'];
        $pwd = $infoArray['pwd'];
        $tollFree = $infoArray['tollFree'];
        $cellNumber = $infoArray['cellNumber'];
        $fax = $infoArray['fax'];
        $role = $infoArray['role'];
        $processingCompanyId = $infoArray['processingCompanyId'];
        $userRole = $infoArray['userRole'];
        $processorId = $infoArray['processorId'] ?? 0;
        $allowToViewAllFiles = $infoArray['allowToViewAllFiles'] ?? 1;
        $serviceProvider = $infoArray['serviceProvider'] ?? '';
        $timeZone = $infoArray['timeZone'] ?? '';
        $allowExcelDownload = $infoArray['allowExcelDownload'] ?? 0;
        $sendMarketingEmail = $infoArray['sendMarketingEmail'] ?? 1;
        $allowToListIn3PartyForm = $infoArray['allowToListIn3PartyForm'] ?? 1;
        $allowEmpToLogin = $infoArray['allowEmpToLogin'] ?? 0;
        $phone = $infoArray['phone'] ?? '';
        $allowEmpToCreateFiles = $infoArray['allowEmpToCreateFiles'] ?? 1;
        $allowEmpToCreateTasks = $infoArray['allowEmpToCreateTasks'] ?? 1;
        $allowEmpToSeeDashboard = $infoArray['allowEmpToSeeDashboard'] ?? 1;
        $accessRestriction = $infoArray['accessRestriction'] ?? 1;
        $seePrivate = $infoArray['seePrivate'] ?? 0;
        $allowedToDeleteUplodedDocs = $infoArray['allowedToDeleteUplodedDocs'] ?? 1;
        $allowedToEditOwnNotes = $infoArray['allowedToEditOwnNotes'] ?? 1;
        $seeBilling = $infoArray['seeBilling'] ?? 1;
        $alternatePC = $infoArray['alternatePC'] ?? '';
        $permissionToREST = $infoArray['permissionToREST'] ?? 1;
        $allowEmailCampaign = $infoArray['allowEmailCampaign'] ?? 1;
        $allowBOToEditLMRFile = $infoArray['allowBOToEditLMRFile'] ?? 0;
        $changeDIYPlan = $infoArray['changeDIYPlan'] ?? 0;
        $activeStatus = $infoArray['activeStatus'] ?? 1;
        $allowToSendHomeownerLink = $infoArray['allowToSendHomeownerLink'] ?? 0;
        $allowToUpdateFileAdminSection = $infoArray['allowToUpdateFileAdminSection'] ?? 0;
        $allowToLASubmit = $infoArray['allowToLASubmit'] ?? 0;
        $allowToCFPBSubmit = $infoArray['allowToCFPBSubmit'] ?? 0;
        $allowToViewCFPBPipeline = $infoArray['allowToViewCFPBPipeline'] ?? 0;
        $serviceFeeRate = $infoArray['serviceFeeRate'] ?? '';
        $subscribeToHOME = $infoArray['subscribeToHOME'] ?? 0;
        $allowToSendFax = $infoArray['allowToSendFax'] ?? 0;
        $allowEmployeeToEditCommission = $infoArray['allowEmployeeToEditCommission'] ?? 1;
        $allowEmployeeToSeeCommission = $infoArray['allowEmployeeToSeeCommission'] ?? 1;
        $allowToSendFileDesignation = trim($infoArray['allowToSendFileDesignation'] ?? 1);
        $allowToChangeOrAssignBranchForFile = trim($infoArray['allowToChangeOrAssignBranchForFile'] ?? 1);
        $allowToGetBorrowerUploadDocsNotification = trim($infoArray['allowToGetBorrowerUploadDocsNotification'] ?? 1);
        $allowToViewMarketPlace = trim($infoArray['allowToViewMarketPlace'] ?? 1);
        $allowToLockLoanFileEmpl = trim($infoArray['allowToLockLoanFileEmpl'] ?? 1);
        $allowToSeeBillingSectionForFile = trim($infoArray['allowToSeeBillingSectionForFile'] ?? 0);
        $barNo = trim($infoArray['barNo'] ?? '');
        $attorneyRate = Strings::replaceCommaValues($infoArray['attorneyRate'] ?? null);
        $paralegalRate = Strings::replaceCommaValues($infoArray['paralegalRate'] ?? '');
        $empState = trim($infoArray['empState'] ?? '');
        $empCity = trim($infoArray['empCity'] ?? '');
        $empAddress = trim($infoArray['empAddress'] ?? '');
        $zipCode = trim($infoArray['zipCode'] ?? '');
        $allowEmpToSeePublicNotes = trim($infoArray['allowEmpToSeePublicNotes'] ?? 1);
        $WFIDsArray = $infoArray['WFIDs'] ?? [];
        $empCounty = trim($infoArray['empCounty'] ?? '');
        $notifyBODocUpload = $infoArray['notifyBODocUpload'] ?? 0;
        $notifyBranchDocUpload = $infoArray['notifyBranchDocUpload'] ?? 0;
        $notifyLODocUpload = $infoArray['notifyLODocUpload'] ?? 0;
        $notifyBrokerDocUpload = $infoArray['notifyBrokerDocUpload'] ?? 0;
        $notifyDocUploadRequest = $infoArray['notifyDocUploadRequest'] ?? 0;
        $notifyNewFileCreated = $infoArray['notifyNewFileCreated'] ?? 0;
        $loanpassLogin = $infoArray['loanpassLogin'] ?? '';
        $loanpassPassword = $infoArray['loanpassPassword'] ?? '';
        $allowUserToSendMsgToBorrower = $infoArray['allowUserToSendMsgToBorrower'] ?? '1';
        $allowEmpToCopyFile = $infoArray['allowEmpToCopyFile'] ?? '1';

        $allowToViewAutomationPopup = $infoArray['allowToViewAutomationPopup'] ?? 0;
        $allowToControlAutomationPopup = $infoArray['allowToControlAutomationPopup'] ?? 0;

        $allowToSelectFromEmail = isset($_REQUEST['allowToSelectFromEmail']) ? Request::GetClean('allowToSelectFromEmail') : 0;
        $allowEmpToSeeAllEmails = $infoArray['allowEmpToSeeAllEmails'] ?? 0;
        $allowToMassUpdate = $infoArray['allowToMassUpdate'] ?? 0;
        $allowToManageDraws = $infoArray['allowToManageDraws'] ?? 0;
        $allowToSeeFeesInDashboard = Request::isset('allowToSeeFeesInDashboard') ? Request::GetClean('allowToSeeFeesInDashboard') : 0;
        $allowToEditLoanStage = Request::isset('allowToEditLoanStage') ? Request::GetClean('allowToEditLoanStage') : 0;


        if ($processorId > 0) {
            $tblAdminUsers = tblAdminUsers::Get([tblAdminUsers_db::COLUMN_AID => $processorId]);
            $tblAdminUsers->processorName = $processorName;
            $tblAdminUsers->processorLName = $processorLName;
            $tblAdminUsers->tollFree = $tollFree;
            $tblAdminUsers->cellNumber = $cellNumber;
            $tblAdminUsers->fax = $fax;
            $tblAdminUsers->role = $role;
            $tblAdminUsers->serviceProvider = $serviceProvider;
            if (array_key_exists('timeZone', $infoArray)) $tblAdminUsers->timeZone = $timeZone;
            if ($userRole == 'Super' || $userRole == 'Manager') {
                $tblAdminUsers->processingCompanyId = $processingCompanyId;
                if (array_key_exists('allowExcelDownload', $infoArray)) {
                    $tblAdminUsers->allowExcelDownload = $allowExcelDownload;
                }
                if (array_key_exists('allowToViewAllFiles', $infoArray)) {
                    $tblAdminUsers->allowToViewAllFiles = $allowToViewAllFiles;
                }
            }
            if (array_key_exists('sendMarketingEmail', $infoArray)) $tblAdminUsers->sendMarketingEmail = $sendMarketingEmail;
            if (array_key_exists('allowToListIn3PartyForm', $infoArray)) $tblAdminUsers->allowToListIn3PartyForm = $allowToListIn3PartyForm;
            if (array_key_exists('allowEmpToLogin', $infoArray)) $tblAdminUsers->allowEmpToLogin = $allowEmpToLogin;
            if (array_key_exists('phone', $infoArray)) $tblAdminUsers->directPhone = $phone;
            if (array_key_exists('allowEmpToCreateFiles', $infoArray)) $tblAdminUsers->allowEmpToCreateFiles = $allowEmpToCreateFiles;
            if (array_key_exists('allowEmpToCreateTasks', $infoArray)) $tblAdminUsers->allowEmpToCreateTasks = $allowEmpToCreateTasks;
            if (array_key_exists('allowEmpToSeeDashboard', $infoArray)) $tblAdminUsers->allowEmpToSeeDashboard = $allowEmpToSeeDashboard;
            if (array_key_exists('accessRestriction', $infoArray)) $tblAdminUsers->accessRestriction = $accessRestriction;
            if (array_key_exists('seePrivate', $infoArray)) $tblAdminUsers->seePrivate = $seePrivate;
            if (array_key_exists('allowedToDeleteUplodedDocs', $infoArray)) $tblAdminUsers->allowedToDeleteUplodedDocs = $allowedToDeleteUplodedDocs;
            if (array_key_exists('allowedToEditOwnNotes', $infoArray)) $tblAdminUsers->allowedToEditOwnNotes = $allowedToEditOwnNotes;
            if (array_key_exists('seeBilling', $infoArray)) $tblAdminUsers->seeBilling = $seeBilling;
            if (array_key_exists('alternatePC', $infoArray)) $tblAdminUsers->alternatePC = $alternatePC;
            if (array_key_exists('permissionToREST', $infoArray)) $tblAdminUsers->permissionToREST = $permissionToREST;
            if (array_key_exists('allowEmailCampaign', $infoArray)) $tblAdminUsers->allowEmailCampaign = $allowEmailCampaign;
            if (array_key_exists('allowBOToEditLMRFile', $infoArray)) $tblAdminUsers->allowBOToEditLMRFile = $allowBOToEditLMRFile;
            if (array_key_exists('changeDIYPlan', $infoArray)) $tblAdminUsers->changeDIYPlan = $changeDIYPlan;
            if (array_key_exists('allowToSendHomeownerLink', $infoArray)) $tblAdminUsers->allowToSendHomeownerLink = $allowToSendHomeownerLink;
            if (array_key_exists('allowToUpdateFileAdminSection', $infoArray)) $tblAdminUsers->allowToUpdateFileAdminSection = $allowToUpdateFileAdminSection;
            if (array_key_exists('allowToLASubmit', $infoArray)) {
                $tblAdminUsers->allowToLASubmit = $allowToLASubmit;
            }
            if (array_key_exists('allowToCFPBSubmit', $infoArray)) {
                $tblAdminUsers->allowToCFPBSubmit = $allowToCFPBSubmit;
            }
            if (array_key_exists('subscribeToHOME', $infoArray)) $tblAdminUsers->subscribeToHOME = $subscribeToHOME;
            if (array_key_exists('allowToSendFax', $infoArray)) $tblAdminUsers->allowToSendFax = $allowToSendFax;
            if (array_key_exists('allowEmployeeToEditCommission', $infoArray)) $tblAdminUsers->allowEmployeeToEditCommission = $allowEmployeeToEditCommission;
            if (array_key_exists('allowEmployeeToSeeCommission', $infoArray)) $tblAdminUsers->allowEmployeeToSeeCommission = $allowEmployeeToSeeCommission;
            if (array_key_exists('allowToSendFileDesignation', $infoArray)) $tblAdminUsers->allowToSendFileDesignation = $allowToSendFileDesignation;
            if (array_key_exists('barNo', $infoArray)) $tblAdminUsers->barNo = $barNo;
            if (array_key_exists('attorneyRate', $infoArray)) $tblAdminUsers->attorneyRate = $attorneyRate;
            if (array_key_exists('paralegalRate', $infoArray)) $tblAdminUsers->paralegalRate = $paralegalRate;
            if (array_key_exists('allowEmpToSeePublicNotes', $infoArray)) $tblAdminUsers->allowEmpToSeePublicNotes = $allowEmpToSeePublicNotes;
            if (array_key_exists('empAddress', $infoArray)) $tblAdminUsers->empAddress = $empAddress;
            if (array_key_exists('empCity', $infoArray)) $tblAdminUsers->empCity = $empCity;
            if (array_key_exists('empState', $infoArray)) $tblAdminUsers->empState = $empState;
            if (array_key_exists('empCounty', $infoArray)) $tblAdminUsers->empCounty = $empCounty;
            if (array_key_exists('zipCode', $infoArray)) $tblAdminUsers->empZip = $zipCode;
            if (array_key_exists('allowToChangeOrAssignBranchForFile', $infoArray)) $tblAdminUsers->allowToChangeOrAssignBranchForFile = $allowToChangeOrAssignBranchForFile;
            if (array_key_exists('allowToLockLoanFileEmpl', $infoArray)) $tblAdminUsers->allowToLockLoanFileEmpl = $allowToLockLoanFileEmpl;
            if (array_key_exists('allowToGetBorrowerUploadDocsNotification', $infoArray)) $tblAdminUsers->allowToGetBorrowerUploadDocsNotification = $allowToGetBorrowerUploadDocsNotification;
            if (array_key_exists('allowToViewMarketPlace', $infoArray)) $tblAdminUsers->allowToViewMarketPlace = $allowToViewMarketPlace;
            if (array_key_exists('allowToSeeBillingSectionForFile', $infoArray)) $tblAdminUsers->allowToSeeBillingSectionForFile = $allowToSeeBillingSectionForFile;
            if (array_key_exists('allowToViewCFPBPipeline', $infoArray)) $tblAdminUsers->allowToViewCFPBPipeline = $allowToViewCFPBPipeline;
            if (array_key_exists('serviceFeeRate', $infoArray)) $tblAdminUsers->serviceFeeRate = $serviceFeeRate;
            if (array_key_exists('allowToAccessRAM', $infoArray)) $tblAdminUsers->allowToAccessRAM = $infoArray['allowToAccessRAM'];
            if (array_key_exists('allowEmpToCreateBranch', $infoArray)) $tblAdminUsers->allowEmpToCreateBranch = $infoArray['allowEmpToCreateBranch'];
            if (isset($_REQUEST['allowEmpToCreateAloware'])) $tblAdminUsers->allowEmpToCreateAloware = $infoArray['allowEmpToCreateAloware'];
            if (array_key_exists('convertNewBRIntoEmpOwnBR', $infoArray)) $tblAdminUsers->convertNewBRIntoEmpOwnBR = $infoArray['convertNewBRIntoEmpOwnBR'];
            if (array_key_exists('allowEmpToCreateAgent', $infoArray)) $tblAdminUsers->allowEmpToCreateAgent = $infoArray['allowEmpToCreateAgent'];
            if (array_key_exists('allowEmpToSeeAgent', $infoArray)) $tblAdminUsers->allowEmpToSeeAgent = $infoArray['allowEmpToSeeAgent'];
            if (isset($_REQUEST['allowToupdateFileAndClient'])
                || (Strings::GetSess('userRole') == 'Manager'
                    || Strings::GetSess('userRole') == 'Super')
            ) {
                $vals = Arrays::getArrayValue('allowToupdateFileAndClient', $_REQUEST);
                if (is_array($vals)) {
                    $vals = implode(',', $vals);
                }
                $tblAdminUsers->allowToupdateFileAndClient = $vals;
            }
            if(Request::isset('thirdPartyServices')) $tblAdminUsers->thirdPartyServices = Request::GetClean('thirdPartyServices');    // | Allow to use thirdPartyServices CRAs.
            if(Request::isset('thirdPartyServicesLegalDocs')) $tblAdminUsers->thirdPartyServicesLegalDocs = Request::GetClean('thirdPartyServicesLegalDocs');    // | Allow to use thirdPartyServices Legal Docs.
            if (isset($_REQUEST['allowToViewContactsList'])) $tblAdminUsers->allowToViewContactsList = $_REQUEST['allowToViewContactsList'];
            if (isset($_REQUEST['shareThisFile'])) $tblAdminUsers->shareThisFile = $_REQUEST['shareThisFile'];
            if (isset($_REQUEST['allowToSubmitOffer'])) $tblAdminUsers->allowToSubmitOffer = $_REQUEST['allowToSubmitOffer'];
            if (isset($_REQUEST['allowToViewCreditScreening'])) $tblAdminUsers->allowToViewCreditScreening = $_REQUEST['allowToViewCreditScreening'];
            if (isset($_REQUEST['userPriceEngineStatus'])) $tblAdminUsers->userPriceEngineStatus = $_REQUEST['userPriceEngineStatus'];
            if (isset($_REQUEST['enable2FAAuthentication'])) $tblAdminUsers->enable2FAAuthentication = $_REQUEST['enable2FAAuthentication'];
            if (isset($_REQUEST['TwoFAType'])) $tblAdminUsers->TwoFAType = $_REQUEST['TwoFAType'];

            if (array_key_exists('notifyBODocUpload', $infoArray)) $tblAdminUsers->notifyBODocUpload = $notifyBODocUpload;
            if (array_key_exists('notifyBranchDocUpload', $infoArray)) $tblAdminUsers->notifyBranchDocUpload = $notifyBranchDocUpload;
            if (array_key_exists('notifyLODocUpload', $infoArray)) $tblAdminUsers->notifyLODocUpload = $notifyLODocUpload;
            if (array_key_exists('notifyBrokerDocUpload', $infoArray)) $tblAdminUsers->notifyBrokerDocUpload = $notifyBrokerDocUpload;
            if (array_key_exists('notifyDocUploadRequest', $infoArray)) $tblAdminUsers->notifyDocUploadRequest = $notifyDocUploadRequest;
            if (array_key_exists('notifyNewFileCreated', $infoArray)) $tblAdminUsers->notifyNewFileCreated = $notifyNewFileCreated;
            if (array_key_exists('loanpassLogin', $infoArray)) $tblAdminUsers->loanpassLogin = $loanpassLogin;
            if (array_key_exists('loanpassPassword', $infoArray)) $tblAdminUsers->loanpassPassword = $loanpassPassword;
            if (array_key_exists('allowUserToSendMsgToBorrower', $infoArray)) $tblAdminUsers->allowUserToSendMsgToBorrower = $allowUserToSendMsgToBorrower;
            if (array_key_exists('allowEmpToCopyFile', $infoArray)) $tblAdminUsers->allowEmpToCopyFile = $allowEmpToCopyFile;
            if (array_key_exists('allowToViewAutomationPopup', $infoArray)) $tblAdminUsers->allowToViewAutomationPopup = $allowToViewAutomationPopup;
            if (array_key_exists('allowToControlAutomationPopup', $infoArray)) $tblAdminUsers->allowToControlAutomationPopup = $allowToControlAutomationPopup;
            $tblAdminUsers->allowToSelectFromEmail = $allowToSelectFromEmail;
            if (array_key_exists('allowEmpToSeeAllEmails', $infoArray)) $tblAdminUsers->allowEmpToSeeAllEmails = $allowEmpToSeeAllEmails;
            if (array_key_exists('allowToMassUpdate', $infoArray)) $tblAdminUsers->allowToMassUpdate = $allowToMassUpdate;
            if (array_key_exists('allowToManageDraws', $infoArray)) $tblAdminUsers->allowToManageDraws = $allowToManageDraws;

            $tblAdminUsers->allowToSeeFeesInDashboard = $allowToSeeFeesInDashboard;
            $tblAdminUsers->allowToEditLoanStage = $allowToEditLoanStage;
            $tblAdminUsers->updatedBy = PageVariables::$userNumber ?? 0;
            $tblAdminUsers->updatedAt = Dates::Timestamp();


            $result = $tblAdminUsers->save();
            if ($result['error'] == '') {
                if ($result['affected_rows'] == 0) {
                    $updateCount = 1;
                } else {
                    $updateCount = $result['affected_rows'];
                }
            } else {
                $updateCount = 0;
            }

        } else {
            $params = [
                'email' => $email,
                'processorName' => $processorName,
                'processorLName' => $processorLName,
                'pwd' => $pwd,
                'tollFree' => $tollFree,
                'cellNumber' => $cellNumber,
                'fax' => $fax,
                'role' => $role,
                'empCreatedDate' => $empCreatedDate,
                'serviceProvider' => $serviceProvider,
                'timeZone' => $timeZone,
            ];

            if ($userRole == 'Sales' || $userRole == 'Super' || $userRole == 'Manager' || $userRole == 'Public') {
                $params['processingCompanyId'] = $processingCompanyId;
                $params['allowToViewAllFiles'] = $allowToViewAllFiles;
                $params['allowExcelDownload'] = $allowExcelDownload;
            }

            if (isset($infoArray['sendMarketingEmail'])) {
                $params['sendMarketingEmail'] = $sendMarketingEmail;
            }

            if (isset($infoArray['allowToListIn3PartyForm'])) {
                $params['allowToListIn3PartyForm'] = $allowToListIn3PartyForm;
            }

            if (isset($infoArray['allowEmpToLogin'])) {
                $params['allowEmpToLogin'] = $allowEmpToLogin;
            }

            if (isset($infoArray['phone'])) {
                $params['directPhone'] = $phone;
            }

            if (isset($infoArray['allowEmpToCreateFiles'])) {
                $params['allowEmpToCreateFiles'] = $allowEmpToCreateFiles;
            }

            if (isset($infoArray['allowEmpToCreateTasks'])) {
                $params['allowEmpToCreateTasks'] = $allowEmpToCreateTasks;
            }

            if (isset($infoArray['allowEmpToSeeDashboard'])) {
                $params['allowEmpToSeeDashboard'] = $allowEmpToSeeDashboard;
            }

            if (isset($infoArray['accessRestriction'])) {
                $params['accessRestriction'] = $accessRestriction;
            }

            if (isset($infoArray['seePrivate'])) {
                $params['seePrivate'] = $seePrivate;
            }

            if (isset($infoArray['allowedToDeleteUplodedDocs'])) {
                $params['allowedToDeleteUplodedDocs'] = $allowedToDeleteUplodedDocs;
            }

            if (isset($infoArray['allowedToEditOwnNotes'])) {
                $params['allowedToEditOwnNotes'] = $allowedToEditOwnNotes;
            }

            if (isset($infoArray['seeBilling'])) {
                $params['seeBilling'] = $seeBilling;
            }

            if (isset($infoArray['alternatePC'])) {
                $params['alternatePC'] = $alternatePC;
            }

            if (isset($infoArray['permissionToREST'])) {
                $params['permissionToREST'] = $permissionToREST;
            }

            if (isset($infoArray['allowEmailCampaign'])) {
                $params['allowEmailCampaign'] = $allowEmailCampaign;
            }

            if (isset($infoArray['allowBOToEditLMRFile'])) {
                $params['allowBOToEditLMRFile'] = $allowBOToEditLMRFile;
            }

            if (isset($infoArray['changeDIYPlan'])) {
                $params['changeDIYPlan'] = $changeDIYPlan;
            }

            if (isset($infoArray['activeStatus'])) {
                $params['activeStatus'] = $activeStatus;
            }

            if (isset($infoArray['allowToSendHomeownerLink'])) {
                $params['allowToSendHomeownerLink'] = $allowToSendHomeownerLink;
            }

            if (isset($infoArray['allowToUpdateFileAdminSection'])) {
                $params['allowToUpdateFileAdminSection'] = $allowToUpdateFileAdminSection;
            }

            if (isset($infoArray['allowToLASubmit'])) {
                $params['allowToLASubmit'] = $allowToLASubmit;
            }

            if (isset($infoArray['allowToCFPBSubmit'])) {
                $params['allowToCFPBSubmit'] = $allowToCFPBSubmit;
            }

            if (isset($infoArray['subscribeToHOME'])) {
                $params['subscribeToHOME'] = $subscribeToHOME;
            }

            if (isset($infoArray['allowToSendFax'])) {
                $params['allowToSendFax'] = $allowToSendFax;
            }

            if (isset($infoArray['allowEmployeeToEditCommission'])) {
                $params['allowEmployeeToEditCommission'] = $allowEmployeeToEditCommission;
            }

            if (isset($infoArray['allowEmployeeToSeeCommission'])) {
                $params['allowEmployeeToSeeCommission'] = $allowEmployeeToSeeCommission;
            }

            if (isset($infoArray['allowToSendFileDesignation'])) {
                $params['allowToSendFileDesignation'] = $allowToSendFileDesignation;
            }

            if (isset($infoArray['barNo'])) {
                $params['barNo'] = $barNo;
            }

            if (isset($infoArray['attorneyRate'])) {
                $params['attorneyRate'] = $attorneyRate;
            }

            if (isset($infoArray['paralegalRate'])) {
                $params['paralegalRate'] = $paralegalRate;
            }

            if (isset($infoArray['allowEmpToSeePublicNotes'])) {
                $params['allowEmpToSeePublicNotes'] = $allowEmpToSeePublicNotes;
            }

            if (isset($infoArray['empAddress'])) {
                $params['empAddress'] = $empAddress;
            }

            if (isset($infoArray['empCity'])) {
                $params['empCity'] = $empCity;
            }

            if (isset($infoArray['empState'])) {
                $params['empState'] = $empState;
            }

            if (isset($infoArray['zipCode'])) {
                $params['empZip'] = $zipCode;
            }

            if (isset($infoArray['empCounty'])) {
                $params['empCounty'] = $empCounty;
            }

            if (isset($infoArray['allowToChangeOrAssignBranchForFile'])) {
                $params['allowToChangeOrAssignBranchForFile'] = $allowToChangeOrAssignBranchForFile;
            }

            if (isset($infoArray['allowToLockLoanFileEmpl'])) {
                $params['allowToLockLoanFileEmpl'] = $allowToLockLoanFileEmpl;
            }

            if (isset($infoArray['allowToGetBorrowerUploadDocsNotification'])) {
                $params['allowToGetBorrowerUploadDocsNotification'] = $allowToGetBorrowerUploadDocsNotification;
            }

            if (isset($infoArray['allowToViewMarketPlace'])) {
                $params['allowToViewMarketPlace'] = $allowToViewMarketPlace;
            }

            if (isset($infoArray['allowToSeeBillingSectionForFile'])) {
                $params['allowToSeeBillingSectionForFile'] = $allowToSeeBillingSectionForFile;
            }

            if (isset($infoArray['allowToViewCFPBPipeline'])) {
                $params['allowToViewCFPBPipeline'] = $allowToViewCFPBPipeline;
            }

            if (isset($infoArray['serviceFeeRate'])) {
                $params['serviceFeeRate'] = Strings::replaceCommaValues($serviceFeeRate);
            }

            if (isset($infoArray['allowToAccessRAM'])) {
                $params['allowToAccessRAM'] = $infoArray['allowToAccessRAM'];
            }

            if (isset($infoArray['allowEmpToCreateBranch'])) {
                $params['allowEmpToCreateBranch'] = $infoArray['allowEmpToCreateBranch'];
            }

            if (isset($infoArray['convertNewBRIntoEmpOwnBR'])) {
                $params['convertNewBRIntoEmpOwnBR'] = $infoArray['convertNewBRIntoEmpOwnBR'];
            }

            if (isset($infoArray['allowEmpToCreateAgent'])) {
                $params['allowEmpToCreateAgent'] = $infoArray['allowEmpToCreateAgent'];
            }

            if (isset($infoArray['allowEmpToSeeAgent'])) {
                $params['allowEmpToSeeAgent'] = $infoArray['allowEmpToSeeAgent'];
            }

            if (isset($infoArray['notifyBODocUpload'])) {
                $params['notifyBODocUpload'] = $notifyBODocUpload;
            }

            if (isset($infoArray['notifyBranchDocUpload'])) {
                $params['notifyBranchDocUpload'] = $notifyBranchDocUpload;
            }

            if (isset($infoArray['notifyLODocUpload'])) {
                $params['notifyLODocUpload'] = $notifyLODocUpload;
            }

            if (isset($infoArray['notifyBrokerDocUpload'])) {
                $params['notifyBrokerDocUpload'] = $notifyBrokerDocUpload;
            }

            if (isset($infoArray['notifyDocUploadRequest'])) {
                $params['notifyDocUploadRequest'] = $notifyDocUploadRequest;
            }

            if (isset($infoArray['notifyNewFileCreated'])) {
                $params['notifyNewFileCreated'] = $notifyNewFileCreated;
            }

            if (isset($infoArray['loanpassLogin'])) {
                $params['loanpassLogin'] = $loanpassLogin;
            }

            if (isset($infoArray['loanpassPassword'])) {
                $params['loanpassPassword'] = $loanpassPassword;
            }
             if (isset($infoArray['allowUserToSendMsgToBorrower'])) {
                $params['allowUserToSendMsgToBorrower'] = $allowUserToSendMsgToBorrower;
            }

            if (isset($_REQUEST['allowEmpToCreateAloware'])) {
                $params['allowEmpToCreateAloware'] = $_REQUEST['allowEmpToCreateAloware'];
            }


            if (isset($_REQUEST['allowToupdateFileAndClient']) || (Strings::GetSess('userRole') == 'Manager' || Strings::GetSess('userRole') == 'Super')) {

                $allowToupdateFileAndClient = Arrays::getArrayValue('allowToupdateFileAndClient', $_REQUEST);
                if ($allowToupdateFileAndClient && !is_array($allowToupdateFileAndClient)) {
                    $allowToupdateFileAndClient = explode(',', $allowToupdateFileAndClient);
                }

                if (is_array($allowToupdateFileAndClient)) {
                    $allowToupdateFileAndClient = implode(',', $allowToupdateFileAndClient);
                }
                $params['allowToupdateFileAndClient'] = $allowToupdateFileAndClient;
            }

            if (isset($_REQUEST['thirdPartyServices'])) {
                $params['thirdPartyServices'] = $_REQUEST['thirdPartyServices'];
            }
            if (Request::isset('thirdPartyServicesLegalDocs')) {
                $params['thirdPartyServicesLegalDocs'] = Request::GetClean('thirdPartyServicesLegalDocs');
            }

            if (isset($_REQUEST['shareThisFile'])) {
                $params['shareThisFile'] = $_REQUEST['shareThisFile'];
            }

            if (isset($_REQUEST['allowToViewContactsList'])) {
                $params['allowToViewContactsList'] = $_REQUEST['allowToViewContactsList'];
            }

            if (isset($_REQUEST['allowToSubmitOffer'])) {
                $params['allowToSubmitOffer'] = $_REQUEST['allowToSubmitOffer'];
            }

            if (isset($_REQUEST['allowToViewCreditScreening'])) {
                $params['allowToViewCreditScreening'] = $_REQUEST['allowToViewCreditScreening'];
            }

            if (isset($_REQUEST['userPriceEngineStatus'])) {
                $params['userPriceEngineStatus'] = $_REQUEST['userPriceEngineStatus'];
            }

            if (isset($_REQUEST['enable2FAAuthentication'])) {
                $params['enable2FAAuthentication'] = $_REQUEST['enable2FAAuthentication'];
            }

            if (isset($_REQUEST['TwoFAType'])) {
                $params['TwoFAType'] = $_REQUEST['TwoFAType'];
            }
            if (isset($_REQUEST['allowEmpToCopyFile'])) {
                $params['allowEmpToCopyFile'] = $_REQUEST['allowEmpToCopyFile'];
            }

            $params['allowToViewAutomationPopup'] = $allowToViewAutomationPopup;
            $params['allowToControlAutomationPopup'] = $allowToControlAutomationPopup;
            $params['allowToSelectFromEmail'] = $allowToSelectFromEmail;
            $params['allowEmpToSeeAllEmails'] = $allowEmpToSeeAllEmails;
            $params['allowToMassUpdate'] = $allowToMassUpdate;
            $params['allowToManageDraws'] = $allowToManageDraws;
            $params['allowToSeeFeesInDashboard'] = $allowToSeeFeesInDashboard;
            $params['allowToEditLoanStage'] = $allowToEditLoanStage;

            $qryIns = '
                insert into tblAdminUsers(
                    ' . implode(', ', array_keys($params)) . '
                ) values (
                    :' . implode(', :', array_keys($params)) . '
                )
                ';

            $insertCount = 1;
            $newProcessorId = Database2::getInstance()->insert($qryIns, $params);
            $processorId = $newProcessorId;
        }
        if ($processorId > 0 && isset($infoArray['WFIDs'])) {
            saveUserAssignedWorkflow::getReport([
                'WFIDs' => $WFIDsArray,
                'UID' => $processorId,
                'UType' => 'Employee',
                'updatedBy' => $infoArray['userNumber'],
                'updatedType' => $infoArray['userGroup'],
            ]);
        }

        // move all update / insert stuff to this
        $user = tblAdminUsers::Get([
            tblAdminUsers_db::COLUMN_AID => $processorId,
        ]);
        $user->allowMERS = $infoArray['allowMERS'] ?? $user->allowMERS;
        $user->manualMERS = $infoArray['manualMERS'] ?? $user->manualMERS;
        $user->allowAllBackofficeUsersForFromEmail = intval($infoArray['allowAllBackofficeUsersForFromEmail'] ?? $user->allowAllBackofficeUsersForFromEmail);
        $user->customEmailForEmail = $infoArray['customEmailForEmail'] ?? $user->customEmailForEmail;
        $user->allowBranchEmailAsFromEmail = intval($infoArray['allowBranchEmailAsFromEmail'] ?? $user->allowBranchEmailAsFromEmail);
        $user->allowBrokerEmailAsFromEmail = intval($infoArray['allowBrokerEmailAsFromEmail'] ?? $user->allowBrokerEmailAsFromEmail);
        $user->allowLoanofficerEmailAsFromEmail = intval($infoArray['allowLoanofficerEmailAsFromEmail'] ?? $user->allowLoanofficerEmailAsFromEmail);
        $user->Save();

        $tblFromEmailUsers = tblFromEmailUsers::GetAll([
            'AID' => $processorId
        ]);
        foreach ($tblFromEmailUsers as $eachFromEmail){
            $eachFromEmail->Delete();
        }
        if(!$user->allowAllBackofficeUsersForFromEmail
            && !is_null($infoArray['backOfficersForFromEmail'])
            && sizeof($infoArray['backOfficersForFromEmail'])) {
            foreach ($infoArray['backOfficersForFromEmail'] as $backOfficeUserId) {
                $tblFromEmailUsers = new tblFromEmailUsers();
                $tblFromEmailUsers->AID = $processorId;
                $tblFromEmailUsers->userId = $backOfficeUserId;
                $tblFromEmailUsers->save();
            }
        }

        if (Request::isset('thirdPartyServices')) {
            saveThirdPartyServicesUserDetails::getReport($processorId, 'Employee', $_REQUEST['thirdPartyServicesData']);
        }

        return [
            'updateCount' => $updateCount,
            'insertCount' => $insertCount,
            'newProcessorId' => $newProcessorId,
        ];
    }
}
