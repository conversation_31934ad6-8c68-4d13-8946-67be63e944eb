<?php
namespace models\composite\oDrawManagement;

use models\composite\oDrawManagement\SowLineItem;
use models\lendingwise\tblDrawTemplateLineItems;
use models\lendingwise\db\tblDrawTemplateLineItems_db;
use models\lendingwise\tblDrawTemplateCategories;

class SowCategory extends tblDrawTemplateCategories
{
    /**
     * @var SowLineItem[] An array of line items associated with this category, indexed by line item ID.
     */
    private ?array $lineItems = [];

    /**
     * Get line items for this category.
     * @return SowLineItem[] Array of line items.
     */
    public function getLineItems(): array {
        return $this->lineItems ?? [];
    }

    /**
     * SowCategory constructor.
     * @param tblDrawTemplateCategories|null $category The database category object to initialize from.
     */
    public function __construct(?tblDrawTemplateCategories $category = null) {
        // Set defaults
        $this->order = 1;
        $this->description = '';
        $this->lineItems = [];

        if ($category !== null) {
            foreach (get_object_vars($category) as $property => $value) {
                if (property_exists($this, $property)) {
                    $this->$property = $value;
                }
            }
        }
        $this->loadLineItems();
    }

    /**
     * Loads line items associated with this category from the database.
     * @return void
     */
    private function loadLineItems(): void {
        $lineItemsData = tblDrawTemplateLineItems::GetAll(
            [tblDrawTemplateLineItems_db::COLUMN_CATEGORYID => $this->id],
            [tblDrawTemplateLineItems_db::COLUMN_CATEGORYID => 'ASC', tblDrawTemplateLineItems_db::COLUMN_ORDER => 'ASC']
        );

        foreach ($lineItemsData as $lineItemData) {
            $this->addLineItem(new SowLineItem($lineItemData));
        }
        $this->sortLineItems();
    }

        /**
     * Save the category to the database.
     * @param array $categoryData Optional data to set before saving.
     * @return array Result from parent Save method.
     */
    public function Save(array $categoryData = []): array {
        if (!empty($categoryData)) {
            $this->setFromArray($categoryData);
        }
        return parent::Save();
    }

    /**
     * Sets the properties of the category from an associative array.
     * @param array $categoryData Associative array containing category data.
     * @return void
     */
    private function setFromArray(array $categoryData): void {
        $this->id = $categoryData['id'] ?? null;
        $this->templateId = $categoryData['templateId'] ?? null;
        $this->categoryName = $categoryData['categoryName'] ?? '';
        $this->description = $categoryData['description'] ?? '';
        $this->order = $categoryData['order'] ?? 1;
    }

    /**
     * Delete the category from DB
     * @return ?array The result of the delete operation.
     */
    public function Delete(): ?array {
        return parent::Delete();
    }

    /**
     * Adds a SowLineItem object to the category's line items.
     * @param SowLineItem $lineItem The line item object to add.
     * @return void
     */
    private function addLineItem(SowLineItem $lineItem): void {
        $this->lineItems[$lineItem->id] = $lineItem;
    }

    /**
     * Sorts the line items by their order property.
     * @return void
     */
    private function sortLineItems(): void {
        uasort($this->lineItems, function($a, $b) {
            return $a->order <=> $b->order;
        });
    }

    /**
     * Retrieves all line items associated with this category.
     * @return SowLineItem[] An array of SowLineItem objects.
     */
    public function getAllLineItems(): array {
        return $this->getLineItems();
    }

    /**
     * Retrieves a specific line item by its ID.
     * @param int $lineItemId The ID of the line item to retrieve.
     * @return SowLineItem|null The SowLineItem object if found, otherwise null.
     */
    public function getLineItemById($lineItemId): ?SowLineItem {
        return $this->lineItems[$lineItemId] ?? null;
    }

    /**
     * Converts the category object and its line items to an associative array.
     * @return array An associative array representation of the category including line items.
     */
    public function toArray(): array {
        $data = parent::toArray();

        $data["lineItems"] = [];
        $lineItems = $this->getLineItems();
        if (is_array($lineItems)) {
            foreach ($lineItems as $lineItem) {
                $data["lineItems"][] = $lineItem->toArray();
            }
        }

        return $data;
    }
}
