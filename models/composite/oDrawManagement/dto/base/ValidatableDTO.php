<?php

namespace models\composite\oDrawManagement\dto\base;

/**
 * Validatable Data Transfer Object
 *
 * Extends BaseDTO with validation capabilities including:
 * - Property validation rules
 * - Type validation
 * - Range validation
 * - Custom business rule validation
 */
abstract class ValidatableDTO extends BaseDTO
{
    /**
     * @var array Validation errors
     */
    private array $validationErrors = [];

    /**
     * Define validation rules for properties
     * Override this method in child classes to define specific validation rules
     *
     * @return array Array of validation rules
     */
    protected function getValidationRules(): array
    {
        return [];
    }

    /**
     * Validate the DTO
     *
     * @return bool True if valid, false otherwise
     */
    public function validate(): bool
    {
        $this->validationErrors = [];
        $rules = $this->getValidationRules();

        foreach ($rules as $property => $propertyRules) {
            $value = $this->$property ?? null;
            $this->validateProperty($property, $value, $propertyRules);
        }

        return empty($this->validationErrors);
    }

    /**
     * Validate a single property
     *
     * @param string $property Property name
     * @param mixed $value Property value
     * @param array $rules Validation rules
     * @return void
     */
    protected function validateProperty(string $property, $value, array $rules): void
    {
        foreach ($rules as $rule => $ruleValue) {
            switch ($rule) {
                case 'required':
                    if ($ruleValue && ($value === null || $value === '')) {
                        $this->addValidationError($property, "Field {$property} is required");
                    }
                    break;

                case 'type':
                    if ($value !== null && !$this->validateType($value, $ruleValue)) {
                        $this->addValidationError($property, "Field {$property} must be of type {$ruleValue}");
                    }
                    break;

                case 'min':
                    if (is_numeric($value) && $value < $ruleValue) {
                        $this->addValidationError($property, "Field {$property} must be at least {$ruleValue}");
                    }
                    break;

                case 'max':
                    if (is_numeric($value) && $value > $ruleValue) {
                        $this->addValidationError($property, "Field {$property} must not exceed {$ruleValue}");
                    }
                    break;

                case 'minLength':
                    if (is_string($value) && strlen($value) < $ruleValue) {
                        $this->addValidationError($property, "Field {$property} must be at least {$ruleValue} characters long");
                    }
                    break;

                case 'maxLength':
                    if (is_string($value) && strlen($value) > $ruleValue) {
                        $this->addValidationError($property, "Field {$property} must not exceed {$ruleValue} characters");
                    }
                    break;

                case 'in':
                    if ($value !== null && !in_array($value, $ruleValue, true)) {
                        $allowedValues = implode(', ', $ruleValue);
                        $this->addValidationError($property, "Field {$property} must be one of: {$allowedValues}");
                    }
                    break;

                case 'regex':
                    if (is_string($value) && !preg_match($ruleValue, $value)) {
                        $this->addValidationError($property, "Field {$property} format is invalid");
                    }
                    break;

                case 'custom':
                    if (is_callable($ruleValue)) {
                        $result = $ruleValue($value, $this);
                        if ($result !== true) {
                            $this->addValidationError($property, $result ?: "Field {$property} is invalid");
                        }
                    }
                    break;
            }
        }
    }

    /**
     * Validate value type
     *
     * @param mixed $value Value to validate
     * @param string $expectedType Expected type
     * @return bool
     */
    protected function validateType($value, string $expectedType): bool
    {
        switch ($expectedType) {
            case 'int':
            case 'integer':
                return is_int($value);
            case 'float':
            case 'double':
                return is_float($value) || is_int($value);
            case 'string':
                return is_string($value);
            case 'bool':
            case 'boolean':
                return is_bool($value);
            case 'array':
                return is_array($value);
            case 'object':
                return is_object($value);
            case 'numeric':
                return is_numeric($value);
            default:
                return true;
        }
    }

    /**
     * Add validation error
     *
     * @param string $property Property name
     * @param string $message Error message
     * @return void
     */
    protected function addValidationError(string $property, string $message): void
    {
        if (!isset($this->validationErrors[$property])) {
            $this->validationErrors[$property] = [];
        }
        $this->validationErrors[$property][] = $message;
    }

    /**
     * Get validation errors
     *
     * @return array
     */
    public function getValidationErrors(): array
    {
        return $this->validationErrors;
    }

    /**
     * Get validation errors for a specific property
     *
     * @param string $property Property name
     * @return array
     */
    public function getPropertyErrors(string $property): array
    {
        return $this->validationErrors[$property] ?? [];
    }

    /**
     * Check if DTO is valid
     *
     * @return bool
     */
    public function isValid(): bool
    {
        return empty($this->validationErrors);
    }

    /**
     * Get first validation error message
     *
     * @return string|null
     */
    public function getFirstError(): ?string
    {
        foreach ($this->validationErrors as $propertyErrors) {
            if (!empty($propertyErrors)) {
                return $propertyErrors[0];
            }
        }
        return null;
    }

    /**
     * Get all validation error messages as flat array
     *
     * @return array
     */
    public function getAllErrorMessages(): array
    {
        $messages = [];
        foreach ($this->validationErrors as $propertyErrors) {
            $messages = array_merge($messages, $propertyErrors);
        }
        return $messages;
    }

    /**
     * Override fromArray to include validation
     *
     * @param array $data Input data
     * @param bool $validateOnCreate Whether to validate during creation
     * @return static
     * @throws \InvalidArgumentException If validation fails and validateOnCreate is true
     */
    public static function fromArray(array $data, bool $validateOnCreate = false)
    {
        $instance = parent::fromArray($data);

        if ($validateOnCreate && !$instance->validate()) {
            $errors = implode(', ', $instance->getAllErrorMessages());
            throw new \InvalidArgumentException("Validation failed: {$errors}");
        }

        return $instance;
    }
}
