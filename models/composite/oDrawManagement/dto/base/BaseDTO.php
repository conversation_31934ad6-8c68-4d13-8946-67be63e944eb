<?php

namespace models\composite\oDrawManagement\dto\base;

use ReflectionClass;
use ReflectionProperty;

/**
 * Base Data Transfer Object
 *
 * Provides common functionality for all DTOs including:
 * - Automatic property mapping from arrays
 * - Serialization to arrays/JSON
 * - Type conversion and validation
 */
abstract class BaseDTO
{
    /**
     * Create DTO instance from array data
     *
     * @param array $data Input data
     * @return static Instance of the DTO
     */
    public static function fromArray(array $data)
    {
        $instance = new static();
        $instance->populateFromArray($data);
        return $instance;
    }

    /**
     * Populate DTO properties from array data
     *
     * @param array $data Input data
     * @return void
     */
    protected function populateFromArray(array $data): void
    {
        $reflection = new ReflectionClass($this);
        $properties = $reflection->getProperties(ReflectionProperty::IS_PUBLIC);

        foreach ($properties as $property) {
            $propertyName = $property->getName();

            if (array_key_exists($propertyName, $data)) {
                $value = $data[$propertyName];
                $convertedValue = $this->convertValue($value, $property);
                $this->setProperty($propertyName, $convertedValue);
            }
        }
    }

    /**
     * Convert value to appropriate type based on property type hint
     *
     * @param mixed $value Input value
     * @param ReflectionProperty $property Property reflection
     * @return mixed Converted value
     */
    protected function convertValue($value, ReflectionProperty $property)
    {
        $type = $property->getType();
        if (!$type) {
            return $value;
        }

        // If value is null, check if the property allows null
        if ($value === null) {
            return $type->allowsNull() ? null : $this->getDefaultValueForType($type->getName());
        }

        $typeName = $type->getName();

        switch ($typeName) {
            case 'int':
                return (int) $value;
            case 'float':
                return (float) $value;
            case 'string':
                return (string) $value;
            case 'bool':
                return (bool) $value;
            case 'array':
                return is_array($value) ? $value : [$value];
            default:
                return $value;
        }
    }

    /**
     * Get default value for a type when null is not allowed
     *
     * @param string $typeName Type name
     * @return mixed Default value
     */
    private function getDefaultValueForType(string $typeName)
    {
        switch ($typeName) {
            case 'int':
                return 0;
            case 'float':
                return 0.0;
            case 'string':
                return '';
            case 'bool':
                return false;
            case 'array':
                return [];
            default:
                return null;
        }
    }

    /**
     * Set property value
     *
     * @param string $propertyName Property name
     * @param mixed $value Property value
     * @return void
     */
    protected function setProperty(string $propertyName, $value): void
    {
        $this->$propertyName = $value;
    }

    /**
     * Convert DTO to array
     *
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public function toArray(bool $includeNulls = true): array
    {
        $result = [];
        $reflection = new ReflectionClass($this);
        $properties = $reflection->getProperties(ReflectionProperty::IS_PUBLIC);

        foreach ($properties as $property) {
            $propertyName = $property->getName();
            $value = $this->$propertyName;

            if (!$includeNulls && $value === null) {
                continue;
            }

            if (is_object($value) && method_exists($value, 'toArray')) {
                $value = $value->toArray($includeNulls);
            } elseif (is_array($value)) {
                $value = $this->convertArrayToArray($value, $includeNulls);
            }

            $result[$propertyName] = $value;
        }

        return $result;
    }

    /**
     * Convert array of objects to array of arrays
     *
     * @param array $array Input array
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    protected function convertArrayToArray(array $array, bool $includeNulls): array
    {
        $result = [];

        foreach ($array as $key => $item) {
            if (is_object($item) && method_exists($item, 'toArray')) {
                $result[$key] = $item->toArray($includeNulls);
            } else {
                $result[$key] = $item;
            }
        }

        return $result;
    }

    /**
     * Convert DTO to JSON
     *
     * @param bool $includeNulls Whether to include null values
     * @return string
     */
    public function toJson(bool $includeNulls = true): string
    {
        return json_encode($this->toArray($includeNulls), JSON_THROW_ON_ERROR);
    }
}
