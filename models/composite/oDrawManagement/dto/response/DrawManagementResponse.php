<?php

namespace models\composite\oDrawManagement\dto\response;

use models\composite\oDrawManagement\dto\base\BaseDTO;
use models\composite\oDrawManagement\dto\shared\DrawRequestData;
use models\composite\oDrawManagement\dto\shared\CategoryData;

/**
 * Draw Management Response DTO
 *
 * Standardized response structure for draw management operations
 */
class DrawManagementResponse extends BaseDTO
{
    public ?DrawRequestData $drawRequest = null;
    public array $categories = [];
    public array $permissions = [];
    public array $summary = [];
    public ?string $status = null;
    public ?int $totalCategories = null;
    public ?int $totalLineItems = null;
    public ?float $totalCost = null;
    public ?float $totalCompletedAmount = null;
    public ?float $totalRequestedAmount = null;

    /**
     * Create response from DrawRequestData
     *
     * @param DrawRequestData $drawRequest Draw request data
     * @param array $permissions User permissions
     * @param array $summary Summary data
     * @return static
     */
    public static function fromDrawRequest(
        DrawRequestData $drawRequest,
        array $permissions = [],
        array $summary = []
    ) {
        $response = new static();
        $response->drawRequest = $drawRequest;
        $response->categories = $drawRequest->categories;
        $response->permissions = $permissions;
        $response->summary = $summary;
        $response->status = $drawRequest->status;

        $response->calculateTotals();

        return $response;
    }

    /**
     * Create response from categories array
     *
     * @param array $categories Array of CategoryData objects
     * @param array $permissions User permissions
     * @param array $summary Summary data
     * @param string|null $status Status
     * @return static
     */
    public static function fromCategories(
        array $categories,
        array $permissions = [],
        array $summary = [],
        ?string $status = null
    ) {
        $response = new static();
        $response->categories = $categories;
        $response->permissions = $permissions;
        $response->summary = $summary;
        $response->status = $status;

        $response->calculateTotals();

        return $response;
    }

    /**
     * Calculate totals from categories
     *
     * @return void
     */
    public function calculateTotals(): void
    {
        $this->totalCategories = count($this->categories);
        $this->totalLineItems = 0;
        $this->totalCost = 0.0;
        $this->totalCompletedAmount = 0.0;
        $this->totalRequestedAmount = 0.0;

        foreach ($this->categories as $category) {
            if ($category instanceof CategoryData) {
                $this->totalLineItems += count($category->lineItems);
                $this->totalCost += $category->getTotalCost();
                $this->totalCompletedAmount += $category->getTotalCompletedAmount();
                $this->totalRequestedAmount += $category->getTotalRequestedAmount();
            }
        }
    }

    /**
     * Override populateFromArray to handle complex objects
     *
     * @param array $data Input data
     * @return void
     */
    protected function populateFromArray(array $data): void
    {
        // Extract complex objects before calling parent
        $drawRequestData = null;
        $categoriesData = null;

        if (isset($data['drawRequest'])) {
            $drawRequestData = $data['drawRequest'];
            unset($data['drawRequest']); // Remove to prevent parent from processing
        }

        if (isset($data['categories'])) {
            $categoriesData = $data['categories'];
            unset($data['categories']); // Remove to prevent parent from processing
        }

        // Call parent with filtered data
        parent::populateFromArray($data);

        // Handle drawRequest
        if ($drawRequestData !== null && is_array($drawRequestData)) {
            $this->drawRequest = DrawRequestData::fromArray($drawRequestData);
        }

        // Handle categories array
        if ($categoriesData !== null && is_array($categoriesData)) {
            $this->categories = [];
            foreach ($categoriesData as $categoryData) {
                if (is_array($categoryData)) {
                    $this->categories[] = CategoryData::fromArray($categoryData);
                } elseif ($categoryData instanceof CategoryData) {
                    $this->categories[] = $categoryData;
                }
            }
        }
    }

    /**
     * Override toArray to handle complex objects
     *
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public function toArray(bool $includeNulls = true): array
    {
        $result = parent::toArray($includeNulls);

        // Handle drawRequest
        if ($this->drawRequest) {
            $result['drawRequest'] = $this->drawRequest->toArray($includeNulls);
        }

        // Convert categories to arrays
        $result['categories'] = [];
        foreach ($this->categories as $category) {
            if ($category instanceof CategoryData) {
                $result['categories'][] = $category->toArray($includeNulls);
            } else {
                $result['categories'][] = $category;
            }
        }

        return $result;
    }

    /**
     * Add permission to response
     *
     * @param string $permission Permission name
     * @param bool $value Permission value
     * @return void
     */
    public function addPermission(string $permission, bool $value): void
    {
        $this->permissions[$permission] = $value;
    }

    /**
     * Add multiple permissions to response
     *
     * @param array $permissions Permissions array
     * @return void
     */
    public function addPermissions(array $permissions): void
    {
        $this->permissions = array_merge($this->permissions, $permissions);
    }

    /**
     * Add summary data
     *
     * @param string $key Summary key
     * @param mixed $value Summary value
     * @return void
     */
    public function addSummary(string $key, $value): void
    {
        $this->summary[$key] = $value;
    }

    /**
     * Add multiple summary data
     *
     * @param array $summary Summary data array
     * @return void
     */
    public function addSummaryData(array $summary): void
    {
        $this->summary = array_merge($this->summary, $summary);
    }

    /**
     * Get category by ID
     *
     * @param int $categoryId Category ID
     * @return CategoryData|null
     */
    public function getCategory(int $categoryId): ?CategoryData
    {
        foreach ($this->categories as $category) {
            if ($category instanceof CategoryData && $category->id === $categoryId) {
                return $category;
            }
        }
        return null;
    }

    /**
     * Check if user has permission
     *
     * @param string $permission Permission name
     * @return bool
     */
    public function hasPermission(string $permission): bool
    {
        return $this->permissions[$permission] ?? false;
    }

    /**
     * Get completion percentage
     *
     * @return float
     */
    public function getCompletionPercentage(): float
    {
        if ($this->totalCost <= 0) {
            return 0.0;
        }

        return ($this->totalCompletedAmount / $this->totalCost) * 100;
    }

    /**
     * Get request percentage
     *
     * @return float
     */
    public function getRequestPercentage(): float
    {
        if ($this->totalCost <= 0) {
            return 0.0;
        }

        return ($this->totalRequestedAmount / $this->totalCost) * 100;
    }
}
