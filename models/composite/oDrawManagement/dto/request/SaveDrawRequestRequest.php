<?php

namespace models\composite\oDrawManagement\dto\request;

use models\composite\oDrawManagement\dto\base\ValidatableDTO;
use models\composite\oDrawManagement\dto\shared\LineItemData;
use models\composite\oDrawManagement\DrawRequest;

/**
 * Save Draw Request DTO
 *
 * Represents the request structure for saving draw request data
 * including status updates and line item modifications
 */
class SaveDrawRequestRequest extends ValidatableDTO
{
    public ?int $lmrid = null;
    public string $status = DrawRequest::STATUS_NEW;
    public string $rejectionReason = '';
    public string $lenderNotes = '';

    /**
     * @var array Line items data grouped by line item ID
     */
    public array $lineItems = [];

    /**
     * Define validation rules
     *
     * @return array
     */
    protected function getValidationRules(): array
    {
        return [
            'lmrid' => [
                'required' => true,
                'type' => 'int',
                'min' => 1
            ],
            'status' => [
                'required' => true,
                'type' => 'string',
                'in' => [
                    DrawRequest::STATUS_NEW,
                    DrawRequest::STATUS_PENDING,
                    DrawRequest::STATUS_APPROVED,
                    DrawRequest::STATUS_REJECTED
                ]
            ],
            'lenderNotes' => [
                'type' => 'string',
                'maxLength' => 2000
            ],
            'lineItems' => [
                'required' => true,
                'type' => 'array',
                'custom' => function ($value, $dto) {
                    if (empty($value)) {
                        return 'At least one line item is required';
                    }

                    // Validate each line item
                    foreach ($value as $lineItemId => $lineItemData) {
                        if (!is_array($lineItemData)) {
                            return "Line item {$lineItemId} must be an array";
                        }

                        // Validate required fields for line items
                        if (!isset($lineItemData['id'])) {
                            return "Line item {$lineItemId} must have an id";
                        }

                        // Validate requested amount if present
                        if (isset($lineItemData['requestedAmount'])) {
                            $amount = $lineItemData['requestedAmount'];
                            if (!is_numeric($amount) || $amount < 0) {
                                return "Line item {$lineItemId} requested amount must be a non-negative number";
                            }
                        }

                        // Validate notes length
                        if (isset($lineItemData['notes']) && strlen($lineItemData['notes']) > 2000) {
                            return "Line item {$lineItemId} notes must not exceed 2000 characters";
                        }

                        if (isset($lineItemData['lenderNotes']) && strlen($lineItemData['lenderNotes']) > 2000) {
                            return "Line item {$lineItemId} lender notes must not exceed 2000 characters";
                        }
                    }

                    return true;
                }
            ]
        ];
    }

    /**
     * Override populateFromArray to handle line items
     *
     * @param array $data Input data
     * @return void
     */
    protected function populateFromArray(array $data): void
    {
        parent::populateFromArray($data);

        // Handle line items array
        if (isset($data['lineItems']) && is_array($data['lineItems'])) {
            $this->lineItems = [];
            foreach ($data['lineItems'] as $lineItemId => $lineItemData) {
                if (is_array($lineItemData)) {
                    // Sanitize and validate line item data
                    $this->lineItems[$lineItemId] = [
                        'id' => $lineItemData['id'] ?? $lineItemId,
                        'notes' => $lineItemData['notes'] ?? '',
                        'lenderNotes' => $lineItemData['lenderNotes'] ?? '',
                        'rejectReason' => $lineItemData['rejectReason'] ?? '',
                        'requestedAmount' => isset($lineItemData['requestedAmount']) ?
                            (float)$lineItemData['requestedAmount'] : null
                    ];
                }
            }
        }
    }

    /**
     * Override toArray to handle line items
     *
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public function toArray(bool $includeNulls = true): array
    {
        $result = parent::toArray($includeNulls);

        // Include line items
        $result['lineItems'] = $this->lineItems;

        return $result;
    }

    /**
     * Get line item by ID
     *
     * @param int $lineItemId Line item ID
     * @return array|null
     */
    public function getLineItem(int $lineItemId): ?array
    {
        return $this->lineItems[$lineItemId] ?? null;
    }

    /**
     * Check if request has any line items with requested amounts
     *
     * @return bool
     */
    public function hasRequestedAmounts(): bool
    {
        foreach ($this->lineItems as $lineItem) {
            if (isset($lineItem['requestedAmount']) && $lineItem['requestedAmount'] > 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * Get total requested amount across all line items
     *
     * @return float
     */
    public function getTotalRequestedAmount(): float
    {
        $total = 0.0;
        foreach ($this->lineItems as $lineItem) {
            if (isset($lineItem['requestedAmount'])) {
                $total += (float)$lineItem['requestedAmount'];
            }
        }
        return $total;
    }

    /**
     * Check if this is a status update request
     *
     * @return bool
     */
    public function isStatusUpdate(): bool
    {
        return $this->status !== DrawRequest::STATUS_NEW;
    }

    /**
     * Check if this is an approval request
     *
     * @return bool
     */
    public function isApproval(): bool
    {
        return $this->status === DrawRequest::STATUS_APPROVED;
    }

    /**
     * Check if this is a rejection request
     *
     * @return bool
     */
    public function isRejection(): bool
    {
        return $this->status === DrawRequest::STATUS_REJECTED;
    }

    /**
     * Check if this is a submission request (borrower)
     *
     * @return bool
     */
    public function isSubmission(): bool
    {
        return $this->status === DrawRequest::STATUS_PENDING;
    }

    /**
     * Validate business rules for borrower submissions
     *
     * @return array Array of validation errors
     */
    public function validateBorrowerSubmission(): array
    {
        $errors = [];

        if ($this->isSubmission() && !$this->hasRequestedAmounts()) {
            $errors[] = 'At least one line item must have a requested amount for submission';
        }

        return $errors;
    }

    /**
     * Validate business rules for lender actions
     *
     * @return array Array of validation errors
     */
    public function validateLenderAction(): array
    {
        $errors = [];

        if ($this->isRejection() && empty(trim($this->rejectionReason))) {
            $errors[] = 'Rejection reason is required when rejecting a draw request';
        }

        return $errors;
    }

    /**
     * Get line items that have requested amounts
     *
     * @return array
     */
    public function getLineItemsWithRequests(): array
    {
        return array_filter($this->lineItems, function ($lineItem) {
            return isset($lineItem['requestedAmount']) && $lineItem['requestedAmount'] > 0;
        });
    }

    /**
     * Sanitize line item data
     *
     * @return void
     */
    public function sanitizeLineItems(): void
    {
        foreach ($this->lineItems as $lineItemId => &$lineItem) {
            // Sanitize strings
            if (isset($lineItem['notes'])) {
                $lineItem['notes'] = trim(strip_tags($lineItem['notes']));
            }
            if (isset($lineItem['lenderNotes'])) {
                $lineItem['lenderNotes'] = trim(strip_tags($lineItem['lenderNotes']));
            }
            if (isset($lineItem['rejectReason'])) {
                $lineItem['rejectReason'] = trim(strip_tags($lineItem['rejectReason']));
            }

            // Ensure numeric values
            if (isset($lineItem['requestedAmount'])) {
                $lineItem['requestedAmount'] = max(0, (float)$lineItem['requestedAmount']);
            }
        }

        // Sanitize other fields
        $this->rejectionReason = trim(strip_tags($this->rejectionReason));
        $this->lenderNotes = trim(strip_tags($this->lenderNotes));
    }
}
