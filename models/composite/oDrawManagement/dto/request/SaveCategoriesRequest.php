<?php

namespace models\composite\oDrawManagement\dto\request;

use models\composite\oDrawManagement\dto\base\ValidatableDTO;
use models\composite\oDrawManagement\dto\shared\CategoryData;

/**
 * Save Categories Request DTO
 * 
 * Represents the request structure for saving categories
 */
class SaveCategoriesRequest extends ValidatableDTO
{
    public ?int $pcid = null;
    public ?int $lmrid = null;
    
    /**
     * @var CategoryData[] Array of categories to save
     */
    public array $categories = [];

    /**
     * Define validation rules
     * 
     * @return array
     */
    protected function getValidationRules(): array
    {
        return [
            'pcid' => [
                'type' => 'int',
                'custom' => function ($value, $dto) {
                    if (!$value && !$dto->lmrid) {
                        return 'Either pcid or lmrid must be provided';
                    }
                    return true;
                }
            ],
            'lmrid' => [
                'type' => 'int',
                'custom' => function ($value, $dto) {
                    if (!$value && !$dto->pcid) {
                        return 'Either pcid or lmrid must be provided';
                    }
                    return true;
                }
            ],
            'categories' => [
                'required' => true,
                'type' => 'array',
                'custom' => function ($value, $dto) {
                    if (empty($value)) {
                        return 'At least one category is required';
                    }
                    
                    // Validate each category
                    foreach ($value as $index => $categoryData) {
                        if (!($categoryData instanceof CategoryData)) {
                            return "Category at index {$index} must be a CategoryData instance";
                        }
                        
                        if (!$categoryData->validate()) {
                            $errors = implode(', ', $categoryData->getAllErrorMessages());
                            return "Category at index {$index} validation failed: {$errors}";
                        }
                    }
                    
                    return true;
                }
            ]
        ];
    }

    /**
     * Override populateFromArray to handle categories
     * 
     * @param array $data Input data
     * @return void
     */
    protected function populateFromArray(array $data): void
    {
        parent::populateFromArray($data);
        
        // Handle categories array
        if (isset($data['categories']) && is_array($data['categories'])) {
            $this->categories = [];
            foreach ($data['categories'] as $categoryData) {
                if (is_array($categoryData)) {
                    $this->categories[] = CategoryData::fromArray($categoryData);
                } elseif ($categoryData instanceof CategoryData) {
                    $this->categories[] = $categoryData;
                }
            }
        }
    }

    /**
     * Override toArray to handle categories
     * 
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public function toArray(bool $includeNulls = true): array
    {
        $result = parent::toArray($includeNulls);
        
        // Convert categories to arrays
        $result['categories'] = [];
        foreach ($this->categories as $category) {
            $result['categories'][] = $category->toArray($includeNulls);
        }
        
        return $result;
    }

    /**
     * Get the identifier (pcid or lmrid)
     * 
     * @return int|null
     */
    public function getIdentifier(): ?int
    {
        return $this->pcid ?? $this->lmrid;
    }

    /**
     * Get the identifier type
     * 
     * @return string|null 'pcid' or 'lmrid'
     */
    public function getIdentifierType(): ?string
    {
        if ($this->pcid) {
            return 'pcid';
        }
        if ($this->lmrid) {
            return 'lmrid';
        }
        return null;
    }

    /**
     * Check if this is a lender request (has pcid)
     * 
     * @return bool
     */
    public function isLenderRequest(): bool
    {
        return $this->pcid !== null;
    }

    /**
     * Check if this is a borrower request (has lmrid)
     * 
     * @return bool
     */
    public function isBorrowerRequest(): bool
    {
        return $this->lmrid !== null;
    }

    /**
     * Validate category order uniqueness
     * 
     * @return array Array of validation errors
     */
    public function validateCategoryOrder(): array
    {
        $errors = [];
        $orders = [];
        
        foreach ($this->categories as $index => $category) {
            $order = $category->order;
            if (in_array($order, $orders)) {
                $errors[] = "Duplicate order value {$order} found in categories";
            }
            $orders[] = $order;
        }
        
        return $errors;
    }

    /**
     * Sort categories by order
     * 
     * @return void
     */
    public function sortCategories(): void
    {
        usort($this->categories, function (CategoryData $a, CategoryData $b) {
            return ($a->order ?? 0) <=> ($b->order ?? 0);
        });
    }

    /**
     * Reorder categories to ensure sequential order starting from 1
     * 
     * @return void
     */
    public function reorderCategories(): void
    {
        $this->sortCategories();
        
        foreach ($this->categories as $index => $category) {
            $category->order = $index + 1;
        }
    }

    /**
     * Get categories that are new (no ID)
     * 
     * @return CategoryData[]
     */
    public function getNewCategories(): array
    {
        return array_filter($this->categories, function (CategoryData $category) {
            return $category->id === null;
        });
    }

    /**
     * Get categories that are existing (have ID)
     * 
     * @return CategoryData[]
     */
    public function getExistingCategories(): array
    {
        return array_filter($this->categories, function (CategoryData $category) {
            return $category->id !== null;
        });
    }
}
