<?php

namespace models\composite\oDrawManagement\dto\shared;

use models\composite\oDrawManagement\dto\base\ValidatableDTO;

/**
 * Line Item Data DTO
 * 
 * Represents line item data structure used across the draw management system
 */
class LineItemData extends ValidatableDTO
{
    public ?int $id = null;
    public ?int $drawId = null;
    public ?int $categoryId = null;
    public ?int $templateCategoryId = null;
    public ?int $templateLineItemId = null;
    public string $name = '';
    public string $description = '';
    public int $order = 1;
    public float $cost = 0.0;
    public float $completedAmount = 0.0;
    public float $completedPercent = 0.0;
    public float $requestedAmount = 0.0;
    public float $requestedPercent = 0.0;
    public float $disbursedAmount = 0.0;
    public string $notes = '';
    public string $lenderNotes = '';
    public ?string $createdAt = null;
    public ?string $updatedAt = null;

    /**
     * Define validation rules
     * 
     * @return array
     */
    protected function getValidationRules(): array
    {
        return [
            'id' => [
                'type' => 'int'
            ],
            'drawId' => [
                'type' => 'int'
            ],
            'categoryId' => [
                'type' => 'int'
            ],
            'templateCategoryId' => [
                'type' => 'int'
            ],
            'templateLineItemId' => [
                'type' => 'int'
            ],
            'name' => [
                'required' => true,
                'type' => 'string',
                'minLength' => 1,
                'maxLength' => 255
            ],
            'description' => [
                'type' => 'string',
                'maxLength' => 1000
            ],
            'order' => [
                'required' => true,
                'type' => 'int',
                'min' => 1
            ],
            'cost' => [
                'required' => true,
                'type' => 'numeric',
                'min' => 0
            ],
            'completedAmount' => [
                'type' => 'numeric',
                'min' => 0,
                'custom' => function ($value, $dto) {
                    if ($value > $dto->cost) {
                        return 'Completed amount cannot exceed total cost';
                    }
                    return true;
                }
            ],
            'completedPercent' => [
                'type' => 'numeric',
                'min' => 0,
                'max' => 100
            ],
            'requestedAmount' => [
                'type' => 'numeric',
                'min' => 0,
                'custom' => function ($value, $dto) {
                    $maxRequestable = $dto->cost - $dto->completedAmount;
                    if ($value > $maxRequestable) {
                        return 'Requested amount cannot exceed remaining cost';
                    }
                    return true;
                }
            ],
            'requestedPercent' => [
                'type' => 'numeric',
                'min' => 0,
                'custom' => function ($value, $dto) {
                    $maxRequestablePercent = 100 - $dto->completedPercent;
                    if ($value > $maxRequestablePercent) {
                        return 'Requested percent cannot exceed remaining percent';
                    }
                    return true;
                }
            ],
            'disbursedAmount' => [
                'type' => 'numeric',
                'min' => 0
            ],
            'notes' => [
                'type' => 'string',
                'maxLength' => 2000
            ],
            'lenderNotes' => [
                'type' => 'string',
                'maxLength' => 2000
            ]
        ];
    }

    /**
     * Calculate requested amount from requested percent
     * 
     * @return void
     */
    public function calculateRequestedAmountFromPercent(): void
    {
        if ($this->cost > 0 && $this->requestedPercent >= 0) {
            $this->requestedAmount = ($this->requestedPercent / 100) * $this->cost;
        }
    }

    /**
     * Calculate requested percent from requested amount
     * 
     * @return void
     */
    public function calculateRequestedPercentFromAmount(): void
    {
        if ($this->cost > 0 && $this->requestedAmount >= 0) {
            $this->requestedPercent = ($this->requestedAmount / $this->cost) * 100;
        }
    }

    /**
     * Calculate completed percent from completed amount
     * 
     * @return void
     */
    public function calculateCompletedPercentFromAmount(): void
    {
        if ($this->cost > 0 && $this->completedAmount >= 0) {
            $this->completedPercent = ($this->completedAmount / $this->cost) * 100;
        }
    }

    /**
     * Calculate completed amount from completed percent
     * 
     * @return void
     */
    public function calculateCompletedAmountFromPercent(): void
    {
        if ($this->cost > 0 && $this->completedPercent >= 0) {
            $this->completedAmount = ($this->completedPercent / 100) * $this->cost;
        }
    }

    /**
     * Get remaining cost (cost - completed amount)
     * 
     * @return float
     */
    public function getRemainingCost(): float
    {
        return max(0, $this->cost - $this->completedAmount);
    }

    /**
     * Get remaining percent (100 - completed percent)
     * 
     * @return float
     */
    public function getRemainingPercent(): float
    {
        return max(0, 100 - $this->completedPercent);
    }

    /**
     * Check if line item is fully completed
     * 
     * @return bool
     */
    public function isFullyCompleted(): bool
    {
        return $this->completedPercent >= 100 || $this->completedAmount >= $this->cost;
    }

    /**
     * Check if line item has any requested amount
     * 
     * @return bool
     */
    public function hasRequestedAmount(): bool
    {
        return $this->requestedAmount > 0;
    }

    /**
     * Get maximum requestable amount
     * 
     * @return float
     */
    public function getMaxRequestableAmount(): float
    {
        return $this->getRemainingCost();
    }

    /**
     * Get maximum requestable percent
     * 
     * @return float
     */
    public function getMaxRequestablePercent(): float
    {
        return $this->getRemainingPercent();
    }

    /**
     * Validate requested amounts against business rules
     * 
     * @return array Array of validation errors
     */
    public function validateRequestedAmounts(): array
    {
        $errors = [];

        if ($this->requestedAmount > $this->getMaxRequestableAmount()) {
            $errors[] = 'Requested amount exceeds remaining cost';
        }

        if ($this->requestedPercent > $this->getMaxRequestablePercent()) {
            $errors[] = 'Requested percent exceeds remaining percent';
        }

        // Check consistency between amount and percent
        $calculatedAmount = ($this->requestedPercent / 100) * $this->cost;
        $tolerance = 0.01; // Allow small floating point differences
        
        if (abs($this->requestedAmount - $calculatedAmount) > $tolerance) {
            $errors[] = 'Requested amount and percent are inconsistent';
        }

        return $errors;
    }

    /**
     * Sync requested amount and percent to ensure consistency
     * 
     * @param string $basedOn Either 'amount' or 'percent' to determine which value to use as base
     * @return void
     */
    public function syncRequestedValues(string $basedOn = 'amount'): void
    {
        if ($basedOn === 'amount') {
            $this->calculateRequestedPercentFromAmount();
        } else {
            $this->calculateRequestedAmountFromPercent();
        }
    }
}
