<?php

namespace models\composite\oDrawManagement\dto\shared;

use models\composite\oDrawManagement\dto\base\ValidatableDTO;

/**
 * Category Data DTO
 * 
 * Represents category data structure used across the draw management system
 */
class CategoryData extends ValidatableDTO
{
    public ?int $id = null;
    public ?int $drawId = null;
    public ?int $templateId = null;
    public string $categoryName = '';
    public string $description = '';
    public int $order = 1;
    public ?string $createdAt = null;
    public ?string $updatedAt = null;
    
    /**
     * @var LineItemData[] Array of line items
     */
    public array $lineItems = [];

    /**
     * Define validation rules
     * 
     * @return array
     */
    protected function getValidationRules(): array
    {
        return [
            'id' => [
                'type' => 'int'
            ],
            'drawId' => [
                'type' => 'int'
            ],
            'templateId' => [
                'type' => 'int'
            ],
            'categoryName' => [
                'required' => true,
                'type' => 'string',
                'minLength' => 1,
                'maxLength' => 255
            ],
            'description' => [
                'type' => 'string',
                'maxLength' => 1000
            ],
            'order' => [
                'required' => true,
                'type' => 'int',
                'min' => 1
            ]
        ];
    }

    /**
     * Override populateFromArray to handle line items
     * 
     * @param array $data Input data
     * @return void
     */
    protected function populateFromArray(array $data): void
    {
        parent::populateFromArray($data);
        
        // Handle line items array
        if (isset($data['lineItems']) && is_array($data['lineItems'])) {
            $this->lineItems = [];
            foreach ($data['lineItems'] as $lineItemData) {
                if (is_array($lineItemData)) {
                    $this->lineItems[] = LineItemData::fromArray($lineItemData);
                } elseif ($lineItemData instanceof LineItemData) {
                    $this->lineItems[] = $lineItemData;
                }
            }
        }
    }

    /**
     * Override toArray to handle line items
     * 
     * @param bool $includeNulls Whether to include null values
     * @return array
     */
    public function toArray(bool $includeNulls = true): array
    {
        $result = parent::toArray($includeNulls);
        
        // Convert line items to arrays
        $result['lineItems'] = [];
        foreach ($this->lineItems as $lineItem) {
            $result['lineItems'][] = $lineItem->toArray($includeNulls);
        }
        
        return $result;
    }

    /**
     * Add line item to category
     * 
     * @param LineItemData $lineItem Line item to add
     * @return void
     */
    public function addLineItem(LineItemData $lineItem): void
    {
        $this->lineItems[] = $lineItem;
    }

    /**
     * Remove line item by ID
     * 
     * @param int $lineItemId Line item ID to remove
     * @return bool True if removed, false if not found
     */
    public function removeLineItem(int $lineItemId): bool
    {
        foreach ($this->lineItems as $index => $lineItem) {
            if ($lineItem->id === $lineItemId) {
                unset($this->lineItems[$index]);
                $this->lineItems = array_values($this->lineItems); // Re-index array
                return true;
            }
        }
        return false;
    }

    /**
     * Get line item by ID
     * 
     * @param int $lineItemId Line item ID
     * @return LineItemData|null
     */
    public function getLineItem(int $lineItemId): ?LineItemData
    {
        foreach ($this->lineItems as $lineItem) {
            if ($lineItem->id === $lineItemId) {
                return $lineItem;
            }
        }
        return null;
    }

    /**
     * Get total cost of all line items
     * 
     * @return float
     */
    public function getTotalCost(): float
    {
        $total = 0.0;
        foreach ($this->lineItems as $lineItem) {
            $total += $lineItem->cost ?? 0.0;
        }
        return $total;
    }

    /**
     * Get total completed amount of all line items
     * 
     * @return float
     */
    public function getTotalCompletedAmount(): float
    {
        $total = 0.0;
        foreach ($this->lineItems as $lineItem) {
            $total += $lineItem->completedAmount ?? 0.0;
        }
        return $total;
    }

    /**
     * Get total requested amount of all line items
     * 
     * @return float
     */
    public function getTotalRequestedAmount(): float
    {
        $total = 0.0;
        foreach ($this->lineItems as $lineItem) {
            $total += $lineItem->requestedAmount ?? 0.0;
        }
        return $total;
    }

    /**
     * Sort line items by order
     * 
     * @return void
     */
    public function sortLineItems(): void
    {
        usort($this->lineItems, function (LineItemData $a, LineItemData $b) {
            return ($a->order ?? 0) <=> ($b->order ?? 0);
        });
    }
}
