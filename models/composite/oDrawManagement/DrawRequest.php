<?php
namespace models\composite\oDrawManagement;

use models\lendingwise\tblDrawRequests;
use models\lendingwise\db\tblDrawRequestCategories_db;
use models\lendingwise\tblDrawRequestCategories;
use models\composite\oDrawManagement\BorrowerDrawCategory;
use models\composite\oDrawManagement\BorrowerDrawLineItem;

class DrawRequest extends tblDrawRequests
{
    /**
     * Status constants
     */
    public const STATUS_NEW = 'new';
    public const STATUS_PENDING = 'pending';
    public const STATUS_APPROVED = 'approved';
    public const STATUS_REJECTED = 'rejected';

    private ?array $categories = null;

    /**
     * DrawRequest constructor.
     * @param tblDrawRequests|null $drawRequest The database draw request object to initialize from.
     */
    public function __construct(?tblDrawRequests $drawRequest = null) {
        $this->status = self::STATUS_PENDING;

        if ($drawRequest !== null) {
            foreach (get_object_vars($drawRequest) as $property => $value) {
                if (property_exists($this, $property)) {
                    $this->$property = $value;
                }
            }
        }
        if ($this->id) $this->loadCategories();
    }

    /**
     * Saves the current draw request object to the database.
     * @param array|null $drawRequestData Optional draw request data to set before saving
     * @return array The result of the save operation.
     */
    public function Save(?array $drawRequestData = null): array {
        if(!empty($drawRequestData)) $this->setFromArray($drawRequestData);
        return parent::Save();
    }

    public function getDbObject(): tblDrawRequests {
        return $this;
    }

    /**
     * Sets the properties of the draw request from an associative array.
     * @param array $drawRequestData Associative array containing draw request data.
     * @return void
     */
    private function setFromArray(array $drawRequestData): void {
        $this->id = $drawRequestData['id'] ?? null;
        $this->LMRId = $drawRequestData['LMRId'];
        $this->status = $drawRequestData['status'] ?? self::STATUS_NEW;
        $this->sowApproved = $drawRequestData['sowApproved'] ?? 0;
        $this->isDrawRequest = $drawRequestData['isDrawRequest'] ?? 0;
    }

    /**
     * Delete the draw request from DB
     * @return ?array The result of the delete operation.
     */
    public function Delete(): ?array {
        return parent::Delete();
    }

    /**
     * Converts the draw request object to an associative array.
     * @return array An associative array representation of the draw request.
     */
    public function toArray(): array {
        $data = parent::toArray();

        $categoriesData = [];
        foreach ($this->categories as $category) {
            $categoriesData[] = $category->toArray();
        }
        $data["categories"] = $categoriesData;

        return $data;
    }

    /**
     * Loads categories associated with this draw request from the database.
     * @return void
     */
    public function loadCategories(): void {
        if (!$this->id) {
            return;
        }

        $categoriesData = tblDrawRequestCategories::GetAll(
            [tblDrawRequestCategories_db::COLUMN_DRAWID => $this->id],
            [tblDrawRequestCategories_db::COLUMN_ORDER => 'ASC']
        );

        $this->categories = [];
        foreach ($categoriesData as $categoryData) {
            $this->addCategory(new BorrowerDrawCategory($categoryData));
        }
        $this->sortCategories();
    }

    /**
     * Adds a BorrowerDrawCategory object to the draw request's categories.
     * @param BorrowerDrawCategory $category The category object to add.
     * @return void
     */
    private function addCategory(BorrowerDrawCategory $category): void {
        $this->categories[$category->id] = $category;
    }

    /**
     * Sorts the categories by their order property.
     * @return void
     */
    private function sortCategories(): void {
        uasort($this->categories, function($a, $b) {
            return $a->order <=> $b->order;
        });
    }

    /**
     * Retrieves all categories associated with this draw request.
     * @return array An array of BorrowerDrawCategory objects.
     */
    public function getCategories(): array {
        if (is_null($this->categories)) {
            $this->loadCategories();
        }
        return $this->categories ?? [];
    }

    /**
     * Retrieves all line items associated with this draw request.
     * @return array An array of BorrowerDrawLineItem objects.
     */
    public function getAllLineItems(): array {
        $lineItems = [];
        foreach ($this->getCategories() as $category) {
            $lineItems = array_merge($lineItems, $category->getAllLineItems());
        }
        return $lineItems;
    }

    /**
     * Update the draw request status based on line item completion.
     * If all line items are completed, set the status to 'approved'.
     * Otherwise, set the status to 'pending'.
     * @return bool True on success, False on failure.
     */

    public function updateDrawRequestStatus(?string $status, ?int $sowApproved = null, ?int $isDrawRequest = null): bool
    {
        $requestAllLineItems = $this->getAllLineItems();

        $this->status = $status ?? self::STATUS_NEW;
        $this->sowApproved = $sowApproved ?? $this->sowApproved;
        $this->isDrawRequest = $isDrawRequest ?? $this->isDrawRequest;
        if (empty($requestAllLineItems) && $this->status !== self::STATUS_NEW) return false;

        $this->Save();
        return true;
    }

    /**
     * Retrieves a specific category by its ID.
     * @param int $categoryId The ID of the category to retrieve.
     * @return BorrowerDrawCategory|null The BorrowerDrawCategory object if found, otherwise null.
     */
    public function getCategoryById($categoryId): ?BorrowerDrawCategory {
        if (is_null($this->categories)) {
            $this->loadCategories();
        }
        return $this->categories[$categoryId] ?? null;
    }

    /**
     * Delete categories by their IDs.
     * @param array $categoryIds Array of category IDs to delete.
     * @return void
     */
    public function deleteCategories(array $categoryIds): void {
        foreach ($categoryIds as $categoryId) {
            $category = $this->getCategoryById($categoryId);
            if ($category) {
                $category->delete();
            }
        }
    }

    /**
     * Delete line items by their IDs.
     * @param array $lineItemIds Array of line item IDs to delete.
     * @return void
     */
    public function deleteLineItems(array $lineItemIds): void {
        foreach ($lineItemIds as $lineItemId) {
            $lineItem = new BorrowerDrawLineItem();
            $lineItem->getDbObject()->id = $lineItemId;
            $lineItem->delete();
        }
    }
}
