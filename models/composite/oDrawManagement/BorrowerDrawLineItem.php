<?php
namespace models\composite\oDrawManagement;

use models\lendingwise\tblDrawRequestLineItems;

class BorrowerDrawLineItem extends tblDrawRequestLineItems
{

    /**
     * BorrowerDrawLineItem constructor.
     * @param tblDrawRequestLineItems|null $lineItem The database line item object to initialize from.
     */
    public function __construct(?tblDrawRequestLineItems $lineItem = null) {
        if ($lineItem !== null) {
            foreach (get_object_vars($lineItem) as $property => $value) {
                if (property_exists($this, $property)) {
                    $this->$property = $value;
                }
            }
        }

        // Set default value for order
        if (empty($this->order)) {
            $this->order = 1;
        }
    }

    /**
     * Saves the current line item object to the database.
     * @param array $lineItemData Optional line item data to set before saving
     * @return array The result of the save operation.
     */
    public function Save(array $lineItemData = []): array {
        if (!empty($lineItemData)) {
            $this->setFromArray($lineItemData);
        }
        return parent::Save();
    }

    /**
     * Sets the properties of the line item from an associative array.
     * @param array $lineItemData Associative array containing line item data.
     * @return void
     */
    private function setFromArray(array $lineItemData): void {
        $this->id = $lineItemData['id'] ?? null;
        $this->drawId = $lineItemData['drawId'];
        $this->categoryId = $lineItemData['categoryId'];
        $this->name = $lineItemData['name'];
        $this->description = $lineItemData['description'] ?? $this->description;
        $this->order = $lineItemData['order'] ?? $this->order;
        $this->cost = $lineItemData['cost'] ?? $this->cost;
        $this->completedAmount = $lineItemData['completedAmount'] ?? $this->completedAmount;
        $this->completedPercent = $lineItemData['completedPercent'] ?? $this->completedPercent;
        $this->requestedAmount = $lineItemData['requestedAmount'] ?? $this->requestedAmount;
        $this->disbursedAmount = $lineItemData['disbursedAmount'] ?? $this->disbursedAmount;
        $this->notes = $lineItemData['notes'] ?? $this->notes;
        $this->lenderNotes = $lineItemData['lenderNotes'] ?? $this->lenderNotes;
        $this->rejectReason = $lineItemData['rejectReason'] ?? $this->rejectReason;
    }

    public function getDbObject(): tblDrawRequestLineItems {
        return $this;
    }

    /**
     * Delete the lineitem from DB
     * @return ?array The result of the delete operation.
     */
    public function Delete(): ?array {
        return parent::Delete();
    }
}
