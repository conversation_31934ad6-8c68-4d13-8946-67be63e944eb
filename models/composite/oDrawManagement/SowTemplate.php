<?php
namespace models\composite\oDrawManagement;

use models\lendingwise\tblProcessingCompanyDrawTemplateSettings;
use models\composite\oDrawManagement\SowCategory;
use models\lendingwise\tblDrawTemplateCategories;
use models\lendingwise\db\tblDrawTemplateCategories_db;

class SowTemplate extends tblProcessingCompanyDrawTemplateSettings
{
    /**
     * @var SowCategory[] An array of SowCategory objects, indexed by category ID.
     */
    private array $categories = [];

    /**
     * SowTemplate constructor.
     * @param tblProcessingCompanyDrawTemplateSettings|null $template The database template object to initialize from.
     */
    public function __construct(?tblProcessingCompanyDrawTemplateSettings $template = null) {
        if ($template !== null) {
            foreach (get_object_vars($template) as $property => $value) {
                if (property_exists($this, $property)) {
                    $this->$property = $value;
                }
            }
        }
        if ($this->id) $this->loadCategories();
    }

    /**
     * Load categories for a given template ID
     * @param int $templateId Template ID to load categories for
     * @return void
     */
    private function loadCategories(): void
    {
        $categoriesData = tblDrawTemplateCategories::GetAll(
            [tblDrawTemplateCategories_db::COLUMN_TEMPLATEID => $this->id],
            [tblDrawTemplateCategories_db::COLUMN_ORDER => 'ASC']
        );

        foreach ($categoriesData as $tblCategoryObj) {
            $this->addCategory(new SowCategory($tblCategoryObj));
        }
        $this->sortCategories();
    }

    /**
     * Adds a SowCategory object to the template's categories.
     * @param SowCategory $category The category object to add.
     * @return void
     */
    public function addCategory(SowCategory $category): void {
        $this->categories[$category->id] = $category;
    }

    /**
     * Sorts the categories by their order property.
     * @return void
     */
    private function sortCategories(): void {
        uasort($this->categories, function($a, $b) {
            return $a->order <=> $b->order;
        });
    }

    /**
     * Converts the template object and its categories to an associative array.
     * @return array An associative array representation of the template.
     */
    public function toArray(): array {
        $data = parent::toArray();

        $categories = [];
        foreach ($this->categories as $category) {
            $categories[] = $category->toArray();
        }

        $data["categories"] = $categories;
        $data["pcId"] = $this->PCID;

        return $data;
    }

    /**
     * Save template settings for a processing company
     * @param array $data Optional form data containing template settings
     * @return array Result array with success status and message
     */
    public function Save(array $data = []): array
    {
        if (!empty($data)) {
            $this->allowBorrowersAddEditCategories = intval($data['allowBorrowersAddEditCategories'] ?? 0);
            $this->allowBorrowersDeleteCategories = intval($data['allowBorrowersDeleteCategories'] ?? 0);
            $this->allowBorrowersAddEditLineItems = intval($data['allowBorrowersAddEditLineItems'] ?? 0);
            $this->allowBorrowersDeleteLineItems = intval($data['allowBorrowersDeleteLineItems'] ?? 0);
            $this->allowBorrowersSOWRevisions = intval($data['allowBorrowersSOWRevisions'] ?? 0);
            $this->allowBorrowersExceedFinancedRehabCostOnRevision = intval($data['allowBorrowersExceedFinancedRehabCostOnRevision'] ?? 0);
            $drawFee = floatval($data['drawFee'] ?? 0);
            $this->drawFee = $drawFee;
        }

        return parent::Save();
    }

    /**
     * Get all categories for this template.
     * @return SowCategory[] Array of categories.
     */
    public function getCategories(): array {
        return $this->categories;
    }

    /**
     * Retrieves a specific category by its ID.
     * @param int $categoryId The ID of the category to retrieve.
     * @return SowCategory|null The SowCategory object if found, otherwise null.
     */
    public function getCategoryById(int $categoryId): ?SowCategory {
        return $this->categories[$categoryId] ?? null;
    }

    /**
     * Deletes categories and their associated line items from the template.
     *
     * @param array $categoriesIdsToDelete An array of category IDs to delete.
     * @return void
     */
    public function deleteCategories(array $categoriesIdsToDelete): void {
        if (empty($categoriesIdsToDelete)) return;

        foreach ($categoriesIdsToDelete as $categoryId) {
            if(isset($this->categories[$categoryId])) {
                foreach($this->categories[$categoryId]->lineItems as $lineItem) {
                    $lineItem->Delete();
                }
                $this->categories[$categoryId]->Delete();
                unset($this->categories[$categoryId]);
            }
        }
    }

    /**
     * Deletes specific line items from the template across all categories.
     *
     * @param array $lineItemIdsToDelete An array of line item IDs to delete.
     * @return void
     */
    public function deleteLineItems(array $lineItemIdsToDelete): void {
        if (empty($lineItemIdsToDelete)) return;

        $idsToDelete = array_flip($lineItemIdsToDelete);

        foreach ($this->categories as $category) {
            foreach ($category->lineItems as $lineItemId => $lineItem) {
                if (isset($idsToDelete[$lineItemId])) {
                    $lineItem->Delete();
                    unset($category->lineItems[$lineItemId]);
                    unset($idsToDelete[$lineItemId]);
                    if (empty($idsToDelete)) {
                        return;
                    }
                }
            }
        }
    }
}
