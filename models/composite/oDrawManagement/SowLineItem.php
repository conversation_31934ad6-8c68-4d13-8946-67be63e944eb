<?php
namespace models\composite\oDrawManagement;

use models\lendingwise\tblDrawTemplateLineItems;

class SowLineItem extends tblDrawTemplateLineItems
{

    /**
     * SowLineItem constructor.
     * @param tblDrawTemplateLineItems|null $lineItem The database line item object to initialize from.
     */
    public function __construct(?tblDrawTemplateLineItems $lineItem = null) {
        $this->order = 1;

        if ($lineItem !== null) {
            foreach (get_object_vars($lineItem) as $property => $value) {
                if (property_exists($this, $property)) {
                    $this->$property = $value;
                }
            }
        }
    }

    /**
     * Saves the current line item object to the database.
     * @param array $lineItemData Optional line item data to set before saving
     * @return array The result of the save operation.
     */
    public function Save(array $lineItemData = []): array {
        if (!empty($lineItemData)) {
            $this->setFromArray($lineItemData);
        }
        return parent::Save();
    }

    /**
     * Sets the properties of the line item from an associative array.
     * @param array $lineItemData Associative array containing line item data.
     * @return void
     */
    private function setFromArray(array $lineItemData): void {
        $this->id = $lineItemData['id'] ?? null;
        $this->templateId = $lineItemData['templateId'];
        $this->categoryId = $lineItemData['categoryId'];
        $this->name = $lineItemData['name'];
        $this->description = $lineItemData['description'] ?? '';
        $this->order = $lineItemData['order'] ?? 1;
    }

    /**
     * Delete the lineitem from DB
     * @return ?array The result of the delete operation.
     */
    public function Delete(): ?array {
        return parent::Delete();
    }

}
