<?php
namespace models\composite\oDrawManagement;

use models\types\strongType;
use models\PageVariables;
use models\lendingwise\tblProcessingCompanyDrawTemplateSettings;
use models\lendingwise\db\tblProcessingCompanyDrawTemplateSettings_db;
use models\composite\oDrawManagement\SowCategory;
use models\composite\oDrawManagement\SowLineItem;
use models\composite\oDrawManagement\SowTemplate;
use models\Database2;

class SowTemplateManager extends strongType
{
    /**
     * @var int|null The PC ID associated with the template manager.
     */
    private ?int $pcId = null;

    /**
     * @var int|null The ID of the SOW template.
     */
    private ?int $templateId = null;

    /**
     * @var array|null An array of categories associated with the template.
     */
    private ?array $categories = null;

    /**
     * @var SowTemplate|null The SOW template object.
     */
    private ?SowTemplate $template = null;

    /**
     * @var int The maximum number of categories allowed.
     */
    public const MAX_CATEGORIES = 20;

    /**
     * SowTemplateManager constructor.
     * @param int $PCId The PC ID to initialize the manager with.
     */
    public function __construct($PCId)
    {
        $this->pcId = $PCId;
        $this->getOrCreateTemplateId();
        if($this->templateId) $this->loadTemplate();
    }

    /**
     * Static factory method to create an instance of SowTemplateManager.
     * @param int|null $PCId The PC ID to initialize the manager with.
     * @return SowTemplateManager A new instance of SowTemplateManager.
     */
    public static function forProcessingCompany($PCId = null): SowTemplateManager {
        if ($PCId === null) {
            $PCId = PageVariables::$PCID;
            if ($PCId === null) {
                throw new \InvalidArgumentException("PCID must be provided or available in PageVariables.");
            }
        }
        return new self($PCId);
    }

    /**
     * Gets the template ID for the current PCID, creates one if it doesn't exist.
     * @return int|null The template ID or null if PCID is not set.
     */
    public function getOrCreateTemplateId(): ?int
    {
        if (!$this->pcId) {
            return null;
        }

        $pcDrawTemplateSettings = tblProcessingCompanyDrawTemplateSettings::Get([tblProcessingCompanyDrawTemplateSettings_db::COLUMN_PCID => $this->pcId]);
        if ($pcDrawTemplateSettings) {
            $this->templateId = (int)$pcDrawTemplateSettings->id;
        } else {
            $newTemplateSettings = new tblProcessingCompanyDrawTemplateSettings();
            $newTemplateSettings->PCID = $this->pcId;
            if ($newTemplateSettings->Save()) {
                $this->templateId = (int)$newTemplateSettings->id;
            } else {
                return null;
            }
        }
        return $this->templateId;
    }

    /**
     * Public getter for templateId.
     * @return int|null The template ID.
     */
    public function getTemplateId(): ?int
    {
        return $this->templateId;
    }

    /**
     * Load template with all categories and line items.
     * This method ensures that the template is loaded from the database along with its associated categories and line items.
     * If `templateId` is not set, it attempts to get or create one.
     * @return void
     */
    private function loadTemplate(): void
    {
        try {
            if (!$this->templateId) {
                $this->getOrCreateTemplateId();
            }

            // Use fully qualified class name to avoid late static binding issues
            $sowTemplate = tblProcessingCompanyDrawTemplateSettings::Get([tblProcessingCompanyDrawTemplateSettings_db::COLUMN_ID => $this->templateId]);
            $this->template = new SowTemplate($sowTemplate);
        } catch (\Exception $e) {
            $this->template = null;
            return;
        }
    }

    /**
     * Retrieves the SowTemplate object.
     * If the template is not already loaded, it will be loaded first.
     * @return SowTemplate|null The SOW template object.
     */
    public function getTemplate(): ?SowTemplate
    {
        if (is_null($this->template)) {
            $this->loadTemplate();
        }
        return $this->template;
    }

    /**
     * Retrieves the categories data as an array.
     * @return array An array containing the categories data.
     */
    public function getTemplateDataArray(): array
    {
        $template = $this->template;
        return $template->toArray();
    }

    /**
     * Saves the provided template settings.
     * @param array $data An array of template settings data.
     * @return void
     */

    public function saveSettings(array $data): void
    {
        $this->template->Save($data);
        $this->loadTemplate();
    }

    /**
     * Saves the provided categories data.
     * This method handles the creation, updating, and deletion of categories and their associated line items.
     * It operates within a database transaction to ensure data integrity.
     * @param array $categoriesData An array of category data, where each category is an associative array.
     * @return bool True on success, false on failure (e.g., validation errors, database transaction failure).
     */
    public function saveCategories(array $categoriesData): bool
    {
        if (count($categoriesData) > self::MAX_CATEGORIES) {
            return false;
        }

        $db = Database2::getInstance();
        $db->beginTransaction();
        try {
            $existingCategories = $this->template->getCategories();
            $existingCategoryIds = array_column($existingCategories, 'id');
            $updatedCategoryIds = [];

            foreach ($categoriesData as $catData) {
                $id = $catData['id'];
                $catData['templateId'] = $this->templateId;

                if ($id) {
                    $categoryObject = $this->template->getCategoryById($id);
                } else {
                    $categoryObject = new SowCategory();
                }
                $categoryObject->Save($catData);
                $updatedCategoryIds[] = $categoryObject->id;
            }

            $idsToDelete = array_diff($existingCategoryIds, $updatedCategoryIds);
            $this->template->deleteCategories($idsToDelete);

            $db->commit();
            $this->loadTemplate();
            return true;

        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }

    /**
     * Save line items with transaction handling and validation.
     * This method processes and saves line items, ensuring they are associated with the correct categories and template.
     * It also handles the deletion of line items that are no longer present in the provided data.
     * @param array $lineItemsData Grouped line items data format: `['categoryId' => [['id', 'categoryName', 'description'], ...]]`
     * @return bool True on success, False on failure.
     * @throws \Exception On invalid category IDs or database errors.
     */
    public function saveLineItems(array $lineItemsData): bool
    {
         $db = Database2::getInstance();
         $db->beginTransaction();
         try {
            if (count($lineItemsData) > self::MAX_CATEGORIES) {
                return false;
            }

            foreach ($lineItemsData as $categoryId => $lineItems) {
                $sowCategory = $this->template->getCategoryById($categoryId);
                $existingLineItemIds = $sowCategory ? array_column($sowCategory->getAllLineItems(), 'id') : [];
                $updatedLineItemIds = [];

                foreach ($lineItems as $liData) {
                    $id = $liData['id'] ?? null;
                    $categoryId = $liData['categoryId'];
                    $liData['templateId'] = $this->templateId;
                    if ($id) {
                        $lineItemObject = $sowCategory->getLineItemById($id);
                    } else {
                        $lineItemObject = new SowLineItem();
                    }
                    $lineItemObject->Save($liData);
                    $updatedLineItemIds[] = $lineItemObject->id;
                }

                $idsToDelete = array_diff($existingLineItemIds, $updatedLineItemIds);
                $this->template->deleteLineItems($idsToDelete);
             }

            $db->commit();
            $this->loadTemplate();
            return true;

        } catch (\Exception $e) {
            $db->rollBack();
            return false;
        }
    }
}
