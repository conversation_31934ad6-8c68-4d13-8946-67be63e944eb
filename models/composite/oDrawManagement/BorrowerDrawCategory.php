<?php
namespace models\composite\oDrawManagement;

use models\lendingwise\tblDrawRequestCategories;
use models\lendingwise\tblDrawRequestLineItems;
use models\lendingwise\db\tblDrawRequestLineItems_db;
use models\composite\oDrawManagement\BorrowerDrawLineItem;

class BorrowerDrawCategory extends tblDrawRequestCategories
{
    private ?array $lineItems = null;

    /**
     * BorrowerDrawCategory constructor.
     * @param tblDrawRequestCategories|null $category The database category object to initialize from.
     */
    public function __construct(?tblDrawRequestCategories $category = null) {
        $this->order = 1;
        $this->description = '';

        if ($category !== null) {
            foreach (get_object_vars($category) as $property => $value) {
                if (property_exists($this, $property)) {
                    $this->$property = $value;
                }
            }
        }
    }

    /**
     * Saves the current category object to the database.
     * @param array $categoryData Optional category data to set before saving
     * @return array The result of the save operation.
     */
    public function Save(array $categoryData = []): array {
        if (!empty($categoryData)) {
            $this->setFromArray($categoryData);
        }
        return parent::Save();
    }

    /**
     * Sets the properties of the category from an associative array.
     * @param array $categoryData Associative array containing category data.
     * @return void
     */
    private function setFromArray(array $categoryData): void {
        $this->id = $categoryData['id'] ?? null;
        $this->drawId = $categoryData['drawId'];
        $this->categoryName = $categoryData['categoryName'];
        $this->description = $categoryData['description'] ?? '';
        $this->order = $categoryData['order'] ?? 1;
    }

    /**
     * Delete the category from DB
     * @return ?array The result of the delete operation.
     */
    public function Delete(): ?array {
        return parent::Delete();
    }

    /**
     * Loads line items associated with this category from the database.
     * @return void
     */
    private function loadLineItems(): void {
        $lineItemsData = tblDrawRequestLineItems::GetAll(
            [tblDrawRequestLineItems_db::COLUMN_CATEGORYID => $this->id],
            [tblDrawRequestLineItems_db::COLUMN_CATEGORYID => 'ASC', tblDrawRequestLineItems_db::COLUMN_ORDER => 'ASC']
        );

        foreach ($lineItemsData as $lineItemData) {
            $this->addLineItem(new BorrowerDrawLineItem($lineItemData));
        }
        $this->sortLineItems();
    }

    /**
     * Adds a BorrowerDrawLineItem object to the category's line items.
     * @param BorrowerDrawLineItem $lineItem The line item object to add.
     * @return void
     */
    private function addLineItem(BorrowerDrawLineItem $lineItem): void {
        $this->lineItems[$lineItem->id] = $lineItem;
    }

    /**
     * Sorts the line items by their order property.
     * @return void
     */
    private function sortLineItems(): void {
        uasort($this->lineItems, function($a, $b) {
            return $a->order <=> $b->order;
        });
    }

    /**
     * Retrieves all line items associated with this category.
     * @return BorrowerDrawLineItem[] An array of BorrowerDrawLineItem objects.
     */
    public function getAllLineItems(): array {
        if (is_null($this->lineItems)) {
            $this->loadLineItems();
        }
        return $this->lineItems ?? [];
    }

    /**
     * Retrieves a specific line item by its ID.
     * @param int $lineItemId The ID of the line item to retrieve.
     * @return BorrowerDrawLineItem|null The BorrowerDrawLineItem object if found, otherwise null.
     */
    public function getLineItemById($lineItemId): ?BorrowerDrawLineItem {
        if (is_null($this->lineItems)) {
            $this->loadLineItems();
        }
        return $this->lineItems[$lineItemId] ?? null;
    }

    /**
     * Converts the category object and its line items to an associative array.
     * @return array An associative array representation of the category.
     */
    public function toArray(): array {
        $data = parent::toArray();

        $data["lineItems"] = [];
        $lineItems = $this->getAllLineItems();
        foreach ($lineItems as $lineItem) {
            $data["lineItems"][] = $lineItem->toArray();
        }

        return $data;
    }
}
