<?php
namespace models\composite\oDrawManagement;

use models\lendingwise\tblDrawRequestLineItems_h;

class BorrowerDrawLineItemHistory extends tblDrawRequestLineItems_h
{

    /**
     * BorrowerDrawLineItemHistory constructor.
     * @param tblDrawRequestLineItems_h|null $lineItemHistory The database line item history object to initialize from.
     */
    public function __construct(?tblDrawRequestLineItems_h $lineItemHistory = null) {
        if ($lineItemHistory !== null) {
            foreach (get_object_vars($lineItemHistory) as $property => $value) {
                if (property_exists($this, $property)) {
                    $this->$property = $value;
                }
            }
        }
    }

    /**
     * Saves the current line item history object to the database.
     * @return array The result of the save operation.
     */
    public function Save(array $lineItemData = []): array {
        if (!empty($lineItemData)) {
            $this->setFromArray($lineItemData);
        }
        return parent::Save();
    }

    /**
     * Sets properties from an array.
     * @param array $data The data array.
     * @return void
     */
    private function setFromArray(array $data): void {
        if (isset($data['recordId'])) $this->recordId = $data['recordId'];
        if (isset($data['lineItemId'])) $this->lineItemId = $data['lineItemId'];
        if (isset($data['completedAmount'])) $this->completedAmount = $data['completedAmount'];
        if (isset($data['completedPercent'])) $this->completedPercent = $data['completedPercent'];
        if (isset($data['requestedAmount'])) $this->requestedAmount = $data['requestedAmount'];
        if (isset($data['disbursedAmount'])) $this->disbursedAmount = $data['disbursedAmount'];
        if (isset($data['notes'])) $this->notes = $data['notes'];
        if (isset($data['lenderNotes'])) $this->lenderNotes = $data['lenderNotes'];
    }

    /**
     * Gets the database object.
     * @return tblDrawRequestLineItems_h The database object.
     */
    public function getDbObject(): tblDrawRequestLineItems_h {
        return $this;
    }
}
