<?php
/**
 * Reusable Stepper Component for Draw Management
 *
 * @param array $steps - Array of step configurations
 * @param string $activeStep - Currently active step
 */

$steps = $steps ?? [
    [
        'key' => 'categories',
        'number' => 1,
        'label' => 'Categories'
    ],
    [
        'key' => 'line-items',
        'number' => 2,
        'label' => 'Line Items'
    ]
];

$activeStep = $activeStep ?? 'categories';
?>

<!-- Stepper UI -->
<div class="stepper-container mb-5">
    <?php foreach ($steps as $step): ?>
        <div class="step <?php echo $step['key'] === $activeStep ? 'active' : ''; ?>" data-step="<?php echo $step['key']; ?>">
            <span class="step-icon"><?php echo $step['number']; ?></span>
            <span class="step-label"><?php echo $step['label']; ?></span>
        </div>
    <?php endforeach; ?>
</div>
