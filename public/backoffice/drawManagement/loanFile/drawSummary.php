<?php

use models\composite\oDrawManagement\DrawSummaryManager;
use models\Controllers\backoffice\LMRequest;

DrawSummaryManager::initialize($LMRId, $drawRequestManager);

?>

<style>
.summary-section {
    background: #fff;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.summary-section h4 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 20px;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 10px;
}

.summary-grid > .row:nth-child(odd) {
    background-color: #f5f5f5;
    border-radius: 4px;
}

.summary-item {
    display: flex;
    flex-direction: column;
    padding: 10px 0;
    position: relative;
}

.summary-item:hover .copy-icon {
    opacity: 1;
    visibility: visible;
}

.summary-label {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.summary-value {
    font-size: 1rem;
    font-weight: 500;
    padding-left: 5px;
    position: relative;
}

.copy-icon {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease, visibility 0.2s ease;
    cursor: pointer;
    font-size: 0.9rem;
    color: #6c757d;
    padding: 2px 5px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.9);
}

.copy-icon:hover {
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
}


</style>

<div class="summary-section">
    <h4>Summary</h4>

    <div class="col-12 summary-grid">
        <!-- Row 1: Address Information -->
        <div class="row">
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Address</div>
                    <div class="summary-value text-muted"><?= htmlspecialchars(DrawSummaryManager::$address); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">City</div>
                    <div class="summary-value text-muted"><?= htmlspecialchars(DrawSummaryManager::$city); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">State</div>
                    <div class="summary-value text-muted"><?= htmlspecialchars(DrawSummaryManager::$state); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Zip</div>
                    <div class="summary-value text-muted"><?= htmlspecialchars(DrawSummaryManager::$zip); ?></div>
                </div>
            </div>
        </div>

        <!-- Row 2: Loan Information -->
        <div class="row">
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Initial Loan</div>
                    <div class="summary-value text-success">$<?= number_format(DrawSummaryManager::$initialLoan); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Rehab Cost Financed</div>
                    <div class="summary-value text-success">$<?= number_format(DrawSummaryManager::$rehabCostFinanced); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Total Loan Amount</div>
                    <div class="summary-value text-success">$<?= number_format(DrawSummaryManager::$totalLoanAmount); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Current Loan Balance</div>
                    <div class="summary-value text-success">$<?= number_format(DrawSummaryManager::$currentLoanBalance); ?></div>
                </div>
            </div>
        </div>

        <!-- Row 3: Additional Loan Information -->
        <div class="row">
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Rehab Cost</div>
                    <div class="summary-value text-success">$<?= number_format(DrawSummaryManager::$rehabCost); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">ARV</div>
                    <div class="summary-value text-success"><?= DrawSummaryManager::$arv; ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Total Draws Funded</div>
                    <div class="summary-value text-success">$<?= htmlspecialchars(DrawSummaryManager::getFormattedTotalDrawsFunded()); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Holdback Remaining</div>
                    <div class="summary-value text-success">$<?= htmlspecialchars(DrawSummaryManager::getFormattedHoldbackRemaining()); ?></div>
                </div>
            </div>
        </div>

        <!-- Row 4: Date Information -->
        <div class="row">
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Closing Date</div>
                    <div class="summary-value text-primary"><?= htmlspecialchars(DrawSummaryManager::$closingDate); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Maturity Date</div>
                    <div class="summary-value text-primary"><?= htmlspecialchars(DrawSummaryManager::$maturityDate); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Date of Last Draw</div>
                    <div class="summary-value text-primary"><?= htmlspecialchars(DrawSummaryManager::$dateOfLastDraw); ?></div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="summary-item">
                    <div class="summary-label">Date of Current Draw</div>
                    <div class="summary-value text-primary"><?= htmlspecialchars(DrawSummaryManager::$dateOfCurrentDraw); ?></div>
                </div>
            </div>
        </div>
    </div>
</div>
