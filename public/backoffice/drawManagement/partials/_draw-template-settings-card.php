    <div class="card-body drawTemplateSettings collapse">
        <form name="drawTemplateSettingsForm" id="drawTemplateSettingsForm" method="post" action="api_v2/draw_management/TemplateSettings">

        <!-- Hidden input fields for form values -->
        <input type="hidden" name="pcid" value="<?= $pcId ?>">
        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to Add / Edit Categories?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersAddEditCategoriesTog" onchange="toggleSwitch('allowBorrowersAddEditCategoriesTog','allowBorrowersAddEditCategories','1','0');"/>
                        <span></span>
                        <input type="hidden" name="allowBorrowersAddEditCategories" id="allowBorrowersAddEditCategories" value="0">
                    </label>
                </span>
            </div>
        </div>

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to Remove Your Template's Categories?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersDeleteCategoriesTog" onchange="toggleSwitch('allowBorrowersDeleteCategoriesTog','allowBorrowersDeleteCategories','1','0' );"/>
                        <span></span>
                        <input type="hidden" name="allowBorrowersDeleteCategories" id="allowBorrowersDeleteCategories" value="0">
                    </label>
                </span>
            </div>
        </div>

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to Add / Edit Line Items?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersAddEditLineItemsTog" onchange="toggleSwitch('allowBorrowersAddEditLineItemsTog','allowBorrowersAddEditLineItems','1','0' );"/>
                        <span></span>
                        <input type="hidden" name="allowBorrowersAddEditLineItems" id="allowBorrowersAddEditLineItems" value="0">
                    </label>
                </span>
            </div>
        </div>

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to Remove Your Template's Line Items?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersDeleteLineItemsTog" onchange="toggleSwitch('allowBorrowersDeleteLineItemsTog','allowBorrowersDeleteLineItems','1','0' );"/>
                        <span></span>
                        <input type="hidden" name="allowBorrowersDeleteLineItems" id="allowBorrowersDeleteLineItems" value="0">
                    </label>
                </span>
            </div>
        </div>

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to submit for Scope of Work Revisions?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersSOWRevisionsTog" onchange="toggleSwitch('allowBorrowersSOWRevisionsTog','allowBorrowersSOWRevisions','1','0' );"/>
                        <span></span>
                        <input type="hidden" name="allowBorrowersSOWRevisions" id="allowBorrowersSOWRevisions" value="0">
                    </label>
                </span>
            </div>
        </div>

        <div class="form-group row align-items-center">
            <label class="col-lg-4">Allow Borrowers to request more then rehab cost financed during a revision?</label>
            <div class="col-lg-2">
                <span class="switch switch-icon">
                    <label>
                        <input class="form-control" type="checkbox"
                        value="" id="allowBorrowersExceedFinancedRehabCostOnRevisionTog" onchange="toggleSwitch('allowBorrowersExceedFinancedRehabCostOnRevisionTog','allowBorrowersExceedFinancedRehabCostOnRevision','1','0' );"/>
                        <span></span>
                        <input type="hidden" name="allowBorrowersExceedFinancedRehabCostOnRevision" id="allowBorrowersExceedFinancedRehabCostOnRevision" value="0">
                    </label>
                </span>
            </div>
        </div>
        <div class="form-group row align-items-center">
            <label class="col-lg-4">Draw Fee</label>
            <div class=col-lg-4>
                <div class="input-group">
                    <div class="input-group-prepend">
                        <span class="input-group-text" id="inputGroup-sizing-default">$</span>
                    </div>
                    <input type="text" name="drawFee" id="drawFee" class="form-control" aria-label="Draw Fees" aria-describedby="inputGroup-sizing-default" placeholder="0.00">
                </div>
            </div>
        </div>
        <div class="d-flex justify-content-center mt-5">
            <button type="submit" class="btn btn-primary save-settings ml-2">Save</button>
        </div>
        </form>
    </div>

<script>
$(document).ready(function() {
    // Handle form submission via AJAX
    $('#drawTemplateSettingsForm').on('submit', function(e) {
        e.preventDefault();

        const $form = $(this);
        const $submitBtn = $form.find('.save-settings');
        const originalText = $submitBtn.text();

        // Disable submit button and show loading state
        $submitBtn.prop('disabled', true).text('Saving...');

        // Serialize form data
        const formData = $form.serialize();

        $.ajax({
            url: '/backoffice/api_v2/draw_management/TemplateSettings',
            type: 'POST',
            data: formData,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    toastrNotification('Template settings saved successfully!', 'success');
                } else {
                    toastrNotification('Error saving settings: ' + (response.message || 'Unknown error'), 'error');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX Error:', error);
                toastrNotification('Error saving settings. Please try again.', 'error');
            },
            complete: function() {
                $submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });
});
</script>
