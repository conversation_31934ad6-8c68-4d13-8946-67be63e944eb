<?php
namespace pages\backoffice\loan\servicing;

use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glPCID;
use models\constants\gl\glUserRole;
use models\Controllers\backoffice\LMRequest;
use models\cypher;
use models\LoanMenu;
use models\MVC;
use models\PageVariables;
use models\Request;

?>


<div class="card card-custom p-0 card-sticky " id="kt_page_sticky_card">
    <div class="card-header px-2 py-2 border" style="">
        <div class="pipelineNavigation ">
            <ul class="nav nav-pills">
                <?php

                $eId = null;
                $rId = null;
                $lId = null;
                $op = cypher::myEncryption('edit');

                if(LMRequest::File()->LMRId) {
                    $eId = cypher::myEncryption(LMRequest::File()->getTblFileResponse_by_LMRId()->executiveId);
                    $rId = cypher::myEncryption(LMRequest::File()->getTblFileResponse_by_LMRId()->LMRResponseId);
                    $lId = cypher::myEncryption(LMRequest::File()->LMRId);
                }

                $params = 'eId=' . $eId . '&lId=' . $lId . '&rId=' . $rId;

                foreach (LoanMenu::$fileTabs as $fileTabsKey => $item) {
                    $icondivCls = '';
                    if ($fileTabsKey == LoanMenu::$activeTab) {
                        if ((LoanMenu::$PCID == glPCID::PCID_MORTGAGE_ASSISTANCE) && (in_array($fileTabsKey, ['CI', 'MI', 'IE', 'HA', 'AL', 'BC', 'PI', 'ADMIN']))) {
                            $divCls = ' active text-danger statusTabActive_1 ';
                        } else {
                            $divCls = 'active statusTabActive';
                        }
                        $icondivCls = ' text-white ';
                        $newHref = '';
                    } else {
                        if ((LoanMenu::$PCID == glPCID::PCID_MORTGAGE_ASSISTANCE) && (in_array($fileTabsKey, ['CI', 'MI', 'IE', 'HA', 'AL', 'BC', 'PI', 'ADMIN']))) {
                            $divCls = ' text-danger ';
                        } else {
                            $divCls = ' text-dark-65 ';
                        }
                        $newHref = glUserRole::getUserBaseURL() . 'LMRequest.php?eId=' . $eId . '&lId=' . $lId . '&rId=' . $rId . '&tabOpt=' . $fileTabsKey . '&op=' . $op;
                    }
                    if (PageVariables::$userGroup == 'Client') {
                        LoanMenu::$fileTabs['BC'] = 'Billing';
                    }

                    ?>

                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="<?php echo LoanMenu::$fileTabs[$fileTabsKey] ?>"
                        id="tab_<?php echo $fileTabsKey; ?>">
                        <a
                                class="nav-link px-2 loanFileButtonLink <?php echo $divCls ?>"
                                href="<?php echo $newHref ?>"
                                style='cursor:pointer'
                        >
                            <?php echo LoanMenu::$fileTabs[$fileTabsKey] ?>
                            <i class="ml-2  <?php echo $icondivCls;
                            if (isset(LoanMenu::$fileTabIcons[$fileTabsKey])) {
                                echo LoanMenu::$fileTabIcons[$fileTabsKey];
                            } else {
                                echo 'fa fa-list ';
                            } ?>"></i>
                        </a>
                    </li>
                    <?php
                }
                ?>

                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton
                    <?php if (PageVariables::$userGroup == 'Client' || LoanMenu::$PCID == glPCID::PCID_MORTGAGE_ASSISTANCE) {
                        echo 'd-none';
                    } ?>" data-html="true"
                        title="Fannie Mae"
                        id="tab_fm34">
                    <a
                            class="nav-link px-2 loanFileButtonLink <?php echo LoanMenu::$activeTab == 'fm34' ? 'active statusTabActive' : 'text-dark-65'; ?>"
                            style='cursor:pointer'
                            data-lmrid="<?php echo htmlspecialchars(Request::GetClean('lId')); ?>"
                            href="/backoffice/loan/fm34?<?php echo $params; ?>&tabOpt=fm34">
                        Fannie Mae
                        <i class="ml-2 fa fa-list"></i>
                    </a>
                    </li>

                <?php if (LMRequest::$hasChangeLog && LoanMenu::$PCID != glPCID::PCID_MORTGAGE_ASSISTANCE) { ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton
                    <?php if (PageVariables::$userGroup == 'Client') {
                        echo 'd-none';
                    } ?>" data-html="true"
                        title="Change Log"
                        id="tab_change_log">
                    <span
                            class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'
                            data-lmrid="<?php echo htmlspecialchars(Request::GetClean('lId')); ?>"
                            onclick="submitAndNavigateTab('CL');">
                        Change Log
                        <i class="ml-2 fa fa-list"></i>
                    </span>
                    </li>
                <?php } ?>

                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton
                    <?php if (stristr(PageVariables::$userEmail, '@lendingwise.com') === false) {
                    echo 'd-none';
                } ?>" data-html="true"
                    title="File Copies"
                    id="tab_file_copies">
                    <a
                            class="nav-link px-2 loanFileButtonLink <?php echo MVC::$web->path !== '/backoffice/loan/copies' ? 'text-dark-65' : 'active statusTabActive'; ?>"
                            style='cursor:pointer'
                            href="<?php echo LMRequest::File()->publicURL('copies', false); ?>">
                        Copies
                        <i class="ml-2 fa fa-list"></i>
                    </a>
                </li>

                <?php if (SHOW_DEBUG_TOOLS || Request::GetClean('show_rule_check') ?? null || PageVariables::PC()->allowAutomatedRulesV2) { ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Rule Check"
                        id="tab_rule_check">
                        <span
                                class="nav-link px-2 loanFileButtonLink"
                                onclick="submitAndNavigateTab('RC');" style='cursor:pointer'>
                            Rule Check
                            <i class="ml-2 fa fa-check"></i>
                        </span>
                    </li>
                <?php } ?>
                <?php if (SHOW_DEBUG_TOOLS) { ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Clear Loan Form"
                        id="clear_loan_form">
                    <span
                            class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'
                            onclick="debugTools.clearForm('loanModForm');">
                        Clear Loan Form
                        <i class="ml-2 fa fa-eraser"></i>
                    </span>
                    </li>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Fill Loan Form A"
                        id="fill_loan_form">
                    <span
                            class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'
                            onclick="debugTools.fillForm('loanModForm');">
                        Fill Loan Form A
                        <i class="ml-2 fa fa-edit"></i>
                    </span>
                    </li>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Fill Loan Form A"
                        id="fill_loan_form">
                    <span
                            class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'
                            onclick="debugTools.fillForm('loanModForm', true);">
                        Fill Loan Form B
                        <i class="ml-2 fa fa-edit"></i>
                    </span>
                    </li>
                <?php } ?>
            </ul> <!-- tab Container -->
        </div>
    </div>
</div>

    <script src="/backoffice/loan/js/LoanMenu.js?<?php echo CONST_JS_VERSION; ?>"></script>
    <script type="text/javascript">

        $(function () {
            LoanMenu.FormFields = <?php echo json_encode(LoanMenu::$fieldsInfo); ?>;
            LoanMenu.fileTab = "<?php echo htmlspecialchars(LoanMenu::$fileTab); ?>";
            LoanMenu.special_fields = ['LMRClientType', 'borrowerFName'];
            LoanMenu.userGroup = "<?php echo PageVariables::$userGroup; ?>";
            LoanMenu.fileModule = $('#fileModule').val();
            LoanMenu.activeTab = '<?php echo htmlspecialchars(LoanMenu::$activeTab); ?>';

            LoanMenu.op = "<?php echo LoanMenu::$op;?>";
            LoanMenu.selectedFileType = "<?php echo $fileModuleInfo[0]['moduleCode'] ?? '';?>";
            LoanMenu.form_original_data = $("#loanModForm").serialize();
            LoanMenu.typeOfHMLOLoanRequesting = "<?php echo LoanMenu::$typeOfHMLOLoanRequesting; ?>";
            LoanMenu.userRole = "<?php echo PageVariables::$userRole;?>";


            LoanMenu.Init();
        });

        <?php  if(LoanMenu::$cemail && LoanMenu::$PCID > 0){  ?>
        populateClientInfo('loanModForm', '<?php echo htmlspecialchars(LoanMenu::$cemail); ?>', '<?php echo LoanMenu::$PCID; ?>');
        <?php    } ?>
    </script>

