<?php
global $isPLO, $userRole, $userGroup, $PCTimeZone;

use models\composite\oAcqualify\assignUserToAccount;
use models\composite\oAcqualify\createUser;
use models\composite\oBranch\getacqualifyBranchDetails;
use models\composite\oEmployee\checkEmployeeExists;
use models\composite\oEmployee\saveAttorneyBarInfo;
use models\composite\oEmployee\saveEmployeeInfo;
use models\composite\oEmployee\updateLMRAEIDToEmp;
use models\composite\oPC\doesPCExceedsNoOfUsers;
use models\composite\oPC\getacqualifyPCDetails;
use models\composite\oPC\getacqualifyUserDetails;
use models\composite\oPC\postacQualifylog;
use models\composite\oUser\avatar;
use models\constants\accessSecondaryWFPC;
use models\constants\gl\glMimeTypes;
use models\constants\gl\globalWithUSTimeZones;
use models\Database2;
use models\FileStorage;
use models\lendingwise\tblProcessingCompany;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\Strings;


session_start();
require '../includes/util.php';

require CONST_BO_PATH . 'initPageVariables.php';
require CONST_BO_PATH . 'getPageVariables.php';

$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
$globalWithUSTimeZones = globalWithUSTimeZones::$globalWithUSTimeZones;

$processorId = 0;
$processorName = '';
$pwd = '';
$tollFree1 = '';
$tollFree2 = '';
$tollFree3 = '';
$fax1 = '';
$fax2 = '';
$fax3 = '';
$tollFree = '';
$fax = '';
$cellNo1 = '';
$cellNo2 = '';
$cellNo3 = '';
$ext = '';
$cellNumber = '';
$role = '';
$updateCount = 0;
$insertCount = 0;
$LMRAEId = 0;
$PCID = 0;
$LMRAEIdArray = [];
$allowToViewAllFiles = 1;
$serviceProvider = '';
$timeZone = '';
$allowExcelDownload = 0;
$sendMarketingEmail = 1;
$allowToListIn3PartyForm = 1;
$allowEmpToLogin = 0;
$phone1 = '';
$phone2 = '';
$phone3 = '';
$phoneExt = '';
$phone = '';
$allowEmpToCreateFiles = 1;
$allowEmpToCreateTasks = 1;
$allowEmpToSeeDashboard = 1;
$seePrivate = 0;
$accessRestriction = 0;
$allowedToEditOwnNotes = 1;
$allowedToDeleteUplodedDocs = 1;
$newProcessorId = 0;
$processorNumb = 0;
$seeBilling = 1;
$alternatePC = '';
$permissionToREST = 1;
$allowBOToEditLMRFile = 0;
$allowEmailCampaign = 1;
$resultArray = [];
$email = '';
$changeDIYPlan = 0;
$processorLName = '';
$allowToUpdateFileAdminSection = 0;
$allowToLASubmit = 0;
$allowToCFPBSubmit = 0;
$subscribeToHOME = 0;
$allowToSendFax = 0;
$allowEmployeeToEditCommission = 1;
$allowEmployeeToSeeCommission = 1;
$allowToSendFileDesignation = 1;
$barNo = '';
$allowEmpToSeePublicNotes = 1;
$attorneyRate = '';
$paralegalRate = '';
$serviceFeeRate = '';
$WFIDsArray = [];
$empState = '';
$empCounty = '';
$empCity = '';
$empAddress = '';
$zipCode = '';
$allowToChangeOrAssignBranchForFile = 1;
$allowToViewCFPBPipeline = 0;

$allowToAccessRAM = 0;
$allowToSeeBillingSectionForFile = 0;
$allowToGetBorrowerUploadDocsNotification = 1;
$allowToLockLoanFileEmpl = 1;
$alowareErr = '';
$allowToViewMarketPlace = 1;
$shareThisFile = 1;
$allowToViewContactsList = 1;
$allowToSubmitOffer = 0;
$allowToViewCreditScreening = 1;
$enable2FAAuthentication = 0;
$TwoFAType = 'email';
$userPriceEngineStatus = 0;
$loanpassLogin = '';
$loanpassPassword = '';
$allowToEditLoanStage = 0;

$notifyBODocUpload = 0;
$notifyBranchDocUpload = 0;
$notifyLODocUpload = 0;
$notifyBrokerDocUpload = 0;
$notifyDocUploadRequest = 0;
$notifyNewFileCreated = 0;

if ($isPLO == 1) $allowToSendHomeownerLink = 1; else $allowToSendHomeownerLink = 0;

if (isset($_POST['pId'])) $processorId = trim($_POST['pId']);
if (isset($_POST['email'])) $email = trim($_POST['email']);
if (isset($_POST['processorName'])) $processorName = trim($_POST['processorName']);
if (isset($_POST['processorLName'])) $processorLName = trim($_POST['processorLName']);
if (isset($_POST['pwd'])) $pwd = trim($_POST['pwd']);
//if (isset($_POST["tollFree1"])) $tollFree1 = trim($_POST["tollFree1"]);
//if (isset($_POST["tollFree2"])) $tollFree2 = trim($_POST["tollFree2"]);
//if (isset($_POST["tollFree3"])) $tollFree3 = trim($_POST["tollFree3"]);
if (isset($_POST['tollFree'])) $tollFree = trim($_POST['tollFree']);
if (isset($_POST['ext'])) $ext = trim($_POST['ext']);
//if (isset($_POST["cellNo1"])) $cellNo1 = trim($_POST["cellNo1"]);
//if (isset($_POST["cellNo2"])) $cellNo2 = trim($_POST["cellNo2"]);
//if (isset($_POST["cellNo3"])) $cellNo3 = trim($_POST["cellNo3"]);
if (isset($_POST['cellNumber'])) $cellNumber = trim($_POST['cellNumber']);
//if (isset($_POST["fax1"])) $fax1 = trim($_POST["fax1"]);
//if (isset($_POST["fax2"])) $fax2 = trim($_POST["fax2"]);
//if (isset($_POST["fax3"])) $fax3 = trim($_POST["fax3"]);
if (isset($_POST['fax'])) $fax = trim($_POST['fax']);
if (isset($_POST['role'])) $role = trim($_POST['role']);
if ($_SERVER['REMOTE_ADDR'] != '76.237.197.71' && strtolower($role) == 'super') {
    $role = 'Coach';
    session_destroy();
}
if (isset($_POST['assignedToCompany'])) $LMRAEIdArray = $_POST['assignedToCompany'];

if (isset($_POST['assignedToProcessingCompany'])) $processingCompanyId = trim($_POST['assignedToProcessingCompany']);
if (isset($_POST['alternateToProcessingCompany'])) $alternatePC = trim($_POST['alternateToProcessingCompany']);
if (isset($_POST['allowToViewAllFiles'])) $allowToViewAllFiles = trim($_POST['allowToViewAllFiles']);
if (isset($_POST['serviceProvider'])) $serviceProvider = trim($_POST['serviceProvider']);
if (isset($_POST['timeZone'])) $timeZone = trim($_POST['timeZone']);
if (isset($_POST['allowExcelDownload'])) $allowExcelDownload = trim($_POST['allowExcelDownload']);
if (isset($_POST['sendMarketingEmail'])) $sendMarketingEmail = trim($_POST['sendMarketingEmail']);
if (isset($_POST['allowToListIn3PartyForm'])) $allowToListIn3PartyForm = trim($_POST['allowToListIn3PartyForm']);
if (isset($_POST['allowEmpToLogin'])) $allowEmpToLogin = trim($_POST['allowEmpToLogin']);
//if (isset($_POST["phone1"])) $phone1 = trim($_POST["phone1"]);
//if (isset($_POST["phone2"])) $phone2 = trim($_POST["phone2"]);
//if (isset($_POST["phone3"])) $phone3 = trim($_POST["phone3"]);
if (isset($_POST['phone'])) $phone = trim($_POST['phone']);
if (isset($_POST['phoneExt'])) $phoneExt = trim($_POST['phoneExt']);
if (isset($_POST['allowEmpToCreateFiles'])) $allowEmpToCreateFiles = trim($_POST['allowEmpToCreateFiles']);
if (isset($_POST['allowEmpToCreateTasks'])) $allowEmpToCreateTasks = trim($_POST['allowEmpToCreateTasks']);
if (isset($_POST['allowEmpToSeeDashboard'])) $allowEmpToSeeDashboard = trim($_POST['allowEmpToSeeDashboard']);
if (isset($_POST['accessRestriction'])) $accessRestriction = trim($_POST['accessRestriction']);
if (isset($_POST['seePrivate'])) $seePrivate = trim($_POST['seePrivate']);
if (isset($_POST['allowUserToDeleteUplodedDocs'])) $allowedToDeleteUplodedDocs = trim($_POST['allowUserToDeleteUplodedDocs']);
if (isset($_POST['allowedToEditOwnNotes'])) $allowedToEditOwnNotes = trim($_POST['allowedToEditOwnNotes']);
if (isset($_POST['seeBilling'])) $seeBilling = trim($_POST['seeBilling']);
if (isset($_POST['permissionToREST'])) $permissionToREST = trim($_POST['permissionToREST']);
if (isset($_POST['allowEmailCampaign'])) $allowEmailCampaign = trim($_POST['allowEmailCampaign']);
if (isset($_POST['allowBOToEditLMRFile'])) $allowBOToEditLMRFile = trim($_POST['allowBOToEditLMRFile']);
if (isset($_POST['changeDIYPlan'])) $changeDIYPlan = trim($_POST['changeDIYPlan']);
if (isset($_POST['allowToSendHomeownerLink'])) $allowToSendHomeownerLink = trim($_POST['allowToSendHomeownerLink']);
if (isset($_POST['allowToUpdateFileAdminSection'])) $allowToUpdateFileAdminSection = trim($_POST['allowToUpdateFileAdminSection']);
if (isset($_POST['allowToLASubmit'])) {
    $allowToLASubmit = trim($_POST['allowToLASubmit']);
}
if (isset($_POST['allowToCFPBSubmit'])) {
    $allowToCFPBSubmit = trim($_POST['allowToCFPBSubmit']);
}
if (isset($_POST['subscribeToHOME'])) $subscribeToHOME = trim($_POST['subscribeToHOME']);
if (isset($_POST['allowToSendFax'])) $allowToSendFax = trim($_POST['allowToSendFax']);
if (isset($_POST['allowEmployeeToEditCommission'])) $allowEmployeeToEditCommission = trim($_POST['allowEmployeeToEditCommission']);
if (isset($_POST['allowEmployeeToSeeCommission'])) $allowEmployeeToSeeCommission = trim($_POST['allowEmployeeToSeeCommission']);
if (isset($_POST['allowToSendFileDesignation'])) {
    $allowToSendFileDesignation = trim($_POST['allowToSendFileDesignation']);
}

if (isset($_POST['allowToChangeOrAssignBranchForFile'])) {
    $allowToChangeOrAssignBranchForFile = trim($_POST['allowToChangeOrAssignBranchForFile']);
}
if (isset($_POST['allowToLockLoanFileEmpl'])) {
    $allowToLockLoanFileEmpl = trim($_POST['allowToLockLoanFileEmpl']);
}

if (isset($_POST['allowToGetBorrowerUploadDocsNotification'])) {
    $allowToGetBorrowerUploadDocsNotification = trim($_POST['allowToGetBorrowerUploadDocsNotification']);
}

if (isset($_POST['allowToViewMarketPlace'])) {
    $allowToViewMarketPlace = trim($_POST['allowToViewMarketPlace']);
}
if (isset($_POST['shareThisFile'])) {
    $shareThisFile = trim($_POST['shareThisFile']);
}

if (isset($_POST['allowToSubmitOffer'])) {
    $allowToSubmitOffer = trim($_POST['allowToSubmitOffer']);
}
if (isset($_POST['allowToViewCreditScreening'])) {
    $allowToViewCreditScreening = trim($_POST['allowToViewCreditScreening']);
}
if (isset($_POST['userPriceEngineStatus'])) {
    $userPriceEngineStatus = trim($_POST['userPriceEngineStatus']);
}

if (isset($_POST['allowToEditLoanStage'])) {
    $infoArray['allowToEditLoanStage'] = trim($_POST['userPriceEngineStatus']);
}

if (isset($_POST['enable2FAAuthentication'])) {
    $enable2FAAuthentication = trim($_POST['enable2FAAuthentication']);
}
if (isset($_POST['TwoFAType'])) {
    $TwoFAType = trim($_POST['TwoFAType']);
}

if (isset($_POST['allowToSeeBillingSectionForFile'])) {
    $allowToSeeBillingSectionForFile = trim($_POST['allowToSeeBillingSectionForFile']);
}

if (isset($_POST['barNo'])) {
    $barNo = trim($_POST['barNo']);
}
if (isset($_POST['attorneyRate'])) {
    $attorneyRate = trim($_POST['attorneyRate']);
}
if (isset($_POST['paralegalRate'])) {
    $paralegalRate = trim($_POST['paralegalRate']);
}
if (isset($_POST['serviceFeeRate'])) {
    $serviceFeeRate = trim($_POST['serviceFeeRate']);
}

if (isset($_POST['empState'])) {
    $empState = trim($_POST['empState']);
}
if (isset($_POST['empCounty'])) {
    $empCounty = trim($_POST['empCounty']);
}
if (isset($_POST['empCity'])) {
    $empCity = trim($_POST['empCity']);
}
if (isset($_POST['empAddress'])) {
    $empAddress = trim($_POST['empAddress']);
}
if (isset($_POST['zipCode'])) {
    $zipCode = trim($_POST['zipCode']);
}
if (isset($_POST['allowEmpToSeePublicNotes'])) {
    $allowEmpToSeePublicNotes = trim($_POST['allowEmpToSeePublicNotes']);
}
if (isset($_POST['WFID'])) {
    $WFIDsArray = $_POST['WFID'];
}

if (isset($_POST['allowToViewCFPBPipeline'])) {
    $allowToViewCFPBPipeline = trim($_POST['allowToViewCFPBPipeline']);
}
if (isset($_POST['allowEmpToAccessRAM'])) {
    $allowToAccessRAM = trim($_POST['allowEmpToAccessRAM']);
}

if ($pwd == '') $pwd = $processorName;
if ($LMRAEId == '') $LMRAEId = 0;
if ($allowToViewAllFiles == '') $allowToViewAllFiles = 1;

//$tollFree = $tollFree1 . $tollFree2 . $tollFree3 . $ext;
//$fax = $fax1 . $fax2 . $fax3;
//$cellNumber = $cellNo1 . $cellNo2 . $cellNo3;

//$phone = $phone1 . $phone2 . $phone3 . $phoneExt;

$phone = str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], $phone);
$tollFree = str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], $tollFree);
$fax = str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], $fax);
$cellNumber = str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], $cellNumber);
$infoArray = ['processorId'         => $processorId,
              'email'               => $email,
              'processorName'       => $processorName,
              'processorLName'      => $processorLName,
              'pwd'                 => $pwd,
              'tollFree'            => $tollFree,
              'cellNumber'          => $cellNumber,
              'fax'                 => $fax,
              'role'                => $role,
              'LMRAEId'             => $LMRAEId,
              'userRole'            => $userRole,
              'processingCompanyId' => $processingCompanyId,
              'serviceProvider'     => $serviceProvider,
              'timeZone'            => $timeZone,
              'phone'               => $phone,
              'alternatePC'         => $alternatePC,
              'userNumber'          => PageVariables::$userNumber,
              'userGroup'           => $userGroup,
];

/** Check employee already exists while create new employee **/
if ($processorId > 0) {
} else {
    $resArray = [];
    $ip = ['email' => $email];
    $resArray = checkEmployeeExists::getReport($ip);

    if (count($resArray) > 0) {
        //           SetSess("msg", "Employee Record Already Exist.");
        header('Location:' . $_SERVER['HTTP_REFERER']);
        exit();
    }
}

if (isset($_POST['timeZone'])) $infoArray['timeZone'] = $timeZone;
if (isset($_POST['allowToViewAllFiles'])) $infoArray['allowToViewAllFiles'] = $allowToViewAllFiles;
if (isset($_POST['allowEmpToLogin'])) $infoArray['allowEmpToLogin'] = $allowEmpToLogin;
if (isset($_POST['allowExcelDownload'])) $infoArray['allowExcelDownload'] = $allowExcelDownload;
if (isset($_POST['allowEmpToCreateFiles'])) $infoArray['allowEmpToCreateFiles'] = $allowEmpToCreateFiles;
if (isset($_POST['allowEmpToCreateTasks'])) $infoArray['allowEmpToCreateTasks'] = $allowEmpToCreateTasks;
if (isset($_POST['allowEmpToSeeDashboard'])) $infoArray['allowEmpToSeeDashboard'] = $allowEmpToSeeDashboard;
if (isset($_POST['allowToListIn3PartyForm'])) $infoArray['allowToListIn3PartyForm'] = $allowToListIn3PartyForm;
if (isset($_POST['accessRestriction'])) $infoArray['accessRestriction'] = $accessRestriction;
if (isset($_POST['seePrivate'])) $infoArray['seePrivate'] = $seePrivate;
if (isset($_POST['allowUserToDeleteUplodedDocs'])) $infoArray['allowedToDeleteUplodedDocs'] = $allowedToDeleteUplodedDocs;
if (isset($_POST['allowedToEditOwnNotes'])) $infoArray['allowedToEditOwnNotes'] = $allowedToEditOwnNotes;
if (isset($_POST['seeBilling'])) $infoArray['seeBilling'] = $seeBilling;
if (isset($_POST['permissionToREST'])) $infoArray['permissionToREST'] = $permissionToREST;
if (isset($_POST['allowEmailCampaign'])) $infoArray['allowEmailCampaign'] = $allowEmailCampaign;
if (isset($_POST['allowBOToEditLMRFile'])) $infoArray['allowBOToEditLMRFile'] = $allowBOToEditLMRFile;
if (isset($_POST['changeDIYPlan'])) $infoArray['changeDIYPlan'] = $changeDIYPlan;
if (isset($_POST['allowToSendHomeownerLink'])) $infoArray['allowToSendHomeownerLink'] = $allowToSendHomeownerLink;
if (isset($_POST['allowToUpdateFileAdminSection'])) $infoArray['allowToUpdateFileAdminSection'] = $allowToUpdateFileAdminSection;
if (isset($_POST['allowToLASubmit'])) {
    $infoArray['allowToLASubmit'] = $allowToLASubmit;
}
if (isset($_POST['allowToCFPBSubmit'])) {
    $infoArray['allowToCFPBSubmit'] = $allowToCFPBSubmit;
}
if (isset($_POST['subscribeToHOME'])) $infoArray['subscribeToHOME'] = $subscribeToHOME;
if (isset($_POST['allowToSendFax'])) $infoArray['allowToSendFax'] = $allowToSendFax;

if (isset($_POST['allowEmployeeToEditCommission'])) $infoArray['allowEmployeeToEditCommission'] = $allowEmployeeToEditCommission;
if (isset($_POST['allowEmployeeToSeeCommission'])) $infoArray['allowEmployeeToSeeCommission'] = $allowEmployeeToSeeCommission;
if (isset($_POST['allowToSendFileDesignation'])) {
    $infoArray['allowToSendFileDesignation'] = $allowToSendFileDesignation;
}

if (isset($_POST['barNo'])) {
    $infoArray['barNo'] = $barNo;
}
if (isset($_POST['allowEmpToSeePublicNotes'])) {
    $infoArray['allowEmpToSeePublicNotes'] = $allowEmpToSeePublicNotes;
}
if (isset($_POST['attorneyRate'])) {
    $infoArray['attorneyRate'] = $attorneyRate;
}
if (isset($_POST['paralegalRate'])) {
    $infoArray['paralegalRate'] = $paralegalRate;
}
if (isset($_POST['serviceFeeRate'])) {
    $infoArray['serviceFeeRate'] = $serviceFeeRate;
}

if (isset($_POST['empState'])) {
    $infoArray['empState'] = $empState;
}
if (isset($_POST['empCounty'])) {
    $infoArray['empCounty'] = $empCounty;
}
if (isset($_POST['empCity'])) {
    $infoArray['empCity'] = $empCity;
}
if (isset($_POST['empAddress'])) {
    $infoArray['empAddress'] = $empAddress;
}
if (isset($_POST['zipCode'])) {
    $infoArray['zipCode'] = $zipCode;
}

if (isset($_POST['allowToChangeOrAssignBranchForFile'])) {
    $infoArray['allowToChangeOrAssignBranchForFile'] = $allowToChangeOrAssignBranchForFile;
}
if (isset($_POST['allowToLockLoanFileEmpl'])) {
    $infoArray['allowToLockLoanFileEmpl'] = $allowToLockLoanFileEmpl;
}

if (isset($_POST['allowToGetBorrowerUploadDocsNotification'])) {
    $infoArray['allowToGetBorrowerUploadDocsNotification'] = $allowToGetBorrowerUploadDocsNotification;
}

if (isset($_POST['allowToViewMarketPlace'])) {
    $infoArray['allowToViewMarketPlace'] = $allowToViewMarketPlace;
}

if (isset($_POST['shareThisFile'])) {
    $infoArray['shareThisFile'] = $shareThisFile;
}

if (isset($_POST['allowToSubmitOffer'])) {
    $infoArray['allowToSubmitOffer'] = $allowToSubmitOffer;
}

if (isset($_POST['allowToViewCreditScreening'])) {
    $infoArray['allowToViewCreditScreening'] = $allowToViewCreditScreening;
}
if (isset($_POST['userPriceEngineStatus'])) {
    $infoArray['userPriceEngineStatus'] = $userPriceEngineStatus;
}

if (isset($_POST['enable2FAAuthentication'])) {
    $infoArray['enable2FAAuthentication'] = $enable2FAAuthentication;
}
if (isset($_POST['TwoFAType'])) {
    $infoArray['TwoFAType'] = $TwoFAType;
}
if (isset($_POST['allowToViewContactsList'])) {
    $infoArray['allowToViewContactsList'] = $allowToViewContactsList;
}

if (isset($_POST['allowToSeeBillingSectionForFile'])) {
    $infoArray['allowToSeeBillingSectionForFile'] = $allowToSeeBillingSectionForFile;
}

if (isset($_POST['allowToViewCFPBPipeline'])) {
    $infoArray['allowToViewCFPBPipeline'] = $allowToViewCFPBPipeline;
}
if (isset($_POST['allowEmpToAccessRAM'])) {
    $infoArray['allowToAccessRAM'] = $allowToAccessRAM;
}

if (isset($_POST['allowEmpToCreateBranch'])) $infoArray['allowEmpToCreateBranch'] = $_POST['allowEmpToCreateBranch']; // | Allow to Create Employee to Create Branch.
if (isset($_POST['allowEmpToCreateAloware'])) $infoArray['allowEmpToCreateAloware'] = $_POST['allowEmpToCreateAloware']; // | Allow to Create Employee to Create Branch.
if (isset($_POST['convertNewBRIntoEmpOwnBR'])) $infoArray['convertNewBRIntoEmpOwnBR'] = $_POST['convertNewBRIntoEmpOwnBR']; // | Allow to Create Employee to Create Branch.
if (isset($_POST['allowEmpToCreateAgent'])) $infoArray['allowEmpToCreateAgent'] = $_POST['allowEmpToCreateAgent']; // | Allow to Create Employee to Create Agent.
if (isset($_POST['allowEmpToSeeAgent'])) $infoArray['allowEmpToSeeAgent'] = $_POST['allowEmpToSeeAgent']; // | Allow to Create Employee to Create Agent.
if (isset($_POST['emailaloware'])) $infoArray['emailaloware'] = $_POST['emailaloware'];
if (isset($_POST['allowEmpToCopyFile'])) $infoArray['allowEmpToCopyFile'] = $_POST['allowEmpToCopyFile'];

if (isset($_POST['notifyBODocUpload'])) {
    $notifyBODocUpload = $_POST['notifyBODocUpload'];
    $infoArray['notifyBODocUpload'] = $notifyBODocUpload;
}
if (isset($_POST['notifyBranchDocUpload'])) {
    $notifyBranchDocUpload = $_POST['notifyBranchDocUpload'];
    $infoArray['notifyBranchDocUpload'] = $notifyBranchDocUpload;
}
if (isset($_POST['notifyLODocUpload'])) {
    $notifyLODocUpload = $_POST['notifyLODocUpload'];
    $infoArray['notifyLODocUpload'] = $notifyLODocUpload;
}
if (isset($_POST['notifyBrokerDocUpload'])) {
    $notifyBrokerDocUpload = $_POST['notifyBrokerDocUpload'];
    $infoArray['notifyBrokerDocUpload'] = $notifyBrokerDocUpload;
}
if (isset($_POST['notifyDocUploadRequest'])) {
    $notifyDocUploadRequest = $_POST['notifyDocUploadRequest'];
    $infoArray['notifyDocUploadRequest'] = $notifyDocUploadRequest;
}
if (isset($_POST['notifyNewFileCreated'])) {
    $notifyNewFileCreated = $_POST['notifyNewFileCreated'];
    $infoArray['notifyNewFileCreated'] = $notifyNewFileCreated;
}
if (isset($_POST['loanpassLogin'])) {
    $loanpassLogin = $_POST['loanpassLogin'];
    $infoArray['loanpassLogin'] = $loanpassLogin;
}
if (isset($_POST['loanpassPassword'])) {
    $loanpassPassword = $_POST['loanpassPassword'];
    $infoArray['loanpassPassword'] = $loanpassPassword;
}
if (isset($_POST['allowUserToSendMsgToBorrower'])) {
    $infoArray['allowUserToSendMsgToBorrower'] = $_POST['allowUserToSendMsgToBorrower'];
}

$allowToViewAutomationPopup = $_POST['allowToViewAutomationPopup'] ?? 0;
$allowToControlAutomationPopup = $_POST['allowToControlAutomationPopup'] ?? 0;

$infoArray['allowToViewAutomationPopup'] = $allowToViewAutomationPopup;
$infoArray['allowToControlAutomationPopup'] = $allowToControlAutomationPopup;
$infoArray['allowEmpToSeeAllEmails'] = Request::GetClean('allowEmpToSeeAllEmails') ?? 0;
$infoArray['allowToMassUpdate'] = intval(Request::GetClean('allowToMassUpdate')) ?? 0;
$infoArray['allowToManageDraws'] = intval(Request::GetClean('allowToManageDraws')) ?? 0;

if (isset($_REQUEST['allowToManageDraws']) && (int)$processorId === 0 && ($userRole == 'Super' || $userRole == 'Manager')) {
    $infoArray['allowToManageDraws'] = 1;
}

/*
* Allow Secondary WF feature for the PCs =
* Dave PC, Enrollment Advisory, Law offices
*/
if (($userRole == 'Manager' || $userRole == 'Administrator' || $userRole == 'Super') && (in_array($processingCompanyId, $accessSecondaryWFPC))) {
    $infoArray['WFIDs'] = $WFIDsArray;
}

if(isset($_REQUEST['allowMERS'])) {
    $infoArray['allowMERS'] = $_REQUEST['allowMERS'];
}

if(isset($_REQUEST['manualMERS'])) {
    $infoArray['manualMERS'] = $_REQUEST['manualMERS'];
}
if(isset($_REQUEST['allowAllBackofficeUsersForFromEmail'])) {
     $infoArray['allowAllBackofficeUsersForFromEmail'] = $_REQUEST['allowAllBackofficeUsersForFromEmail'];
}
if(isset($_REQUEST['backOfficersForFromEmail'])) {
     $infoArray['backOfficersForFromEmail'] = $_REQUEST['backOfficersForFromEmail'];
}
if(isset($_REQUEST['customEmailForEmail'])) {
     $infoArray['customEmailForEmail'] = $_REQUEST['customEmailForEmail'];
}
if(isset($_REQUEST['allowBranchEmailAsFromEmail'])) {
     $infoArray['allowBranchEmailAsFromEmail'] = $_REQUEST['allowBranchEmailAsFromEmail'];
}
if(isset($_REQUEST['allowBrokerEmailAsFromEmail'])) {
     $infoArray['allowBrokerEmailAsFromEmail'] = $_REQUEST['allowBrokerEmailAsFromEmail'];
}
if(isset($_REQUEST['allowLoanofficerEmailAsFromEmail'])) {
     $infoArray['allowLoanofficerEmailAsFromEmail'] = $_REQUEST['allowLoanofficerEmailAsFromEmail'];
}

$resultArray = saveEmployeeInfo::getReport($infoArray);


if (count($resultArray) > 0) {
    $updateCount = trim($resultArray['updateCount']);
    $insertCount = trim($resultArray['insertCount']);
    $newProcessorId = trim($resultArray['newProcessorId']);
    if ($updateCount > 0) {
        // SetSess("msg", "Updated Successfully.");
        $responseAjax = ['code' => '100', 'msg' => 'Updated Successfully.'];
    } else if ($newProcessorId > 0) {
        $processorId = $newProcessorId;
        //SetSess("msg", "Inserted Successfully.");
        $responseAjax = ['code' => '100', 'msg' => 'Inserted Successfully.'];
    } else {
        //SetSess("msg", "Error while Updating");
        $responseAjax = ['code' => '101', 'msg' => 'Error while Updating.'];
    }
}
$pcAcqualifyDetails = getacqualifyPCDetails::getReport(['pcid' => $processingCompanyId]);

if ($allowToViewCreditScreening == 1 && count($pcAcqualifyDetails) > 0 && ($userRole == 'Super' || $userRole == 'Manager')) {

    $EmplAcqualifyArray = [];
    $EmplAcqualifyArray = ['lwUserId' => $processorId, 'userRole' => 'Employee'];
    $EmplAcqualifyDetails = getacqualifyUserDetails::getReport($EmplAcqualifyArray);

    $acAccountId = $pcAcqualifyDetails[0]['accountId'];
    $acqualifyArray['apiToken'] = '4J2Qn9R0Kp6XenAOj5nUBed55Gsy9Ais';

    $acQualifyEmail = $_REQUEST['oldEmail'] ?? $email;
    if (count($EmplAcqualifyDetails) > 0) {
        $acqualifyArray['data'] = ['name' => Strings::escapeQuoteNew($processorName) . ' ' . Strings::escapeQuoteNew($processorLName), 'notificationEmail' => $acQualifyEmail, 'username' => $processorId . '+' . $acQualifyEmail];
        $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/update-user';
        $acUserId = $EmplAcqualifyDetails[0]['userId'];

        $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/assign-user-to-account';

        if (count($LMRAEIdArray) > 0) {
            $BRANCHAcqualifyArray = ['executiveId' => implode(',', $LMRAEIdArray)];
        } else {
            $BRANCHAcqualifyArray = ['executiveId' => $_REQUEST['allBranchList']];
        }
        $BRANCHAcqualifyArray = getacqualifyBranchDetails::getReport($BRANCHAcqualifyArray);
        foreach ($BRANCHAcqualifyArray as $BRANCHAcqualifyVal) {
            $BRANCHAcqualifyUserId[] = $BRANCHAcqualifyVal['userId'];
        }

        $acqualifyArray['data'] = ['userId' => $acUserId, 'accountId' => $acAccountId, 'role' => 'manager', 'branchIds' => [implode(',', $BRANCHAcqualifyUserId)]];

        $acQualifyResponse = assignUserToAccount::getReport($acqualifyArray);
        $acQualifyResponse = json_decode($acQualifyResponse);

        if ($acQualifyResponse->status == 'success') {
            if (isset($acQualifyResponse->roleInfo)) {

                $qry = "update acqualifyUsers set accountId = '" . $acQualifyResponse->roleInfo->accountId . "', role = '" . $acQualifyResponse->roleInfo->role . "',branchIds = '" . implode(',', $acQualifyResponse->roleInfo->branchIds) . "'  where  userId ='" . $acUserId . "'  and userRole ='Employee'";

                $acqualifyInsertId = Database2::getInstance()->update($qry);
            }
            $acUserId = $acQualifyResponse->user->id;
        }

        $acqualifyPostArray['url'] = $acqualifyArray['url'];
        $acqualifyPostArray['request'] = $acqualifyArray;
        $acqualifyPostArray['response'] = $acQualifyResponse;
        $acqualifyPostArray['pcid'] = $processingCompanyId;
        postacQualifylog::getReport($acqualifyPostArray);


    } else {
        $acqualifyArray['data'] = ['name' => Strings::escapeQuoteNew($processorName) . ' ' . Strings::escapeQuoteNew($processorLName), 'notificationEmail' => $acQualifyEmail, 'username' => $processorId . '+' . $acQualifyEmail];
        $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/create-user';
        $acQualifyResponse = createUser::getReport($acqualifyArray);
        $acQualifyResponse = json_decode($acQualifyResponse);

        if ($acQualifyResponse->status == 'success') {
            if (isset($acQualifyResponse->user)) {
                $qry = "insert into acqualifyUsers (lwUserId,`name`,email,loginKey,userId,createdDate,userRole) values ('" . $processorId . "','" . $acQualifyResponse->user->name . "','" . $acQualifyResponse->user->email . "','" . $acQualifyResponse->user->loginKey . "','" . $acQualifyResponse->user->id . "','" . Dates::Timestamp() . "','Employee');";
                $acqualifyInsertId = Database2::getInstance()->insert($qry);
            }
            $acUserId = $acQualifyResponse->user->id;
        }
    }
    $acqualifyPostArray['url'] = $acqualifyArray['url'];
    $acqualifyPostArray['request'] = $acqualifyArray;
    $acqualifyPostArray['response'] = $acQualifyResponse;
    $acqualifyPostArray['pcid'] = $processingCompanyId;
    postacQualifylog::getReport($acqualifyPostArray);

    if ($acUserId > 0) {
        $BRANCHAcqualifyUserId = [];
        $BRANCHAcqualifyArray = [];
        if (count($LMRAEIdArray) > 0) {
            $BRANCHAcqualifyArray = ['executiveId' => implode(',', $LMRAEIdArray)];
            $BRANCHAcqualifyArray = getacqualifyBranchDetails::getReport($BRANCHAcqualifyArray);
            foreach ($BRANCHAcqualifyArray as $BRANCHAcqualifyVal) {
                $BRANCHAcqualifyUserId[] = $BRANCHAcqualifyVal['userId'];
            }
            $acqualifyArray['data'] = ['userId' => $acUserId, 'accountId' => $acAccountId, 'role' => 'manager', 'branchIds' => [implode(',', $BRANCHAcqualifyUserId)]];
        } else {

            $BRANCHAcqualifyArray = ['executiveId' => $_REQUEST['allBranchList']];
            $BRANCHAcqualifyArray = getacqualifyBranchDetails::getReport($BRANCHAcqualifyArray);
            foreach ($BRANCHAcqualifyArray as $BRANCHAcqualifyVal) {
                $BRANCHAcqualifyUserId[] = $BRANCHAcqualifyVal['userId'];
            }
            $acqualifyArray['data'] = ['userId' => $acUserId, 'accountId' => $acAccountId, 'role' => 'manager', 'branchIds' => [implode(',', $BRANCHAcqualifyUserId)]];
        }

        $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/assign-user-to-account';

        $acQualifyResponse = assignUserToAccount::getReport($acqualifyArray);
        $acQualifyResponse = json_decode($acQualifyResponse);

        if ($acQualifyResponse->status == 'success') {
            if (isset($acQualifyResponse->roleInfo)) {

                $qry = "update acqualifyUsers set accountId = '" . $acQualifyResponse->roleInfo->accountId . "', role = '" . $acQualifyResponse->roleInfo->role . "',branchIds = '" . implode(',', $acQualifyResponse->roleInfo->branchIds) . "'  where  userId ='" . $acUserId . "'  and userRole ='Employee'";

                $acqualifyInsertId = Database2::getInstance()->update($qry);
            }
            $acUserId = $acQualifyResponse->user->id;
        }
        $acqualifyPostArray['url'] = $acqualifyArray['url'];
        $acqualifyPostArray['request'] = $acqualifyArray;
        $acqualifyPostArray['response'] = $acQualifyResponse;
        $acqualifyPostArray['pcid'] = $processingCompanyId;
        postacQualifylog::getReport($acqualifyPostArray);
    }

}

/**
 * Card # :530
 * Description      : Aloware integration
 * Functionality    : Employee creation process.
 */
if ($_POST['allowEmpToCreateAloware'] == 1 && $cellNumber > 0) {
    $aloware = new alowareApi();
    $resuser = $aloware->getUserAlowareInfo(['aid' => $processorId]);

    if (empty($resuser)) {
        $request['internal_company_id'] = $processingCompanyId;
        $request['company_name'] = $alternatePC;
        $request['company_country'] = 'US';
        $request['internal_user_id'] = $processorId;
        $request['user_name'] = $_POST['processorName'];
        $request['user_phone_number'] = $cellNumber;
        $request['user_email'] = $_POST['emailaloware'];
        $request['user_timezone'] = Arrays::getArrayValue($PCTimeZone, $globalWithUSTimeZones);
        $creteUserJson = $aloware->creteUser($request);

        $creteUserJsonDecrypt = json_decode($creteUserJson, true);
        $alowareInfo['aid'] = $processorId;
        $alowareInfo['api_token'] = $creteUserJsonDecrypt['api_token'];
        if (Arrays::getArrayValue('api_token', $creteUserJsonDecrypt) != '') {
            $alowareinfo = $aloware->saveUserAloware($alowareInfo);
        } else {
            $tblAdminUsers = tblAdminUsers::Get([tblAdminUsers_db::COLUMN_AID => $processorId]);
            $tblAdminUsers->allowEmpToCreateAloware = 0;
            $tblAdminUsers->updatedBy = PageVariables::$userNumber ?? 0;
            $tblAdminUsers->updatedAt = Dates::Timestamp();
            $tblAdminUsers->save();
        }
        if (isset($creteUserJsonDecrypt['errors'])) {
            foreach ($creteUserJsonDecrypt['errors'] as $errKey => $errValue) {
                $alowareErr .= $errValue[0] . '<br>';
            }
        }
    }
}
/* Aloware end. */

if ($role == 'Attorney') {

    $maxBarNo = 1;
    if (isset($_POST['maxBarNo'])) {
        $maxBarNo = $_POST['maxBarNo'];
    }
    if ($newProcessorId > 0) {
        saveAttorneyBarInfo::getReport(['empId' => $newProcessorId, 'maxBarNo' => $maxBarNo, 'p' => $_POST]);
    } else {
        saveAttorneyBarInfo::getReport(['empId' => $processorId, 'maxBarNo' => $maxBarNo, 'p' => $_POST]);
    }
}

if ($newProcessorId > 0) {

    if ($userRole == 'Manager' && $role == 'Manager') {

        $resultArray = [];
        $PCAllowToCreate = 1;
        $pcInfo = tblProcessingCompany::Get(['PCID' => $processingCompanyId]);
        $resultArray = doesPCExceedsNoOfUsers::getReport(['PCID' => $processingCompanyId, 'planType' => $pcInfo->planType]);
        if (count($resultArray) > 0) $PCAllowToCreate = $resultArray['PCAllowToCreate'];
        $infoArray = ['existingEmpId'   => PageVariables::$userNumber,
                      'newEmpId'        => $newProcessorId,
                      'PCAllowToCreate' => $PCAllowToCreate,

        ];
//          $oEmployee->updateMgrEmployeeRestriction($infoArray);

    }
}

if (($updateCount > 0 || $newProcessorId > 0) && ($userRole == 'Super' || $userRole == 'Manager')) {
    $inputArray['LMRAEIdArray'] = $LMRAEIdArray;
    if ($newProcessorId > 0) {
        $inputArray['processorId'] = $newProcessorId;
    } else {
        $inputArray['processorId'] = $processorId;
    }
    updateLMRAEIDToEmp::getReport($inputArray);
}


if ($processorId && $_FILES['employeeAvatar']['type']
    && in_array($_FILES['employeeAvatar']['type'], glMimeTypes::$imageTypes)
    && $_FILES['employeeAvatar']['size']) {
    $employeeAvatar = '/avatar/employee/' . $processorId . '.' . pathinfo($_FILES['employeeAvatar']['name'], PATHINFO_EXTENSION);
    FileStorage::moveFile($_FILES['employeeAvatar']['tmp_name'], $employeeAvatar);
    avatar::updateEmployee($processorId, $employeeAvatar);
}

if ($alowareErr != '') {
    echo $alowareErr;
    exit;
}//SetSess("msg", $alowareErr); // Aloware Error messages.
echo json_encode($responseAjax);
exit();


