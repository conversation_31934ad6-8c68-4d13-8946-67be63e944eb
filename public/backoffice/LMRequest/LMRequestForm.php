<?php


global $publicUser, $fileCT, $fieldsInfo, $fileTab, $fileMC, $glLMRId, $fileLP, $fileCLP, $file_id;
global $branchReferralCode, $agentReferralCode, $LMRId, $fOpt, $activeTab, $executiveId, $LMRResponseId, $PCID, $oSubstatus,
       $myFileInfo, $fileSubstatusInfo, $userRole, $allowCFPBAuditing, $allowUserToUpdateCFPBFile,
       $borrowerName, $isHMLO, $isMF, $isSLM,
       $isHOALien, $processorCommentsArray, $userTimeZone, $showSysGenNote,
       $viewPrivateNotes, $viewPublicNotes, $userGroup, $permissionsToEdit,
       $allowToUpdateFileAdminSection, $op;
global $shareThisFile, $addedToFav, $fileTabs, $isSLMOnly, $allowPCUserToSubmitCFPB,
       $isEF, $allowToAccessRAM, $moduleRequested;
global $servicesRequested, $isLM, $showSSTab, $isLO, $isLSR, $allowToEdit, $allowToViewMarketPlace, $subscribePCToHOME,
       $subscribeToHOME, $allowToSeeAlowareTab, $viewSubmitOfferTab, $pcPriceEngineStatus, $userPriceEngineStatus, $permissionToREST;
global $editOpt, $copyOpt, $allowToChangeOrAssignBranchForFile, $oldFPCID, $isSysNotesPrivate,
       $allowClientToCreateHMLOFile, $acqualifyPCAccountId, $acqualifyPCMinCreditScore, $acqualifyBranchId, $applicantUserid, $defaultBranchId, $debugSubPage, $confirmType, $allowToupdateFileAndClient;

//use models\composite\oBroker\checkBrokerIsLoanOfficer;
use models\composite\oClient\getClientInfo;
use models\composite\oEmail\alertLoanProgramNull;
use models\composite\oFile\getFileInfo;
use models\composite\oPC\getAppFormFields;
use models\composite\oSubstatus\getPCFileSubstatus;
use models\composite\oUser\getUserInfo;
use models\composite\oUserAccess\getAutomationControlAccess;
use models\constants\accessIntakeFileTabPC;
use models\constants\accessNewIntakeFileTabPC;
use models\constants\fileTabIcons;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glFileTabs;
use models\constants\gl\glFUModulesNotesTypeArray;
use models\constants\gl\glNotesTypeArray;
use models\constants\gl\glNotShowCFPBTabForClientLogin;
use models\constants\gl\glPCID;
use models\constants\gl\glShowALTabForClientLogin;
use models\constants\gl\glUserGroup;
use models\constants\GpropertyTypeNumbArray;
use models\constants\showAssignedEmployeeToPC;
use models\constants\ShowSaleDate;
use models\constants\showTotalOwedAndOutstandingBalance;
use models\Controllers\backoffice\LMRequest;
use models\Controllers\LMRequest\Property;
use models\Controllers\loanForm;
use models\Controllers\backoffice\LoanStagesController;
use models\Database2;
use models\cypher;
use models\lendingwise\tblFileHMLO;
use models\lendingwise\tblFileHMLOExperience;
use models\PageVariables;
use models\Request;
use models\standard\BaseHTML;
use models\standard\Currency;
use models\standard\Dates;
use models\standard\Strings;

$ShowSaleDate = ShowSaleDate::$ShowSaleDate;
$accessNewIntakeFileTabPC = accessNewIntakeFileTabPC::$accessNewIntakeFileTabPC;
$glShowALTabForClientLogin = glShowALTabForClientLogin::$glShowALTabForClientLogin;
$showTotalOwedAndOutstandingBalance = showTotalOwedAndOutstandingBalance::$showTotalOwedAndOutstandingBalance;
$showAssignedEmployeeToPC = showAssignedEmployeeToPC::$showAssignedEmployeeToPC;
$accessIntakeFileTabPC = accessIntakeFileTabPC::$accessIntakeFileTabPC;
$glFUModulesNotesTypeArray = glFUModulesNotesTypeArray::$glFUModulesNotesTypeArray;
$fileTabIcons = fileTabIcons::$fileTabIcons;
$glNotShowCFPBTabForClientLogin = glNotShowCFPBTabForClientLogin::$glNotShowCFPBTabForClientLogin;
$glNotesTypeArray = glNotesTypeArray::$glNotesTypeArray;

LMRequest::setLMRId($LMRId);
LMRequest::$PCID = $PCID;
LMRequest::$allowToEdit = $allowToEdit;
LMRequest::$activeTab = $activeTab;

$BorInfoMO = '';
$newHref = '';
$propAddrInfo = '';
$propAddressInfo = '';
$agentNumber = 0;
$showAgentList = 0;
$webFormFOpt = '';
$emailOpt = '';
$borOrigEmail = '';
$propertyAddress = '';
$propertyCity = '';
$propertyCountry = '';
$propertyUnit = '';
$propertyState = '';
$propertyZip = '';
$isCoBorrower = 0;
$ssnNumber = '';
$tempSSNNumber = '';
$borrowerDOB = '';
$borrowerPOB = '';
$midFicoScore = '';
$occupancy = '';
$webformName = '';
$loanNumber = '';
$loanNumber2 = '';
$primaryStatusId = 0;
$primaryStatus = '';
$fileSubStatus = '';
$salesDate = '';
$PCSubStatusInfo = [];
$tabIndexNumb = 0;
$incomeInfo = [];
$submitterEmail = '';
$submitterPhone = '';
$submittedBy = '';
$submitterPCName = '';
$submitterPCPhone = '';
$submitterCell = '';
$billingPaymentInfoArray = [];
$billingFeeInfoArray = [];
$closingDate = '';
$tempLMRClientTypeInfo = $fileHMLOPropertyInfo = [];
$tempServicesRequested = [];
$hideBorrowerInfo = '';
$fileCT = $fileCLP = '';
$filRentRollInfo = [];

$PCPermissonToREST = 0;
$branchApprovedByREST = 0;
$branchSubscribedToREST = 0;
$subscribeToREST = 0;
$cemail = '';
$mortgageNotes = '';
$tempLMRClientServiceType = '';
$MFLoanTermsInfo = [];
$CFPBInfo = $resultArr = [];
$isClientProfile = 0;
$registerDate = '';
$selClientId = $clientId = '';
$PCClientWFServiceTypeArray = $clientInfoArray = [];
$PCquickAppFieldsInfo = [];
$clId = '';
$fileMC = $fileLP = [];

$phoneNumber = '';
$cellNumber = '';
$entityName = '';
$fileTypesTxt = '';


if (isset($_POST['publicUser'])) $publicUser = Request::GetClean('publicUser');
if (isset($_REQUEST['cemail'])) $cemail = cypher::myDecryption(Request::GetClean('cemail'));
if (isset($_REQUEST['clId'])) $clId = cypher::myDecryption(Request::GetClean('clId'));

if ($publicUser == 1) {
    $myUrl = CONST_SITE_URL . 'loanModificationPrequalRemote.php?rsc=' . $branchReferralCode . '&aRc=' . cypher::myEncryption($agentReferralCode) . '&lId=' . cypher::myEncryption($LMRId) . '&fOpt=' . cypher::myEncryption($fOpt) . '&tabOpt=' . $activeTab;
} else {
    $myUrl = 'LMRequest.php?eId=' . cypher::myEncryption($executiveId) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;rId=' . cypher::myEncryption($LMRResponseId) . '&amp;tabOpt=' . $activeTab;
}
Strings::SetSess('gotoUrl', $myUrl);
/**
 * Desc : Get Substatus Info For new loan file. - Pivotal - #154749877
 */
if (($activeTab == 'LI' || $activeTab == 'QAPP' || $activeTab == 'CI') && $LMRId == 0) {
    $inArray['PCID'] = $PCID;
    $inArray['opt1'] = 'list';
    $inArray['opt2'] = 'Y';

    $result = getPCFileSubstatus::getReport($inArray);
    if (count($result) > 0) {
        $PCSubStatusInfo = $result['substatusInfo'];
    }
}

/* Check if this is being used anywhere - Kavya / Sekar 2/11/2018 */

if (($activeTab == 'LI' || $activeTab == 'QAPP') && $LMRId == 0) {
    $inArray['CID'] = $clId;
    $inArray['opt1'] = 'list';
    $inArray['opt2'] = 'Y';

    $resultArr = getClientInfo::getReport($inArray);

    if (count($resultArr) > 0) {
        $clientInfoArray = $resultArr['clientInfo'];
    }
}
$file_id = $LMRId;
$LMRInternalLoanPrograms = null;

if (count($myFileInfo) > 0) {

    // $myFileInfo -- LMRClientTypeInfo -- LMRInternalLoanprograms

    $LMRInternalLoanPrograms = $myFileInfo['LMRInternalLoanprograms'];

    if (array_key_exists('LMRInfo', $myFileInfo)) {
        $borOrigEmail = $myFileInfo['LMRInfo']['borrowerEmail'];
        $phoneNumber = $myFileInfo['LMRInfo']['phoneNumber'];
        $cellNumber = $myFileInfo['LMRInfo']['cellNumber'];
    }

    if (array_key_exists('fileHMLOEntityInfo', $myFileInfo)) $entityName = $myFileInfo['fileHMLOEntityInfo']['entityName'];
    if (array_key_exists('fileHMLOEntityInfo', $myFileInfo)) $entityType = $myFileInfo['fileHMLOEntityInfo']['entityType'];
    if (array_key_exists('PCStatusInfo', $myFileInfo)) $PCStatusInfo = $myFileInfo['PCStatusInfo'];
    /** Fetch all PC status **/
    if (array_key_exists('PCSubStatusInfo', $myFileInfo)) $PCSubStatusInfo = $myFileInfo['PCSubStatusInfo'];
    /** Fetch all PC sub-status **/

    if (array_key_exists('BillingPaymentInfo', $myFileInfo)) $billingPaymentInfoArray = $myFileInfo['BillingPaymentInfo'];
    if (array_key_exists('BillingFeeInfo', $myFileInfo)) $billingFeeInfoArray = $myFileInfo['BillingFeeInfo'];
    if (array_key_exists('LMRClientTypeInfo', $myFileInfo)) {
        if (array_key_exists($LMRId, $myFileInfo['LMRClientTypeInfo'])) $tempLMRClientTypeInfo = $myFileInfo['LMRClientTypeInfo'][$LMRId];
    }
    /** Fetch file services **/
    if (array_key_exists('branchClientTypeInfo', $myFileInfo)) $tempServicesRequested = $myFileInfo['branchClientTypeInfo'];
    /** Fetch all Branch services requested **/
    if (array_key_exists('MFLoanTermsInfo', $myFileInfo)) $MFLoanTermsInfo = $myFileInfo['MFLoanTermsInfo'];
    if (array_key_exists('CFPBInfo', $myFileInfo)) $CFPBInfo = $myFileInfo['CFPBInfo'];
    if (array_key_exists('PCClientWFServiceType', $myFileInfo)) $PCClientWFServiceTypeArray = $myFileInfo['PCClientWFServiceType'];
    if (array_key_exists('fileHMLOPropertyInfo', $myFileInfo)) $fileHMLOPropertyInfo = $myFileInfo['fileHMLOPropertyInfo'];
    if (array_key_exists('fileModuleInfo', $myFileInfo)) {
        if (array_key_exists($LMRId, $myFileInfo['fileModuleInfo'])) $fileModuleInfo = $myFileInfo['fileModuleInfo'][$LMRId];
    }
    if (array_key_exists('LMRClientTypeInfo', $myFileInfo)) {
        if (array_key_exists($LMRId, $myFileInfo['LMRClientTypeInfo'])) $LMRClientTypeInfo = $myFileInfo['LMRClientTypeInfo'][$LMRId];
    }
    if (array_key_exists('filRentRollInfo', $myFileInfo)) $filRentRollInfo = $myFileInfo['filRentRollInfo'];
    $y = 0;
    for ($j = 0; $j < count($tempServicesRequested); $j++) {
        $tempLMRClientTypeCode = '';
        $tempLMRClientTypeCode = trim($tempServicesRequested[$j]['LMRClientType']);
        for ($x = 0; $x < count($tempLMRClientTypeInfo); $x++) {
            if ($tempLMRClientTypeCode == trim($tempLMRClientTypeInfo[$x]['ClientType'])) {
                if ($y > 0) {
                    $tempLMRClientServiceType .= ', ' . trim($tempServicesRequested[$j]['serviceType']);
                } else {
                    $tempLMRClientServiceType .= trim($tempServicesRequested[$j]['serviceType']);
                }
                $y++;
            }
        }
    }
    $fileModuleCodeForWebForm = '';
    $fileModuleCodeForWebForm = $fileModuleInfo[0]['moduleCode'];

    $MFLoanTermsInfoCnt = 0;
    $MFLoanTermsInfoCnt = count($MFLoanTermsInfo);
    $noOfApprovedStatusCnt = 0;
    $totalApprovedLoanAmt = 0;
    for ($i = 0; $i < count($MFLoanTermsInfo); $i++) {

        $approvedLoanAmt = 0;
        $applnStatus = '';
        $approvedLoanAmt = $MFLoanTermsInfo[$i]['approvedLoanAmt'];
        $applnStatus = $MFLoanTermsInfo[$i]['applnStatus'];

        if ($applnStatus == 'Approved') {
            $totalApprovedLoanAmt += $approvedLoanAmt;
            $noOfApprovedStatusCnt++;
        }
    }

    $totalApprovedLoanAmt = Currency::formatDollarAmountWithDecimal($totalApprovedLoanAmt);

    $brokerNumber = Strings::showField('brokerNumber', 'LMRInfo');
    if (isset($myFileInfo['BrokerInfo'])) {
        if (trim($myFileInfo['BrokerInfo']['email'] ?? '') == trim($PCID) . '@dummyAgentemail.com') {
            $dummyBrokerId = $myFileInfo['BrokerInfo']['userNumber'];
        }
    }

    $secondaryBrokerNumber = Strings::showField('secondaryBrokerNumber', 'LMRInfo');
    $clientId = Strings::showField('clientId', 'LMRInfo');
    $propertyAddress = Property::$primaryPropertyInfo->propertyAddress;
    $propertyCity = Property::$primaryPropertyInfo->propertyCity;
    $propertyCountry = Property::$primaryPropertyInfo->propertyCountry;
    $propertyUnit = Property::$primaryPropertyInfo->propertyUnit;
    $propertyState = Property::$primaryPropertyInfo->propertyState;
    $propertyZip = Property::$primaryPropertyInfo->propertyZipCode;
    $propertyCounty = Property::$primaryPropertyInfo->propertyCounty;
    $propertyType = Property::$primaryPropertyInfo->getTblPropertiesCharacteristics_by_propertyId()->propertyType;

    $ssnNumber = Strings::showField('ssnNumber', 'LMRInfo');
    $borrowerDOB = Strings::showField('borrowerDOB', 'LMRInfo');
    $borrowerPOB = Strings::showField('borrowerPOB', 'LMRInfo');
    $midFicoScore = Strings::showField('midFicoScore', 'fileHMLOInfo');

    $occupancy = Strings::showField('occupancy', 'LMRInfo');
    $loanNumber = Strings::showField('loanNumber', 'LMRInfo');
    $loanNumber2 = Strings::showField('loanNumber2', 'LMRInfo');
    $primaryStatusId = Strings::showField('primeStatusId', 'ResponseInfo');
    $servicer1 = Strings::showField('servicer1', 'LMRInfo');
    $servicer2 = Strings::showField('servicer2', 'LMRInfo');
    $closingDate = Strings::showField('closingDate', 'QAInfo');
    $registerDate = Strings::showField('registerDate', 'clientInfo');
    $selClientId = Strings::showField('clientId', 'LMRInfo');

    $salesDate = Strings::showField('salesDate', 'LMRInfo');
    $mortgageNotes = Strings::showField('mortgageNotes', 'LMRInfo');
    $lien1Rate = Strings::showField('lien1Rate', 'LMRInfo');
    $loanAmount = Currency::formatDollarAmountWithDecimal(Strings::showField('totalLoanAmount', 'fileHMLONewLoanInfo'));

    if (Dates::IsEmpty($salesDate)) {
        $salesDate = '';
    } else {
        $salesDate = Dates::formatDateWithRE($salesDate, 'YMD', 'm/d/Y');
    }
    if (Dates::IsEmpty($closingDate)) {
        $closingDate = '';
    } else {
        $closingDate = Dates::formatDateWithRE($closingDate, 'YMD', 'm/d/Y');
    }
    if (trim($ssnNumber) != '') $tempSSNNumber = Strings::formatSSNNumber($ssnNumber);

    if (Dates::IsEmpty($borrowerDOB)) {
        $borrowerDOB = '';
    } else {
        $borrowerDOB = Dates::formatDateWithRE($borrowerDOB, 'YMD', 'm/d/Y');
    }

    for ($j = 0; $j < count($PCStatusInfo); $j++) {
        if (trim($PCStatusInfo[$j]['PSID']) == $primaryStatusId) $primaryStatus = Strings::escapeQuoteNew(trim($PCStatusInfo[$j]['primaryStatus']));
    }
    $k = 0;
    for ($i = 0; $i < count($PCSubStatusInfo); $i++) {
        for ($j = 0; $j < count($fileSubstatusInfo); $j++) {
            if (trim($PCSubStatusInfo[$i]['PFSID']) == trim($fileSubstatusInfo[$j]['substatusId'])) {
                if ($k > 0) $fileSubStatus .= ', ';
                $fileSubStatus .= trim($PCSubStatusInfo[$i]['substatus']);
                $k++;
            }
        }
    }

    $appStr = '';
    if (trim($propertyAddress) != '') {
        $propAddressInfo = ucwords($propertyAddress);
        $appStr = '';
    }
    if (trim($propertyCity) != '') {
        $propAddrInfo .= $appStr . ucfirst($propertyCity);
        $appStr = ', ';
    }
    if (trim($propertyState) != '') {
        $propAddrInfo .= $appStr . $propertyState;
        $appStr = '. ';
    }
    if (trim($propertyZip) != '') {
        $propAddrInfo .= $appStr . $propertyZip;
        $appStr = '';
    }
    $appStr = '';

    if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1)) {
        $submitterEmail = '';
        $submitterPhone = '';
        $submittedBy = '';
        $submitterPCName = '';
        $submitterCell = '';
        $CFPBSubmitterInfo = $tempCFPBSubmitterInfo = [];
        $CFPBSubmitterUID = 0;
        $CFPBSubmitterUType = '';
        $submitterPCName = Strings::showField('processingCompanyName', 'PCInfo');

        if (count($CFPBInfo) > 0) {
            $CFPBSubmitterUID = Strings::showField('UID', 'CFPBInfo');
            $CFPBSubmitterUType = Strings::showField('UType', 'CFPBInfo');
            $tempCFPBSubmitterInfo = getUserInfo::getReport(['UID' => $CFPBSubmitterUID, 'UType' => $CFPBSubmitterUType]);
        }
        if (count($tempCFPBSubmitterInfo) > 0) {
            if (array_key_exists($CFPBSubmitterUID, $tempCFPBSubmitterInfo)) $CFPBSubmitterInfo = $tempCFPBSubmitterInfo[$CFPBSubmitterUID];
        }
        if (count($CFPBSubmitterInfo) > 0) {
            $submitterEmail = trim($CFPBSubmitterInfo['email']);
            $submitterPhone = Strings::formatPhoneNumber(trim($CFPBSubmitterInfo['tollFree']));
            $submitterCell = Strings::formatPhoneNumber(trim($CFPBSubmitterInfo['cellNumber']));
            $submittedBy = trim($CFPBSubmitterInfo['employeeNames']);
        }
        $submitterPCPhone = Strings::formatPhoneNumber(Strings::showField('attorneyTelephone', 'PCInfo'));
    }
    /* Customization for PC = The J. Freeman Firm March 2 , 2016
    1492 = The J. Freeman Firm  , 820 = Dave PC , 2 = AWATA  PC
    */

    $billingPaymentKeyArray = [];
    $billingFeeKeyArray = [];
    if (count($billingPaymentInfoArray) > 0) {
        $billingPaymentKeyArray = array_keys($billingPaymentInfoArray);
    }
    $totalPaidAmount = 0;
    $totalOwedAmt = 0;
    $totalBalanceOwed = 0;

    /* no of Phases shown in Billing section START */

    $noOfCols = CONST_BILLING_PHASE;
    /* no of Phases shown in Billing section END */

    if (count($billingFeeInfoArray) > 0) {
        $billingFeeKeyArray = array_keys($billingFeeInfoArray);
    }
    for ($bf = 0; $bf < count($billingFeeKeyArray); $bf++) {
        $billingKey = '';
        $tempArray = [];
        $billingKey = trim($billingFeeKeyArray[$bf]);
        if (array_key_exists($billingKey, $billingFeeInfoArray)) {
            $tempArray = [];
            $tempArray = $billingFeeInfoArray[$billingKey];
            for ($bt = 0; $bt < count($tempArray); $bt++) {
                $noOfCols = trim($tempArray[$bt]['maxPhase']);
                break 2;
            }
        }
    }
    if ((int)($noOfCols) == 0 || (int)($noOfCols) <= CONST_BILLING_PHASE) {
        $noOfCols = CONST_BILLING_PHASE;
    }
    $totalAmt = '';
    for ($i = 1; $i <= $noOfCols; $i++) {
        ${'totalOwedAmt' . $i} = 0;
        ${'totalPaid' . $i} = 0;
        ${'billingDateOwed' . $i} = 0;
        ${'totalAmt' . $i} = 0;
    }

    for ($bf = 0; $bf < count($billingFeeKeyArray); $bf++) {
        $billingKey = '';
        $tempArray = [];
        $billingKey = trim($billingFeeKeyArray[$bf]);
        if (array_key_exists($billingKey, $billingFeeInfoArray)) {
            $tempArray = $billingFeeInfoArray[$billingKey];
            for ($bt = 0; $bt < count($tempArray); $bt++) {
                $phase = '';
                $feeAmount = '';
                $feeCode = '';
                $feeCode = trim($tempArray[$bt]['feeCode']);
                $phase = trim($tempArray[$bt]['phase']);
                $feeAmount = trim($tempArray[$bt]['feeAmount']);

                ${'totalAmt' . $phase} = floatval(${'totalAmt' . $phase});

                ${$feeCode . '_phase_' . $phase} = Strings::replaceCommaValues($feeAmount);
                ${$feeCode . '_phase_' . $phase} = Currency::formatDollarAmountWithDecimal(${$feeCode . '_phase_' . $phase}, 2);
                ${'totalAmt' . $phase} += Strings::replaceCommaValues(${$feeCode . '_phase_' . $phase});
            }
        }
    }
    for ($bp = 0; $bp < count($billingPaymentKeyArray); $bp++) {
        $phaseKey = '';
        $tempBillingArray = [];
        $phaseKey = $billingPaymentKeyArray[$bp];

        $tempBillingArray = $billingPaymentInfoArray[$phaseKey];
        ${'billingDateOwed' . $phaseKey} = trim($tempBillingArray['dateOwed']);
        ${'billingDateOwed' . $phaseKey} = Dates::formatDateWithRE(${'billingDateOwed' . $phaseKey}, 'YMD', 'm/d/Y');
        ${'totalPaid' . $phaseKey} = trim($tempBillingArray['totalPaid']);

        $totalPaidAmount += Strings::replaceCommaValues(${'totalPaid' . $phaseKey});

    }

    $tempColorY = 0;
    $curDate = date('Y/m/d');
    $curDate = Dates::formatDateWithRE($curDate, 'YMD', 'm/d/Y');

    for ($i = 1; $i <= $noOfCols; $i++) {
        $billingDateOwed = '';
        $totalPaid = 0;
        $totalBalAmtOwed = 0;
        ${'totalOwedAmt' . $i} = Strings::replaceCommaValues(${'totalAmt' . $i});

        $billingDateOwed = ${'billingDateOwed' . $i};

        $totalBalAmtOwed = Strings::replaceCommaValues(${'totalOwedAmt' . $i}) - Strings::replaceCommaValues(${'totalPaid' . $i});
        if ($billingDateOwed != '' && strtotime($billingDateOwed) < strtotime($curDate) && $totalBalAmtOwed > 0) {
            $tempColorY = 1;
        }
        $totalOwedAmt += ${'totalOwedAmt' . $i};
    }

    $totalBalanceOwed = $totalOwedAmt - $totalPaidAmount;
    $clsColor = '';

    if (in_array($PCID, $showTotalOwedAndOutstandingBalance)) {
        if ($tempColorY == 1) {
            $clsColor = 'red-text';
        }
    }

    $typeOfHMLOLoanRequesting = null;
    if (count($fileHMLOPropertyInfo) > 0) {
        $typeOfHMLOLoanRequesting = trim(Strings::showField('typeOfHMLOLoanRequesting', 'fileHMLOPropertyInfo'));
        $loanTerm = Strings::showField('loanTerm', 'fileHMLOPropertyInfo');

    }
    $fileInternalLoanProgramsName = getFileInfo::$fileInternalLoanProgramsName['ClientTypeInternal'] ?? '';
    /** Mouse over help icon >> Show Borrower details **/


    $midFicoScoreLabel = loanForm::$formFields['BCI']['midFicoScore']->fieldLabel ?? '';
    $borNoOfREPropertiesCompletedLabel = loanForm::$formFields['BE']['borNoOfREPropertiesCompleted']->fieldLabel ?? '';
    $borNoOfFlippingExperienceLabel = loanForm::$formFields['BE']['borNoOfFlippingExperience']->fieldLabel ?? '';
    $tblFileHMLO = tblFileHMLO::Get([
        'fileID' => $LMRId
    ]);
    $midFicoScoreView = 0;
    if ($tblFileHMLO) {
        $midFicoScoreView = $tblFileHMLO->midFicoScore;
    }
    $tblFileHMLOExperience = tblFileHMLOExperience::Get([
        'fileID' => $LMRId
    ]);
    $borNoOfREPropertiesCompleted = 0;
    $borNoOfFlippingExperience = 0;
    if ($tblFileHMLOExperience) {
        $borNoOfREPropertiesCompleted = $tblFileHMLOExperience->borNoOfREPropertiesCompleted;
        $borNoOfFlippingExperience = $tblFileHMLOExperience->borNoOfFlippingExperience;
    }


    $BorInfoMO = <<<EOT
		  <table style="width:100%;">
		  <tr>
			    <td><b> File ID :</b></td>
			    <td>$LMRId</td>
			</tr>

EOT;
    if (trim($loanNumber) != '') {
        if ($isHMLO == 1) {
            $BorInfoMO .= <<<EOT
		    <tr>
			    <td><b> Loan #:</b></td>
			    <td>$loanNumber</td>
			</tr>
EOT;
        } else {
            $BorInfoMO .= <<<EOT
                <tr>
                    <td><b> 1st Lien Loan Number:</b></td>
                    <td>$loanNumber</td>
                </tr>
EOT;
        }
    }
    if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1)) {
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td style="vertical-align:top"><b>Company Name:</b></td>
			  <td>$submitterPCName</td>
			</tr>
EOT;
        if (trim($submitterPCPhone) != '') {
            $submitterPCPhone = Strings::formatPhoneNumber($submitterPCPhone);
            $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Company Phone:</b></td>
			  <td>$submitterPCPhone</td>
			</tr>
EOT;
        }
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td style="vertical-align:top"><b>Submitted By:</b></td>
			  <td>$submittedBy</td>
			</tr>
EOT;
        if (trim($submitterPhone) != '') {
            $submitterPhone = Strings::formatPhoneNumber($submitterPhone);
            $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Submitter Phone:</b></td>
			  <td>$submitterPhone</td>
			</tr>
EOT;
        }
        if (trim($submitterCell) != '') {
            $submitterCell = Strings::formatPhoneNumber($submitterCell);
            $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Submitter Cell:</b></td>
			  <td>$submitterCell</td>
			</tr>
EOT;
        }
        if (trim($submitterEmail) != '') {
            $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Submitter Email:</b></td>
			  <td>$submitterEmail</td>
			</tr>
EOT;
        }

    }
    if (trim($entityName) != '') {
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Entity Name:</b></td>
			  <td>$entityName</td>
			</tr>
EOT;
    }
    $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Borrower&apos;s Name:</b></td>
			  <td>$borrowerName</td>
			</tr>
EOT;
    if (trim($phoneNumber) != '') {
        $phoneNumber = Strings::formatPhoneNumber($phoneNumber);
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Phone:</b></td>
			  <td>$phoneNumber</td>
			</tr>
EOT;
    }
    if (trim($cellNumber) != '') {
        $cellNumber = Strings::formatPhoneNumber($cellNumber);
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Cell:</b></td>
			  <td>$cellNumber</td>
			</tr>
EOT;
    }
    if (trim($borOrigEmail) != '') {
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Email:</b></td>
			  <td>$borOrigEmail</td>
			</tr>
EOT;
    }
    if (trim($propAddrInfo) != '') {
        $propAddrInfo = $propAddrInfo;
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Property Address:</b></td>
			  <td>
EOT;
        if ($propAddressInfo != '') {
            $BorInfoMO .= <<<EOT
            $propAddressInfo<br>
EOT;
        }
        $BorInfoMO .= <<<EOT
        $propAddrInfo</td>
			</tr>
EOT;
    }
    if (trim($tempSSNNumber) != '') {
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Social Security Number:</b></td>
			  <td>$tempSSNNumber</td>
			</tr>
EOT;
    }
    if (trim($borrowerDOB) != '') {
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Date Of Birth:</b></td>
			  <td>$borrowerDOB</td>
			</tr>
EOT;
    }
    if (trim($occupancy) != '') {
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>Occupancy:</b></td>
			  <td>$occupancy</td>
			</tr>
EOT;
    }
    if (trim($servicer1) != '') {
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>1st Lien Current Lender:</b></td>
			  <td>$servicer1</td>
			</tr>
EOT;
    }

    if (trim($loanNumber2) != '') {
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>2nd Lien Loan Number:</b></td>
			  <td>$loanNumber2</td>
			</tr>
EOT;
    }
    if (trim($servicer2) != '') {
        $BorInfoMO .= <<<EOT
		    <tr>
			  <td><b>2nd Lien Current Lender:</b></td>
			  <td>$servicer2</td>
			</tr>
EOT;
    }
    if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1)) {
    } else {
        if (trim($primaryStatus) != '') {
            $BorInfoMO .= <<<EOT

EOT;
            if (trim($isHMLO) == 1) {
                $BorInfoMO .= <<<EOT
                <tr>
                    <td><b>File Primary Status:</b></td>
                    <td>$primaryStatus</td>
                </tr>
EOT;
            } else {
                $BorInfoMO .= <<<EOT
            <tr>
			    <td><b>Primary Client File Status:</b></td>
                <td>$primaryStatus</td>
			</tr>
EOT;
            }
        }
        if (trim($fileSubStatus) != '') {
            $fileSubStatus = $fileSubStatus;
            $BorInfoMO .= <<<EOT
		    <tr>
			  <td style="vertical-align:top"><b>File Sub Status:</b></td>
			  <td>$fileSubStatus</td>
			</tr>
EOT;
        }
    }
    if ($isHMLO == 1) {
        if (trim($tempLMRClientServiceType) != '') {
            $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Loan Programs:</b></td>
                <td>$tempLMRClientServiceType</td>
            </tr>
EOT;
        }

        if (trim($closingDate) != '') {
            $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Closing Date:</b></td>
                <td>$closingDate</td>
            </tr>
EOT;
        }
    }

    if ($isMF == 1) {
        if (trim($MFLoanTermsInfoCnt) > 0) {
            $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Applications Submitted: </b></td>
                <td>$MFLoanTermsInfoCnt</td>
            </tr>
EOT;
        }

        if (trim($noOfApprovedStatusCnt) > 0) {
            $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Applications Approved: </b></td>
                <td>$noOfApprovedStatusCnt</td>
            </tr>
EOT;
        }

        if (trim($totalApprovedLoanAmt) != '') {
            $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Total Amount Approved: </b></td>
                <td>$totalApprovedLoanAmt</td>
            </tr>
EOT;
        }
    }

    if ($typeOfHMLOLoanRequesting) {
        $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Transaction Type:</b></td>
                <td>$typeOfHMLOLoanRequesting</td>
            </tr>
EOT;
    }
    if ($fileInternalLoanProgramsName) {
        $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Internal Loan Program:</b></td>
                <td>$fileInternalLoanProgramsName</td>
            </tr>
EOT;
    }
    if (glCustomJobForProcessingCompany::showLoanInfoV2TotalLoanAmount($PCID)) {
        $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Total Loan Amount:</b></td>
              <td>
EOT;
        $BorInfoMO .= Currency::formatDollarAmountWithDecimal(LMRequest::myFileInfo()->getLoanPropertySummary()->totalPropertiesLoanAmount);
        $BorInfoMO .= <<<EOT
        </td>
            </tr>
EOT;
    } else if ($loanAmount) {
        $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Loan Amount:</b></td>
                <td>$loanAmount</td>
            </tr>
EOT;
    }
    if ($lien1Rate) {
        $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Rate:</b></td>
                <td>$lien1Rate</td>
            </tr>
EOT;
    }
    if ($loanTerm) {
        $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Loan Term:</b></td>
                <td>$loanTerm</td>
            </tr>
EOT;
    }
    //if($midFicoScore) {
    $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>$midFicoScoreLabel:</b></td>
                <td>$midFicoScoreView</td>
            </tr>
EOT;
    //}
    //if($borNoOfREPropertiesCompleted) {
    $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Application Experience:</b></td>
                <td>$borNoOfREPropertiesCompleted</td>
            </tr>
EOT;
    //}
    //if($borNoOfFlippingExperience) {
    $BorInfoMO .= <<<EOT
            <tr>
                <td style="vertical-align:top"><b>Validated Experience:</b></td>
                <td>$borNoOfFlippingExperience</td>
            </tr>
EOT;
    //}

    $BorInfoMO .= <<<EOT
		  </table>
EOT;
}
if($LMRId){
    $HMLOTAC = LMRequest::myFileInfo()->BranchInfo()->TAC;
    $HMLOTACQA = LMRequest::myFileInfo()->BranchInfo()->TACQA;
}
if (!$HMLOTAC || !$HMLOTACQA) {
    $HMLOTAC = $glHMLOTAC;
    $HMLOTACQA = $glHMLOTAC;
}
?>

<div id="divLoader"><img src="<?php echo IMG_PROGRESS_BAR; ?>" alt=""></div>
<div class="subheader py-0 py-lg-6 subheader-solid" id="kt_subheader">
    <div class="container-fluid d-flex align-items-center justify-content-between flex-wrap flex-sm-nowrap">
        <?php

        $hideThisField = 1; //default show
        //permission to view, $shareThisFile value assigned in getPageVariables.php


        if ($LMRId == 0 && $isSLM == 1) {
            ?>
            <div class="d-flex align-items-center flex-wrap mr-2"><h6 class="text-dark font-weight-bold mt-2 mb-2 mr-5">
                    Create Student Loan Mod File</h6></div>
            <?php
        } else if ($PCID == 170 && $LMRId == 0) {
            ?>
            <div class="d-flex align-items-center flex-wrap mr-2"><h6 class="text-dark font-weight-bold mt-2 mb-2 mr-5">
                    Debt Revision Modification Short Sale Portal</h6></div>
            <?php
        } else if ($PCID == 1434 && $branchReferralCode == '365320') {
            ?>
            <div class="d-flex align-items-center flex-wrap mr-2"><h6 class="text-dark font-weight-bold mt-2 mb-2 mr-5">
                    Create LDA Report File</h6></div>
            <?php
        } else if ($LMRId == 0) { /* Changed the Lable for PC = THE	FAME GROUP = 1494  04 May, 2016 */
            ?>
            <div class="d-flex align-items-center flex-wrap mr-2"><h6 class="text-dark font-weight-bold mt-2 mb-2 mr-5">
                    <?php if (($PCID == 2 || $PCID == 820 || $PCID == 1494) && ($publicUser == 1 && $agentReferralCode > 1)) { ?>
                        Create Lead Application
                    <?php } else { ?>
                        Create Loan File
                    <?php } ?>
                </h6></div>
            <?php
        } else {
            if ($LMRId > 0) { ?>
                <div class="d-flex align-items-center flex-wrap mr-2">
                    <?php
                    if ($isHOALien == 1 && $publicUser != 1) {
                        ?>
                        <h6 class="text-dark font-weight-bold mt-2 mb-2 mr-5">
                            File <?php if (trim($propertyAddress) != '') echo ' of ' . ucwords($propertyAddress); ?>
                        </h6>

                        <?php
                    } else {
                        $appStr = '';
                        $fileTitle = '';
                        if ($entityName != '') {
                            $fileTitle = $entityName;
                            $appStr = ', ';
                        }
                        if ($borrowerName != '') {
                            if ($fileTitle != '') {
                                $fileTitle .= $appStr;
                            }
                            $fileTitle .= $borrowerName;
                            $appStr = ', ';
                        }
                        if (trim($propertyAddress) != '') {
                            $propAddrInfo = ucwords($propertyAddress);
                            $appStr = ', ';
                        }

                        if ($propertyUnit) {
                            if ((in_array($propertyType, [GpropertyTypeNumbArray::CONDO,
                                    GpropertyTypeNumbArray::CONDO_HIGH_RISE,
                                    GpropertyTypeNumbArray::CONDO_LOW_RISE]) && $PCID == glPCID::PCID_PROD_CV3)) {
                                $propAddrInfo .= $appStr . $propertyUnit;
                            } else {
                                $propAddrInfo .= $appStr . 'Unit ' . $propertyUnit;
                            }
                            $appStr = ', ';
                        }

                        if (trim($propertyCity) != '') {
                            $propAddrInfo .= $appStr . ucfirst($propertyCity);
                            $appStr = ', ';
                        }
                        if (trim($propertyState) != '') {
                            $propAddrInfo .= $appStr . $propertyState;
                            $appStr = ' ';
                        }
                        if (trim($propertyZip) != '') {
                            $propAddrInfo .= $appStr . $propertyZip;
                            $appStr = '';
                        }
                        ?>
                        <h6 class="text-dark font-weight-bold mt-2 mb-2 mr-2">
                            File: <?php echo stripslashes($fileTitle);
                            if ($propAddrInfo != '') echo ', ' . stripslashes($propAddrInfo);
                            if ($loanNumber && glCustomJobForProcessingCompany::showLoanNumberInTitle($PCID)) echo ', LN# ' . $loanNumber; ?>
                        </h6>
                        <?php
                    } ?>
                    <i data-title='<?php echo htmlentities($borrowerName); ?>'
                       data-content='<?php echo htmlentities($BorInfoMO, ENT_QUOTES | ENT_SUBSTITUTE | ENT_DISALLOWED) ?>'
                       data-placement="bottom"
                       class="fas fa-info-circle text-primary manualPopover">
                    </i>
                </div>
                <?php if (PageVariables::isSuper()) { ?>
                    <div class="d-flex align-items-center flex-wrap border bg-primary-o-10">
                        <a target="_blank"
                           href="/backoffice/createProcessingCompany.php?pcId=<?= cypher::myEncryption(LMRequest::File()->FPCID); ?>"
                           title="Company Profile"
                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1  tooltipClass" >
                            <i class="fas fa-building"></i>
                        </a>
                        <a target="_blank"
                           href="/backoffice/processorList.php?procCompanyId=<?= cypher::myEncryption(LMRequest::File()->FPCID); ?>"
                           title="Backoffice Users"
                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1  tooltipClass" >
                            <i class="fas fa-hospital-user"></i>
                        </a>
                        <a target="_blank"
                           href="/backoffice/branchList.php?pcId=<?= cypher::myEncryption(LMRequest::File()->FPCID); ?>"
                           title="Branch Users"
                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1  tooltipClass" >
                            <i class="fas fa-users text-primary"></i>
                        </a>
                        <a target="_blank"
                           href="/backoffice/brokers/?PCID=<?= (LMRequest::File()->FPCID); ?>"
                           title="Brokers"
                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1  tooltipClass" >
                            <i class="fas fa-users text-dark"></i>
                        </a>
                        <a target="_blank"
                           href="/backoffice/loan_officers/?PCID=<?= (LMRequest::File()->FPCID); ?>"
                           title="Loan Officers"
                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1  tooltipClass" >
                            <i class="fas fa-users text-dark"></i>
                        </a>
                    </div>
                <?php } ?>
                <div class="d-flex align-items-center flex-wrap">
                <?php    if ((PageVariables::$allowToUpdateFileAdminSection == 1)
                    || (PageVariables::$userRole == 'Agent' && PageVariables::$userNumber == $brokerNumber
                        && PageVariables::$allowToUpdateFileAdminSection == 1)

                    ) { ?>
                    <a data-href="<?php echo CONST_URL_POPS; ?>updateFileStatus.php" href="javascript:void(0)"
                       data-name="File: <?php echo $borrowerName; ?> > Update File Status"
                       class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 tooltipClass"
                       title="Status: <?php echo $filePrimaryStatus.' Sub-Status: '.$subStatus; ?>" style="text-decoration: none;" data-toggle='modal'
                       data-target='#exampleModal1'
                       data-wsize='modal-lg'
                       data-id="LID=<?php echo cypher::myEncryption($LMRId); ?>&RID=<?php echo cypher::myEncryption($LMRResponseId); ?>&exID=<?php echo cypher::myEncryption($executiveId); ?>
                       &PCID=<?php echo cypher::myEncryption($PCID); ?>&fileTypesTxt=<?php echo cypher::myEncryption($fileModuleCodeForWebForm); ?>&URole=<?php echo  cypher::myEncryption($userRole); ?>&dataLoc=file"><i
                                class="flaticon2-reload text-success"></i></a>
                    <?php
                    }
                    if ($isMF == 1) {
                        $glNotesTypeArray = $glFUModulesNotesTypeArray;
                    }

                    $processorCommentsNotes = '';
                    $notesEmpInfo = [];
                    $notesAgentInfo = [];
                    $notesBranchInfo = [];
                    $notesClientInfo = [];


                    if (array_key_exists('notesEmpInfo', $myFileInfo)) $notesEmpInfo = $myFileInfo['notesEmpInfo']();
                    if (array_key_exists('notesAgentInfo', $myFileInfo)) $notesAgentInfo = $myFileInfo['notesAgentInfo']();
                    if (array_key_exists('notesBranchInfo', $myFileInfo)) $notesBranchInfo = $myFileInfo['notesBranchInfo']();
                    if (array_key_exists('notesClientInfo', $myFileInfo)) $notesClientInfo = $myFileInfo['notesClientInfo']();

                    $nCnt = 1;

                    foreach ($processorCommentsArray as $eachNote) {
                        $processorComments = '';
                        $notesDate = '';
                        $commentsBy = '';
                        $processorCommentsDetail = '';
                        $privateNotes = '';
                        $notesEmpId = 0;
                        $notesExeId = 0;
                        $notesAgentId = 0;
                        $notesClientId = $isSysNotes = 0;
                        $signExecutiveName = '';
                        $notesType = '';
                        $processorName = $updatedUserType = '';
                        $sendTo = '';

                        $processorComments = Strings::replaceProcessedHeader(rawurldecode(trim($eachNote['processorComments'])));
                        $notesDate = trim($eachNote['notesDate']);
                        $sendTo = trim($eachNote['sendTo']);
                        $privateNotes = trim($eachNote['private']);
                        $signExecutiveName = trim($eachNote['signExecutiveName']);
                        $notesEmpId = trim($eachNote['employeeId']);
                        $notesExeId = trim($eachNote['executiveId']);
                        $notesAgentId = trim($eachNote['brokerNumber']);
                        $notesClientId = trim($eachNote['clientId']);
                        $notesType = trim($eachNote['notesType']);
                        $isSysNotes = trim($eachNote['isSysNotes']);
                        $processorName = trim($eachNote['processorName']);
                        $updatedUserType = trim($eachNote['updatedUserType']);

                        if (!$showSysGenNote && $isSysNotes) {
                            continue;
                        }
                        if (($viewPrivateNotes && $privateNotes)
                            || ($viewPublicNotes && !$privateNotes)
                        ) {
                            $notesType = $glNotesTypeArray[$notesType] ?? $notesType;

                            if ($notesEmpId > 0) {
                                if (array_key_exists($notesEmpId, $notesEmpInfo)) $commentsBy = trim($notesEmpInfo[$notesEmpId]['processorName']);
                            } elseif ($notesExeId > 0) {
                                if (array_key_exists($notesExeId, $notesBranchInfo)) $commentsBy = trim($notesBranchInfo[$notesExeId]['LMRExecutive']);
                            } elseif ($notesAgentId > 0) {
                                if (array_key_exists($notesAgentId, $notesAgentInfo)) $commentsBy = trim($notesAgentInfo[$notesAgentId]['agentName']);
                            } elseif ($notesClientId > 0) {
                                if (array_key_exists($notesClientId, $notesClientInfo)) $commentsBy = trim($notesClientInfo[$notesClientId]['clientName']);
                            } elseif ($processorName != '') {
                                $commentsBy = $processorName . '- (' . $updatedUserType . ')';
                            } else {
                                $commentsBy = 'Admin';
                            }
                            $ipArray['inputZone'] = CONST_SERVER_TIME_ZONE;
                            $ipArray['outputZone'] = $userTimeZone;
                            $ipArray['inputTime'] = $notesDate;
                            $notesDate = Dates::timeZoneConversion($ipArray);
                            $notesDate = Dates::formatDateWithRE($notesDate, 'YMD_HMS', 'M j, Y h:i A') . ' - ' . $userTimeZone;

                            if (in_array($eachNote['displayIn'], ['NH', 'BO', 'SH'])) {
                                $processorCommentsDetail .= '<span class="font-weight-bolder">' . trim($processorComments) . '</span>';
                                if (!preg_match('/Request Status Update:/i', $processorComments)) {
                                    if ($commentsBy != 'Admin') {
                                        $processorCommentsDetail .= "<span class='d-flex flex-row mt-3'>
                                                                        <span class=\"text-muted flex-fill ml-auto pl-4 col-md-6\">- " . $commentsBy . ' (' . $notesDate . ')</span>';
                                        $processorCommentsDetail .= "<span class=\"badge label label-inline   bg-warning-o-50 flex-fill ml-auto col-md-3 text-truncate\">" . $notesType . '</span>';
                                        $processorCommentsDetail .= "<span class=\"badge label label-inline   bg-warning-o-50 flex-fill ml-auto col-md-1 text-truncate\">";
                                        if ($isSysNotes) {
                                            $processorCommentsDetail .= "<i class='icon-md la la-laptop-medical text-dark tooltipClass' title='System Generated Notes'></i>";
                                        } else {
                                            $processorCommentsDetail .= "<i class='icon-md la la-user text-dark tooltipClass' title='User Notes'></i>";
                                        }
                                        $processorCommentsDetail .= '</span>';
                                        $processorCommentsDetail .= '</span>';
                                    } else {
                                        $processorCommentsDetail .= "<span class='d-flex flex-row mt-1'><span class=\"text-muted flex-fill ml-auto pl-4 col-md-12\">- " . $commentsBy . ' (' . $notesDate . ')</span>';
                                    }
                                }
                                $nCnt++;
                            }
                            if ($sendTo) {
                                $processorCommentsDetail .= "<span class='d-flex flex-row mt-1'><span class='text-muted  pl-4 col-md-12 font-weight-bolder'>- Sent To (" . $sendTo . ')</span></span>';
                            }
                            $clsName = ($nCnt % 2 ? 'bg-gray-100' : '');
                            $isSysNotesClass = $isSysNotes == 1 ? ' isSysNotes ' : '';
                            $processorCommentsNotes .= "<li class=\"list-group-item px-2 py-1 " . $clsName . " " . $isSysNotesClass . " \">" . $processorCommentsDetail . '</li>';

                            if ($nCnt == 25) break;
                        }
                    }

                    if (!($publicUser == 1
                        || $userRole == 'CFPB Auditor'
                        || $userRole == 'Auditor Manager'
                        || ($allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1 && Strings::showField('FPCID', 'LMRInfo') != $PCID)
                    )
                    ) { /* CFPB auditor disable notes icon START */
                        // if ($viewPrivateNotes == 1 || $viewPublicNotes == 1) {

                        $commentIcon = ($processorCommentsNotes == '') ? 'fa-comment-medical' : 'fa-comments';
                        $processorCommentHeaderButton = '
                            <span class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 tooltipClass"
                                            title="Add Notes"
                                            id="notesIcon"
                                            data-href="' . CONST_URL_POPS . 'addNotes.php"
                                            data-wsize = "modal-xl"
                                            data-name="File: ' . htmlentities($borrowerName) . '> Add Note"
                                            data-toggle="modal" data-target="#exampleModal1"
                                            data-id="rId=' . cypher::myEncryption($LMRResponseId) .
                            '&exID=' . cypher::myEncryption($executiveId) .
                            '&LMRId=' . cypher::myEncryption($LMRId) .
                            '&opt=fileTab&FPCID=' . cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo')) .
                            '&UR=' . cypher::myEncryption($userRole) .
                            '&isHMLO=' . $isHMLO .
                            '&showSaveBtn=1">
                                    <i class="icon-md fas ' . $commentIcon . ' "></i>
                            </span>
                                    ';

                        $processorCommentHeaderButtonAdd = '
                            <span class="btn btn-xs btn-primary btn-text-primary btn-hover-primary btn-icon mr-2 tooltipClass"
                                            title="Add Notes"
                                            data-href="' . CONST_URL_POPS . 'addNotes.php"
                                            data-wsize="modal-xl"
                                            data-name="File: ' . htmlentities($borrowerName) . '> Add Note"
                                            data-toggle="modal"
                                            data-target="#exampleModal1"
                                            data-id="rId=' . cypher::myEncryption($LMRResponseId) .
                            '&exID=' . cypher::myEncryption($executiveId) .
                            '&LMRId=' . cypher::myEncryption($LMRId) .
                            '&opt=fileTab&FPCID=' . cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo')) .
                            '&UR=' . cypher::myEncryption($userRole) .
                            '&isHMLO=' . $isHMLO .
                            '&showSaveBtn=1">
                                    <i class="icon-sm fas fa-comment-medical "></i>
                            </span>
                                    ';
                        // $processorCommentsNotesList = '<ul class="list-group p-1 font-size-sm">' . Strings::stripQuote($processorCommentsNotes) . '</ul>'; ?>

                        <div id="divListNotes<?php echo $LMRId ?>">
            <span id="status-bar">
                <span class="status-infos">
                    <span class="lireplace">
                        <?php echo $processorCommentHeaderButton; ?>
                        <div class="result-block card-body p-0 ">
                            <span class="arrow"></span>
                            <div class="card card-custom">
                                <div class="card-header">
                                    <div class="card-title">
                                         <h3 class="card-label">Latest 25 notes are listed below</h3>
                                        <?php echo $processorCommentHeaderButtonAdd; ?>
                                    </div>
                                    <div class="card-toolbar">
                                        <?php if ($showSysGenNote) { ?>
                                            <div class="switch switch-sm switch-icon ">
                                            <label class="">Show system generated notes </label>
                                            <label class="font-weight-bold">
                                                <input type="checkbox"
                                                       class="form-control"
                                                       onchange="fileCommon.showHideSysGenNotes(this);"
                                                       checked>
                                                <span></span>
                                            </label>
                                        </div>
                                        <?php } ?>
                                    </div>
                                </div>
                                <div class="card-body p-0 note-scrollbar" id="note-style-3">
                                    <div class="note-force-overflow">
                                            <ul class="list-group p-1 font-size-sm"
                                                id="listNotes<?php echo $LMRId ?>"><?php echo Strings::stripQuote($processorCommentsNotes); ?></ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </span>
                </span>
            </span>
                        </div>
                        <?php
                        //   }
                    }

                    //End oF Notes

                    /**
                     * Allow Loan version Email Send to Client. pivotal #154715300
                     */
                    //if( ($userRole == 'Manager' || $userRole == 'Super') && $LMRId > 0 && $isHMLO == 1 && array_key_exists( Strings::showField('primeStatusId', 'ResponseInfo'), $permissionsToEditClient ) )  { //Allowed for all user type refer pivotal Ticket: #155549871

                    //if (in_array($PCID, $glAllowHMLOPCToEditBrokerINLV)) {

                    if ($userGroup != 'Client' && $LMRId > 0) {
                        $allowToSendBorrower = 1;
                        $allowToSendAgent = 1;
                        /* if (array_key_exists( Strings::showField('primeStatusId', 'ResponseInfo'), $permissionsToEditClient )) $allowToSendBorrower = 1; https://www.pivotaltracker.com/story/show/159896558
                            if (array_key_exists( Strings::showField('primeStatusId', 'ResponseInfo'), $permissionsToEditAgent )) $allowToSendAgent = 1;
                            sendLoanAppEmail('myEncryption($LMRId)', 'BO'); */
                        ?>

                        <!--                   <span class="left pad5 span12367">
		<div class="with-children-tip">
			<a title="Click to send an email to the borrower to complete the loan application"
               style="text-decoration:none;" href="<?php echo CONST_URL_POPS; ?>sendLoanAppEmail.php"
               name="<?php echo $borrowerName ?> > Send an email link to complete the loan application? "
               id="LMRId=<?php echo cypher::myEncryption($LMRId) ?>&amp;ft=<?php echo cypher::myEncryption($fileModuleCodeForWebForm); ?>&amp;FPCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo')) ?>&amp;BO=<?php echo cypher::myEncryption($allowToSendBorrower) ?>&amp;UType=<?php echo cypher::myEncryption($userGroup) ?>&amp;BR=<?php echo cypher::myEncryption($allowToSendAgent) ?>"
               class="tip-bottom fa fa-2x fa-envelope"></a>
		</div>
	</span> -->

                        <?php //if (in_array($PCID, array(3400, 3307, 3345, 1652, 3580, 2863, 3373, 3821))) { //Enabled For Specific PC's Story 26?>

                        <a data-href="<?php echo CONST_URL_POPS; ?>sendFileEmail.php"
                           data-name="<?php echo htmlentities($borrowerName); ?> > Send An Email"
                           data-wsize='modal-xl'
                           data-id="LMRId=<?php echo cypher::myEncryption($LMRId) ?>&ft=<?php echo cypher::myEncryption($fileModuleCodeForWebForm); ?>&FPCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo')) ?>&BO=<?php echo cypher::myEncryption($allowToSendBorrower) ?>&UType=<?php echo cypher::myEncryption($userGroup) ?>&BR=<?php echo cypher::myEncryption($allowToSendAgent) ?>"
                           data-toggle='modal' data-target='#exampleModal1' href='#'
                           data-content="Compose Email"
                           data-placement="bottom"
                           class=" popoverClass btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 p-0"
                           id="sendEmail">
                   <span class="svg-icon svg-icon svg-icon-2x"><svg
                               xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                               width="24px" height="24px" viewBox="0 0 24 24">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect x="0" y="0" width="24" height="24"/>
        <path d="M6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 L19,12 C19,12.5522847 18.5522847,13 18,13 L6,13 C5.44771525,13 5,12.5522847 5,12 L5,3 C5,2.44771525 5.44771525,2 6,2 Z M7.5,5 C7.22385763,5 7,5.22385763 7,5.5 C7,5.77614237 7.22385763,6 7.5,6 L13.5,6 C13.7761424,6 14,5.77614237 14,5.5 C14,5.22385763 13.7761424,5 13.5,5 L7.5,5 Z M7.5,7 C7.22385763,7 7,7.22385763 7,7.5 C7,7.77614237 7.22385763,8 7.5,8 L10.5,8 C10.7761424,8 11,7.77614237 11,7.5 C11,7.22385763 10.7761424,7 10.5,7 L7.5,7 Z"
              fill="#000000" opacity="0.3"/>
        <path d="M3.79274528,6.57253826 L12,12.5 L20.2072547,6.57253826 C20.4311176,6.4108595 20.7436609,6.46126971 20.9053396,6.68513259 C20.9668779,6.77033951 21,6.87277228 21,6.97787787 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,6.97787787 C3,6.70173549 3.22385763,6.47787787 3.5,6.47787787 C3.60510559,6.47787787 3.70753836,6.51099993 3.79274528,6.57253826 Z"
              fill="#000000"/>
    </g>
</svg></span> </a>


                        <a data-href="<?php echo CONST_URL_POPS; ?>mailHistory.php"
                           data-name="Automation Emails/Email History"
                           data-wsize='modal-xl'
                           data-footerhide='hide'
                           data-id="LMRId=<?php echo cypher::myEncryption($LMRId) ?>&amp;ft=<?php echo cypher::myEncryption($fileModuleCodeForWebForm); ?>&amp;FPCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo')) ?>&amp;BO=<?php echo cypher::myEncryption($allowToSendBorrower) ?>&amp;UType=<?php echo cypher::myEncryption($userGroup) ?>&amp;BR=<?php echo cypher::myEncryption($allowToSendAgent) ?>"
                           data-toggle='modal' data-target='#exampleModal1' href='javascript:void(0)'
                           data-content="Scheduled Emails/Email History"
                           data-placement="bottom"
                           class=" popoverClass btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 p-0"
                           id="mailHistory">
                   <span class="svg-icon svg-icon svg-icon-2x"><!--begin::Svg Icon | path:C:\wamp64\www\keenthemes\themes\metronic\theme\html\demo1\dist/../src/media/svg/icons\Communication\Snoozed-mail.svg--><svg
                               xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"
                               width="24px" height="24px" viewBox="0 0 24 24">
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect x="0" y="0" width="24" height="24"/>
        <path d="M12.9835977,18 C12.7263047,14.0909841 9.47412135,11 5.5,11 C4.98630124,11 4.48466491,11.0516454 4,11.1500272 L4,7 C4,5.8954305 4.8954305,5 6,5 L20,5 C21.1045695,5 22,5.8954305 22,7 L22,16 C22,17.1045695 21.1045695,18 20,18 L12.9835977,18 Z M19.1444251,6.83964668 L13,10.1481833 L6.85557487,6.83964668 C6.4908718,6.6432681 6.03602525,6.77972206 5.83964668,7.14442513 C5.6432681,7.5091282 5.77972206,7.96397475 6.14442513,8.16035332 L12.6444251,11.6603533 C12.8664074,11.7798822 13.1335926,11.7798822 13.3555749,11.6603533 L19.8555749,8.16035332 C20.2202779,7.96397475 20.3567319,7.5091282 20.1603533,7.14442513 C19.9639747,6.77972206 19.5091282,6.6432681 19.1444251,6.83964668 Z"
              fill="#000000"/>
        <path d="M8.4472136,18.1055728 C8.94119209,18.3525621 9.14141644,18.9532351 8.89442719,19.4472136 C8.64743794,19.9411921 8.0467649,20.1414164 7.5527864,19.8944272 L5,18.618034 L5,14.5 C5,13.9477153 5.44771525,13.5 6,13.5 C6.55228475,13.5 7,13.9477153 7,14.5 L7,17.381966 L8.4472136,18.1055728 Z"
              fill="#000000" fill-rule="nonzero" opacity="0.3"/>
    </g>
</svg><!--end::Svg Icon--></span>
                        </a>


                        <?php //} ?>

                        <?php
                    }

                    if ($userGroup == 'Client' && !(array_key_exists($primaryStatusId, $permissionsToEdit))) {
                        ?>
                        <a data-href="<?php echo CONST_URL_POPS; ?>requestToAllowEditFile.php" href="javascript:void(0)"
                           data-name="File: <?php echo $borrowerName; ?> > Request To Update File"
                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 tooltipClass"
                           title="Request To Update File" style="text-decoration: none;" data-toggle='modal'
                           data-target='#exampleModal1'
                           data-wsize='modal-xl'
                           data-id="rId=<?php echo cypher::myEncryption($LMRResponseId); ?>&exID=<?php echo cypher::myEncryption($executiveId); ?>&LMRId=<?php echo cypher::myEncryption($LMRId); ?>&UR=<?php echo cypher::myEncryption($userRole); ?>"><i
                                    class="fa fa-edit"></i></a>
                        <?php
                    }

                    if (in_array($PCID, $showAssignedEmployeeToPC) && $activeTab == 'CI') {
                        if ($allowToUpdateFileAdminSection == 1 && $op != 'view' && $userRole != 'Auditor') {
                            $assignedEmpLink = '';
                            $AssignedStaffInfo = [];
                            $assignAppendComma = '';
                            if (array_key_exists('AssignedBOStaffInfo', $myFileInfo)) $AssignedStaffInfo = $myFileInfo['AssignedBOStaffInfo'];
                            if (count($AssignedStaffInfo) == 0) $assignedEmpLink = 'Click to assign employee';
                            for ($m = 0; $m < count($AssignedStaffInfo); $m++) {
                                $assignedEmpLink .= '<span id=ass_' . trim($AssignedStaffInfo[$m]['AID']) . '>';
                                $assignedEmpLink .= $assignAppendComma;
                                if (trim($AssignedStaffInfo[$m]['activeStatus']) == 0) $assignedEmpLink .= '<span class=red>';
                                if (trim(trim($AssignedStaffInfo[$m]['role'])) != '') $assignedEmpLink .= '<b>' . trim($AssignedStaffInfo[$m]['role']) . ': </b> ';
                                $assignedEmpLink .= trim($AssignedStaffInfo[$m]['processorName']);
                                if (trim($AssignedStaffInfo[$m]['activeStatus']) == 0) $assignedEmpLink .= '</span>';
                                $assignedEmpLink .= '</span><br>';
                            }
                            ?>
                            <div class="left padl5">
                                <div class="left with-children-tip pad5"><a
                                            class="fa fa-users fa-2x tip-bottom popuplink asgn_emp"
                                            style="text-decoration:none;"
                                            href="<?php echo CONST_URL_POPS; ?>assignEmployee.php"
                                            name=" <?php echo $borrowerName ?> > Assigning Employees"
                                            id="LMRId=<?php echo cypher::myEncryption($LMRId) ?>&amp;PCID=<?php echo cypher::myEncryption($PCID) ?>&amp;BRID=<?php echo cypher::myEncryption($executiveId) ?>&tab=CI"
                                            title="<?php echo $assignedEmpLink ?>"></a></div>
                                <div id="divListAssignEmp" style="display: none;"><?php echo $assignedEmpLink ?></div>
                            </div>
                            <?php
                        }
                    }
                    if (trim($mortgageNotes) != '' && $userGroup != 'Client' && $publicUser != 1) { ?>

                        <a type="button"
                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 manualPopover "
                           data-html="true"
                           data-trigger="focus"
                           data-placement="left"
                           data-title='Client Notes'
                           data-content='<?php echo Strings::escapeQuoteNew(nl2br($mortgageNotes)); ?>'
                           id="clientNotes"><i class="flaticon2-file-1 "></i>
                        </a>


                        <?php
                    }
                    if ($userGroup != 'Client' && $publicUser != 1) {
                        if (in_array($PCID, $ShowSaleDate) && $salesDate != '') {
                            ?>
                            <div class="left pad10"><h4>Sales Date: <?php echo $salesDate ?> </h4></div>
                            <?php
                        }
                        if (in_array($PCID, $showTotalOwedAndOutstandingBalance) && $totalBalanceOwed > 0) {
                            ?>
                            <div class="left pad10 <?php echo $clsColor ?>"><font size="3"><b>Outstanding
                                        Balance: <?php echo Currency::formatDollarAmountWithDecimal($totalBalanceOwed) ?> </b></font>
                            </div>
                            <?php
                        }
                    }


                    if ($isHMLO == 1 && $userGroup != 'Client') {
                        $url = CONST_SITE_URL . 'package/pkgController.php?rId=' . cypher::myEncryption($LMRResponseId) . '&amp;bn=' . cypher::myEncryption($brokerNumber) . '&amp;lId=' . cypher::myEncryption($LMRId) . '&amp;pkgID=' . cypher::myEncryption(1080) . '&opt=sample';
                        if ($publicUser != 1 && $userGroup != 'Client' && $allowToEdit) {
                            ?>
                            <label for="file-download" style="margin-top: 7px;">
                                <a title="Fannie Mae 3.4 Export"
                                   href="/mismo/fm34Export.php?lId=<?php echo cypher::myEncryption($LMRId); ?>"
                                   class="tooltipClass btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2"
                                   id="fm34Export"><img
                                            src="/assets/images/fm32red.png" style="width:20px"></a>
                            </label>
                            <label for="file-upload" class="" style="margin-top: 7px;">
                                <a title="Fannie Mae 3.4 Import"
                                   href="/backoffice/loan/fm34?eId=&lId=<?php echo Request::GetClean('lId'); ?>&rId=&tabOpt=fm34"
                                   class="tooltipClass btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2"
                                   id="fm34Import"><img
                                            src="/assets/images/fm32blue.png" style="width:20px"></a>
                            </label>
                            <?php
                        }
                    }

                    if ($LMRId > 0 && $shareThisFile == 1 || $userRole == 'Super') { ?>

                        <!-- Button trigger modal -->
                        <a data-id="LMRId=<?php echo cypher::myEncryption($LMRId); ?>&amp;ft=<?php echo cypher::myEncryption($fileModuleCodeForWebForm); ?>&amp;FPCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo')); ?>&amp;BO=<?php echo cypher::myEncryption($allowToSendBorrower); ?>&amp;UType=<?php echo cypher::myEncryption($userGroup); ?>&amp;BR=<?php echo cypher::myEncryption($allowToSendAgent); ?>"

                           class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2 popoverClass"
                           data-href="<?php echo CONST_URL_POPS; ?>shareThisFile.php"
                           data-toggle='modal' data-target='#exampleModal1'
                           data-footerhide="true"
                           data-wsize='modal-xl'
                           data-content="Share This File"
                           data-placement="bottom"
                           data-name='Share This File'
                           id="shareThisFile">
                            <i class="fa fa-share" aria-hidden="true"></i>
                        </a>
                        <!-- POC Update popup link -->
                        <a data-id="LMRId=<?php echo cypher::myEncryption($LMRId); ?>&amp;ft=<?php echo cypher::myEncryption($fileModuleCodeForWebForm); ?>&amp;FPCID=<?php echo cypher::myEncryption(Strings::showField('FPCID', 'LMRInfo')); ?>&amp;BO=<?php echo cypher::myEncryption($allowToSendBorrower); ?>&amp;UType=<?php echo cypher::myEncryption($userGroup); ?>&amp;BR=<?php echo cypher::myEncryption($allowToSendAgent); ?>"
                           class="pocPopUp hidden"
                           data-href="<?php echo CONST_URL_POPS; ?>submitOfferPopUp.php"
                           data-toggle='modal' data-target='#exampleModal2'
                           data-wsize='modal-xl' data-footerhide="false"
                           data-content="Update Point of contact"
                           data-placement="bottom"
                           data-name='Update Point of contact'>
                            POC
                        </a>


                    <?php }

                    if ($LMRId > 0) {

                        if ($addedToFav == 1) { ?>
                            <a data-html="true" title="Remove from favorites"
                               data-placement="bottom"
                               class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 addAsFav tooltipClass"
                               data-file-id="<?php echo cypher::myEncryption($LMRId); ?>"
                               data-opt="<?php echo('remove') ?>"
                               id="favoriteRemove">
                                <i class="fa fa-star text-warning"></i>
                            </a>
                        <?php } else { ?>
                            <a data-html="true" title="Add to favorites"
                               data-placement="bottom"
                               class="btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon m-1 addAsFav tooltipClass"
                               data-file-id="<?php echo cypher::myEncryption($LMRId); ?>"
                               data-opt="<?php echo('add') ?>"
                               id="favoriteAdd">
                                <i class="fa fa-star"></i>
                            </a>
                        <?php }
                    } ?>
                </div>
            <?php }
        }
        if ($LMRId == 0 && $publicUser != 1 && $userGroup != 'Client' && $allowToEdit && (
                Request::GetClean('tabOpt') == 'LI'
                || Request::GetClean('tabOpt') == 'QAPP'
                || Request::GetClean('tabOpt') == 1003
            )) {   //i could specify tabs but people cant reach any except fa and qa so its fine.
            ?>
            <label for="file-upload" class="" style="margin-top: 7px;">
                <a title="fannie mae 3.4 import"
                   class="tooltipClass btn btn-sm btn-light btn-text-primary btn-hover-primary btn-icon mr-2"><img
                            src="/assets/images/fm32blue.png" width="20px"></a> </label>
            <input id="file-upload" type="file" style="display: none" onchange="handleImportChange(this);"/>
            <div id="importAlert" class="alert hidden col-2 ms-auto" role="alert"></div>
        <?php }
        ?>
    </div>
</div>
<div class="card card-custom p-0 card-sticky " id="kt_page_sticky_card">
    <!-- Status Progress Tracker Component -->
    <?php
        echo LoanStagesController::forLoanFile($LMRId)->renderStagesHtml();
    ?>

    <div class="card-header px-2 py-2 border">
        <div class="pipelineNavigation ">
            <ul class="nav nav-pills">
                <?php

                $allowPCToMarketPlace = $myFileInfo['PCInfo']['allowPCToMarketPlace'] ?? 0;
                $allowServicing = $myFileInfo['PCInfo']['allowServicing'] ?? 0;
                $allowPCToSeeAlowareTab = $myFileInfo['PCInfo']['allowToCreateAloware'] ?? 0;
                $allowPeerstreet = $myFileInfo['PCInfo']['allowPeerstreet'] ?? 0;
                $enabledDrawManagementV2 = $myFileInfo['PCInfo']['drawManagement'] && $myFileInfo['PCInfo']['enableDrawManagementV2'] ?? 0;

                /* File tab check show or not to show - customization other than DB - START */
                if ($publicUser == 1 || $userGroup == 'Client') {
                    if (in_array($PCID, $glShowALTabForClientLogin)) { //Law offices Client portal - Show mortgage info tab and assets tab
                        // 402 - Forghany Law, P.C. PC Allow their Clients to See Assets & Liabilities in the client login

                        //$fileTabs1['AL'] = 'Assets & Liabilities'; Show Assets and Liabilities tab
                    } else {
                        if (array_key_exists('AL', $fileTabs)) unset($fileTabs['AL']);

                        if ($isSLMOnly != 1 && ($allowPCUserToSubmitCFPB == 1 || $allowCFPBAuditing == 1)) {
                            //$fileTabs1['CFPB'] 	= 'CFPB Audit'; Show CFPB Audit tab
                        } else {
                            if (array_key_exists('CFPB', $fileTabs)) unset($fileTabs['CFPB']);
                        }
                    }
                    if ($userGroup == 'Client') {

                        // Customization for PC = Vantis Law Firm (Aug 27, 2016)
                        // PC = 886 Vantis Law Firm hide the Admin & CFPB tabs

                        if (in_array($PCID, $glNotShowCFPBTabForClientLogin)) {
                            $notShowArray = ['CFPB', 'ADMIN'];
                            for ($n = 0;
                                 $n < count($notShowArray);
                                 $n++) {
                                if (array_key_exists($notShowArray[$n], $fileTabs)) {
                                    unset($fileTabs[$notShowArray[$n]]);
                                }
                            }
                        }
                    }

                    if ($publicUser == 1 && $PCID == '1399') { // PC = Debt Free, LLC hide the Doc tabs
                        if (array_key_exists('DOC', $fileTabs)) unset($fileTabs['DOC']);
                    }

                    /*Add toggle switch for workflows--> Display in borrower portal? Y/N . - PT #: 157958923 - Date June 21, 2018 */
                    if (count($PCClientWFServiceTypeArray) == 0) {
                        if (array_key_exists('CW', $fileTabs)) unset($fileTabs['CW']);
                    }
                }

                if ($isHMLO == 1 || $isEF == 1) { // Added the Tab Name changed for Hard/Private Money LO Module on Date Jan 03, 2017
                    if (array_key_exists('CI', $fileTabs)) $fileTabs['CI'] = 'Borrower Info';

                    if (array_key_exists('IE', $fileTabs)) $fileTabs['IE'] = 'Personal Inc & Exp';
                }

                // Allow to view new tab intake
                // AWATA, Dave PC, Enrollment Advisory (Client Document)

                if ((!in_array($PCID, $accessIntakeFileTabPC) || $isSLMOnly == 1) && (!in_array($PCID, $accessNewIntakeFileTabPC) || $isSLMOnly == 1)) {
                    if (array_key_exists('INT', $fileTabs)) unset($fileTabs['INT']);
                }
                if ($isSLMOnly != 1 && ($allowPCUserToSubmitCFPB == 1 || $allowCFPBAuditing == 1) && count($CFPBInfo) > 0) {
                    //$fileTabs1['CFPB'] 	= 'CFPB';
                } else {
                    if (array_key_exists('CFPB', $fileTabs)) unset($fileTabs['CFPB']);
                }

                if ($PCID == '656') {  //Pac1lending.com
                    if (array_key_exists('HR', $fileTabs)) unset($fileTabs['HR']);
                }

                if ($userRole == 'Client' && $isHMLO == 1) $userSeeBilling = 1; // PT : 152699388

                if ($userSeeBilling == 1 && $userRole != 'Auditor' && $userRole != 'CFPB Auditor' && $userRole != 'Auditor Manager') {
                    //$fileTabs2['BC'] 	= 'Billing & Com.';
                } else {
                    if (array_key_exists('BC', $fileTabs)) unset($fileTabs['BC']);
                }

                if ($allowToAccessRAM == 1) {
                    //$fileTabs2['RAM'] = 'RAM';
                } else {
                    if (array_key_exists('RAM', $fileTabs)) unset($fileTabs['RAM']);
                }

                //show Credit Memo Tab for Loan Officers
                if ($userGroup == glUserGroup::AGENT && !PageVariables::$externalBroker) {
                    if (array_key_exists('MEMO', $fileTabs)) unset($fileTabs['MEMO']);
                }

                /* File tab check show or not to show - customization other than DB - END */

                /* File tab check show or not to show - if fields are disabled in BO START*/
                if ($activeTab == 'QAPP') {
                    $fileTab = 'QA';
                } else if ($activeTab == 'LI') {
                    $fileTab = 'FA';
                } else {
                    $fileTab = 'BO';
                }
                if ($activeTab == 'QAPP' || $activeTab == 'LI' || $activeTab == 'CI') {
                    $fileTypeArray = $moduleRequested;

                } else {
                    $fileTypeArray = $fileModuleInfo;
                }

                if(array_key_exists('BD', $fileTabs) && $enabledDrawManagementV2) {
                    $fileTabs['BD'] = 'Draw Management';
                }

                /* Get Branch Modules Keys */
                $aCm = '';
                for ($ct = 0;
                     $ct < count($fileTypeArray);
                     $ct++) {
                    $fileCT .= $aCm . trim($fileTypeArray[$ct]['moduleCode']);
                    $aCm = ',';
                }
                /* Get File Modules Keys */
                $aCm = '';
                for ($ct = 0;
                     $ct < count($fileModuleInfo);
                     $ct++) {
                    $fileMC[] = trim($fileModuleInfo[$ct]['moduleCode']);
                }
                $fileTypesTxt = implode(',', $fileMC);
                /*get file level loan program*/

                for ($ct = 0;
                     $ct < count($LMRClientTypeInfo);
                     $ct++) {
                    $fileLP[] = trim($LMRClientTypeInfo[$ct]['ClientType']);
                }

                $lpmsg = '';

                $lphQry = 'SELECT * FROM tblLMRClientType_h WHERE recordDate = (
SELECT  MAX(recordDate) FROM tblLMRClientType_h WHERE lmrid =   ' . $LMRId . ')';
                $lphresultArray = Database2::getInstance()->fetchRecords(['qry' => $lphQry]);

                if (count($fileLP) < 0 && $LMRId > 0) {
                    if (count($lphresultArray) <= 0) {
                        $fileLP[0] = 'TBD';
                        $lpmsg = 'Loan Program of file has been deleted so by default is assigned to "TBD" 1';
                    } else {
                        $LMRClientTypeInfo = $lphresultArray;
                        for ($ct = 0; $ct < count($LMRClientTypeInfo); $ct++) {
                            $fileLP[] = trim($LMRClientTypeInfo[$ct]['ClientType']);
                        }
                        $lpmsg = 'Loan Program of file has been deleted so assigned to last known Loan Program ';
                    }
                    alertLoanProgramNull::getReport(['msg' => $lpmsg, 'LMRID' => $LMRId, 'loanProgram' => $fileLP[0]]);
                } else if (($fileLP[0] ?? '') == '' && $LMRId > 0) {
                    if (count($lphresultArray) <= 0) {
                        $fileLP[0] = 'TBD';
                        $lpmsg = 'Loan Program of file has been deleted so by default is assigned to "TBD" 2';
                    } else {
                        $LMRClientTypeInfo = $lphresultArray;
                        for ($ct = 0; $ct < count($LMRClientTypeInfo); $ct++) {
                            $fileLP[] = trim($LMRClientTypeInfo[$ct]['ClientType']);
                            $lpmsg = 'Loan Program of file has been deleted so assigned to last known Loan Program ';
                        }
                    }
                    alertLoanProgramNull::getReport(['msg' => $lpmsg, 'LMRID' => $LMRId, 'loanProgram' => $fileLP[0]]);
                }

                /*get branch level loan program*/
                $aCm = '';
                for ($ct = 0;
                     $ct < count($servicesRequested);
                     $ct++) {
                    $fileCLP .= $aCm . trim($servicesRequested[$ct]['LMRClientType']);
                    $aCm = ',';
                }

                $glLMRId = $LMRId;

                $fieldsInfo = getAppFormFields::getReport([
                    'assignedPCID' => $PCID,
                    'fTArray'      => $fileTypeArray,
                    'myOpt'        => $fileTab,
                    'activeTab'    => $activeTab,
                ]);

                loanForm::init(
                    $PCID,
                    $fileTab,
                    cypher::myEncryption($LMRId),
                    (int)$LMRId,
                    $fileMC,
                    getFileInfo::$fileLoanPrograms,
                    getFileInfo::$fileInternalLoanPrograms,
                    getFileInfo::$fileAdditionalLoanPrograms
                );

                //Hide Credit Memo Tab if no fields are selected to show in BO
                /*if(count(sectionAccess(array('sId' => 'MEMO', 'opt' => 'BO'))) == 0) {
                    unset($fileTabs['MEMO']);
                }*/
                /* File tab check show or not to show - if fields are disabled in BO END */

                /*
                ** Description	: Hide the file tab for Client portal
                ** Date			: Sep 28, 2017
                ** devloper		: Viji, Venky
                */

                if ($userGroup == 'Client' && ($isLM == 1 || $showSSTab == 1 || $isMF == 1 || $isLO == 1 || $isLSR == 1 || $isSLMOnly == 1)) {
                    if (array_key_exists('CON', $fileTabs)) unset($fileTabs['CON']);
                    if (array_key_exists('HUD', $fileTabs)) unset($fileTabs['HUD']);
                    if (array_key_exists('ADMIN', $fileTabs)) unset($fileTabs['ADMIN']);
                }

                if (!glCustomJobForProcessingCompany::showLoanTermsToBusinessFunding($PCID) && $ftModuleCode == 'loc') { //2057 customization (ticket is sc-30749) **exception**
                    unset($fileTabs['LT']);
                }

                if (!in_array($PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
                    unset($fileTabs['DS']);
                }
                if (!count(BaseHTML::sectionAccess2(['sId' => 'EF', 'opt' => 'BO']))) {
                    if (array_key_exists('EF', $fileTabs)) unset($fileTabs['EF']);
                }


                $fileTabsCnt = count($fileTabs);

                if ($fileTabsCnt > 0) $fileTabsKey = array_keys($fileTabs);

                /* Show & Hide the File tabs as per the selected modules - Viji On Jan 2, 2016 */

                $goToTabIndex = 0;
                $goToTab = 'CI';
                $lastIndex = 'BC';
                if ($fileTabsCnt > 0) {
                    $goToTabIndex = array_search($activeTab, $fileTabsKey);

                    if (isset($fileTabsKey[$goToTabIndex + 1])) {
                        $goToTabIndex = $goToTabIndex + 1;
                    }

                    $goToTab = $fileTabsKey[$goToTabIndex];
                    $lastIndex = end($fileTabsKey);
                }

                $eId = null;
                $rId = null;
                $lId = null;

                if (LMRequest::File()->LMRId) {
                    $eId = cypher::myEncryption(LMRequest::File()->getTblFileResponse_by_LMRId()->executiveId);
                    $rId = cypher::myEncryption(LMRequest::File()->getTblFileResponse_by_LMRId()->LMRResponseId);
                    $lId = cypher::myEncryption(LMRequest::File()->LMRId);
                }

                $params = 'eId=' . $eId . '&lId=' . $lId . '&rId=' . $rId;

                for ($f = 0;
                     $f < count($fileTabsKey);
                     $f++) {
                    $icondivCls = '';
                    if ($fileTabsKey[$f] == $activeTab) {
                        if (($PCID == glPCID::PCID_MORTGAGE_ASSISTANCE) && (in_array($fileTabsKey[$f], ['CI', 'MI', 'IE', 'HA', 'AL', 'BC', 'PI', 'ADMIN']))) {
                            $divCls = ' active text-danger statusTabActive_1 ';
                            $icondivCls = ' text-white ';
                        } else {
                            $divCls = 'active statusTabActive';
                            $icondivCls = ' text-white ';
                        }
                        $newHref = '';
                    } else {
                        if (($PCID == glPCID::PCID_MORTGAGE_ASSISTANCE) && (in_array($fileTabsKey[$f], ['CI', 'MI', 'IE', 'HA', 'AL', 'BC', 'PI', 'ADMIN']))) {
                            $divCls = ' text-danger ';
                        } else {
                            $divCls = ' text-dark-65 ';
                        }
                        if ($isHMLO == 1 && ($userGroup == 'Employee' || $userGroup == 'Super')) {
                            /* Mandatory field for "check list status" drop down, if file is HMLO Module On May 23, 2017 */
                            /* Mandatory field Validation only for User Group "Employee And Super Admin" on June 08, 2017 */
                            if ($allowToEdit && $activeTab != 'CW' && $activeTab != 'TA' && $activeTab != 'CON') $newHref = "onclick=\"javascript:submitAndNavigateTab('" . $fileTabsKey[$f] . "');\"";
                            else                $newHref = "onclick=\"javascript:goToNextTab('" . $fileTabsKey[$f] . "');\"";
                        } else {
                            if ($allowToEdit && $activeTab != 'DOC' && $activeTab != 'TA' && $activeTab != 'CON' && $activeTab != 'CW' && $activeTab != 'CFPB') $newHref = "onclick=\"javascript:submitAndNavigateTab('" . $fileTabsKey[$f] . "');\"";
                            else                $newHref = "onclick=\"javascript:goToNextTab('" . $fileTabsKey[$f] . "');\"";
                        }
                    }
                    if ($userGroup == 'Client') {
                        $fileTabs['BC'] = 'Billing';
                    }
                    if ($fileTabsKey[$f] == 'MP') {
                        if ($allowPCToMarketPlace == '0' || $allowToViewMarketPlace == '0') {
                            $divClsBackup = $divCls;
                            $divCls .= ' d-none';
                        }
                    }
                    if ($fileTabsKey[$f] == 'HR') {
                        if ($subscribePCToHOME == '0' || $subscribeToHOME == '0') {
                            $divClsBackup = $divCls;
                            $divCls .= ' d-none';
                        }
                    }
                    if ($fileTabsKey[$f] == 'AW') {
                        if ($allowToSeeAlowareTab == '0' || $allowPCToSeeAlowareTab == '0') {
                            $divClsBackup = $divCls;
                            $divCls .= ' d-none';
                        }
                    }
                    if ($fileTabsKey[$f] == 'PE') {
                        if ($allowPeerstreet == '0') {
                            $divClsBackup = $divCls;
                            $divCls .= ' d-none';
                        }
                    }
                    if ($fileTabsKey[$f] == 'SER2') {
                        if ($allowServicing == '0' || ($userGroup != 'Employee' && $userGroup != 'Super')) {
                            if ($userGroup == 'Agent' && PageVariables::$CurrentAgent->allowServicing) {
                                doNothing();
                            } else {
                                $divClsBackup = $divCls;
                                $divCls .= ' d-none';
                            }
                        }
                    }

                    //permission to view, Submit Offer Tab value assigned in getPageVariables.php
                    if ($fileTabsKey[$f] == 'SO') {
                        if ($viewSubmitOfferTab != 1) {
                            $divClsBackup = $divCls;
                            $divCls .= ' d-none';
                        }
                    }

                    if ($fileTabsKey[$f] == 'PEN') {
                        if ($pcPriceEngineStatus == 0 || $userPriceEngineStatus == 0) {
                            $divClsBackup = $divCls;
                            $divCls .= ' d-none';
                        }
                    }
                    if ($userGroup == 'Agent' && $allowAgentWorkflowEdit == 0 && $fileTabsKey[$f] == 'CW') {
                        $divClsBackup = $divCls;
                        $divCls .= ' d-none';
                    }
                    ?>

                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="<?php echo $fileTabs[$fileTabsKey[$f]] ?>"
                        id="tab_<?php echo $fileTabsKey[$f]; ?>">
                        <span
                                class="nav-link px-2 loanFileButtonLink <?php echo $divCls ?>"
                            <?php echo $newHref ?> style='cursor:pointer'>
                            <?php echo $fileTabs[$fileTabsKey[$f]] ?>
                            <i class="ml-2  <?php echo $icondivCls;
                            if (isset($fileTabIcons[$fileTabsKey[$f]])) {
                                echo $fileTabIcons[$fileTabsKey[$f]];
                            } else {
                                echo 'fa fa-list ';
                            } ?>"></i>
                        </span>
                    </li>
                    <?php
                    if ($userGroup == 'Client') $tabsOpt = '';
                    else if ($userSeeBilling == 1 && $userRole != 'Auditor' && $userRole != 'CFPB Auditor' && $userRole != 'Auditor Manager') $tabsOpt = 'BC';
                    else if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager') $tabsOpt = 'LE';
                    else                      $tabsOpt = 'ADMIN';
                    if (($PCID == 2 || $PCID == 820 || $PCID == 1379) && $isSLMOnly == 1) { /* 1379 - fresh start student LLC */
                        $tabsOpt = '';
                    }

                    if ($fileTabsKey[$f] == $lastIndex) { ?>
                    <?php }
                }
                ?>

                <?php if (!glCustomJobForProcessingCompany::hideFannieMaeTab($PCID) && $LMRId) { ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton
                    <?php if (PageVariables::$userGroup == 'Client' || $PCID == glPCID::PCID_MORTGAGE_ASSISTANCE) {
                        echo 'd-none';
                    } ?>" data-html="true"
                        title="Fannie Mae"
                        id="tab_fm34">
                        <a
                                class="nav-link px-2 loanFileButtonLink <?php echo $activeTab == 'fm34' ? 'active statusTabActive' : 'text-dark-65'; ?>"
                                style='cursor:pointer'
                                data-lmrid="<?php echo htmlspecialchars(Request::GetClean('lId')); ?>"
                                href="/backoffice/loan/fm34?<?php echo $params; ?>&tabOpt=fm34&op=<?php echo cypher::myEncryption($op); ?>">
                            Fannie Mae
                            <i class="ml-2 fa fa-list"></i>
                        </a>
                    </li>
                <?php } ?>

                <?php if (LMRequest::$hasChangeLog && $PCID != glPCID::PCID_MORTGAGE_ASSISTANCE) { ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton
                    <?php if ($userGroup == 'Client') {
                        echo 'd-none';
                    } ?>" data-html="true"
                        title="Change Log"
                        id="tab_change_log">
                    <span
                            class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'
                            data-lmrid="<?php echo htmlspecialchars(Request::GetClean('lId')); ?>"
                            onclick="submitAndNavigateTab('CL');">
                        Change Log
                        <i class="ml-2 fa fa-list"></i>
                    </span>
                    </li>
                <?php } ?>

                <?php if (LMRequest::File()->LMRId) { ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton
                    <?php if (stristr(PageVariables::$userEmail, '@lendingwise.com') === false) {
                        echo 'd-none';
                    } ?>" data-html="true"
                        title="File Copies"
                        id="tab_file_copies">
                        <a
                                class="nav-link px-2 loanFileButtonLink text-dark-65"
                                style='cursor:pointer'
                                href="<?php echo LMRequest::File()->publicURL('copies', false); ?>">
                            Copies
                            <i class="ml-2 fa fa-list"></i>
                        </a>
                    </li>
                <?php } ?>

                <?php if (SHOW_DEBUG_TOOLS || Request::GetClean('show_rule_check') ?? null || PageVariables::PC()->allowAutomatedRulesV2) { ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Rule Check"
                        id="tab_rule_check">
                        <span
                                class="nav-link px-2 loanFileButtonLink"
                                onclick="submitAndNavigateTab('RC');" style='cursor:pointer'>
                            Rule Check
                            <i class="ml-2 fa fa-check"></i>
                        </span>
                    </li>
                <?php } ?>
                <?php if (SHOW_DEBUG_TOOLS) { ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Clear Loan Form"
                        id="clear_loan_form">
                    <span
                            class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'
                            onclick="debugTools.clearForm('loanModForm');">
                        Clear Loan Form
                        <i class="ml-2 fa fa-eraser"></i>
                    </span>
                    </li>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Fill Loan Form A"
                        id="fill_loan_form">
                    <span
                            class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'
                            onclick="debugTools.fillForm('loanModForm');">
                        Fill Loan Form A
                        <i class="ml-2 fa fa-edit"></i>
                    </span>
                    </li>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                        title="Fill Loan Form A"
                        id="fill_loan_form">
                    <span
                            class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'
                            onclick="debugTools.fillForm('loanModForm', true);">
                        Fill Loan Form B
                        <i class="ml-2 fa fa-edit"></i>
                    </span>
                    </li>
                <?php } ?>

                <?php if (LMRequest::File()->LMRId) { ?>
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 btn loanFileButton" data-html="true"
                    title="Open/Close All Sections"
                    data-action="open"
                    onclick="globalJS.toggleSections(this);">
                    <span class="nav-link px-2 loanFileButtonLink text-dark-65"
                            style='cursor:pointer'>
                       Open/Close All Sections
                        <i class="ml-2 ki ki-arrow-down icon-nm"></i>
                    </span>
                </li>
                <?php } ?>
            </ul> <!-- tab Container -->
        </div>

    </div>
    <div class="card-body p-0">
        <?php
        if ($activeTab == 'REST') {
            if (count($myFileInfo) > 0) {
                $PCPermissonToREST = Strings::showField('subscribeToREST', 'PCInfo');
                $branchApprovedByREST = Strings::showField('RESTApproval', 'BranchInfo');
            }
            if (($PCPermissonToREST == 1 && $branchApprovedByREST == 1 && $permissionToREST == 1) || ($userRole == 'Super') || ($userRole == 'REST')) {
                $subscribeToREST = 1;
            } else {
                $subscribeToREST = 0;
            }
        }
        if ($subscribeToREST == 1 && $activeTab == 'REST') {
            $RESTSF = CONST_URL_BOSSL . 'NPVSave.php'; // change log
            $RESTFN = 'NPVForm.php';

        } else {
            $RESTSF = 'javascript:void(0);';
            $RESTFN = 'NPVFormDemo.php';
        }

        $HRSF = 'javascript:void(0);';
        $HRFN = 'homeReportForm.php';

        if (($subscribeToHOME == 1 && $subscribePCToHOME == 1) || $userGroup == 'Super') {
            $HRFN = 'homeReportForm.php';
            $HRSF = CONST_URL_BOSSL . 'homeReportSave.php'; // change log
        } else {
            $HRSF = 'javascript:void(0);';
            $HRFN = 'homeReportDemoPage.php';
        }

        /** The forms array contain the following info.
         ** FN = Form / File Name (to be included), SF = Save File (file to which data is posted), JS = JS validation function
         **/

        if ($publicUser == 1) {
            $forms = [
                'CI'  => ['FN' => __DIR__ . '/../clientInfoForm.php', 'SF' => CONST_URL_BOSSL . 'clientInfoSave.php', 'JS' => ' ( validateLoanForm() && validateQuickAndLongAppFormsHMLI() )'], // change log // trigger
                'IE'  => ['FN' => __DIR__ . '/../incExpForm.php', 'SF' => CONST_URL_BOSSL . 'IncExpSave.php', 'JS' => ''], // change log // trigger
                'AL'  => ['FN' => __DIR__ . '/../assetLiabilityForm.php', 'SF' => CONST_URL_BOSSL . 'assetLiabilitySave.php', 'JS' => ''], // change log // trigger
                'HA'  => ['FN' => __DIR__ . '/../hardshipForm.php', 'SF' => CONST_URL_BOSSL . 'hardshipSave.php', 'JS' => ''], // change log // trigger
                'QA'  => ['FN' => __DIR__ . '/../QAForm.php', 'SF' => CONST_URL_BOSSL . 'QASave.php', 'JS' => 'validateQAInfo()'], // change log // trigger
                'DOC' => ['FN' => __DIR__ . '/../DocsForm.php', 'SF' => CONST_URL_BOSSL . 'DocsSave.php', 'JS' => ''], // change log // trigger
            ];
        } else {
            if ($userGroup == 'Client') {
                $validationName = '( validateQuickAndLongAppFormsHMLI() && validateBorrowerFormClientPortal() && validateMinMaxLoanGuidelines() )';
            } else {
                if ($allowAutomation) {
                    $validAdminForm = '( validateFileAdminForm() && LMRequest.checkLoanNumberExists() && allowAutomationRuleRepeat() )';
                    $validationName = '( validateFileTypeAndLoanProgram()   && LMRequest.checkLoanNumberExists() && validateClientInfoForm() && validateMinMaxLoanGuidelines() && allowAutomationRuleRepeat() )';
                    $validationLoanInfoTab = ' ( validateQuickAndLongAppFormsHMLI() && LMRequest.checkLoanNumberExists() && loanCalculation.checkLoanTermViolation() && allowAutomationRuleRepeat() )';
                    $validationPropertyInfoTab = ' ( validatePropertyInfoForm() && allowAutomationRuleRepeat() )';
                    $validationDealAnalysisTab = 'allowAutomationRuleRepeat()';
                    $validationHMDATab = 'allowAutomationRuleRepeat()';
                    $validationServicingTab = 'allowAutomationRuleRepeat()';
                    $validationIncExpTab = '( validateIncomeExpForm() && allowAutomationRuleRepeat() )';
                    $validationAssetLiabilityTab = 'allowAutomationRuleRepeat()';
                    $validationBillCommTab = '( validateBillingForm() && allowAutomationRuleRepeat() )';
                    $validationAdverseActionTab = 'allowAutomationRuleRepeat()';
                    $validationCreditDecisionTab = 'allowAutomationRuleRepeat()';
                } else {
                    $validAdminForm = '( validateFileAdminForm() && LMRequest.checkLoanNumberExists() )';
                    $validationName = '( validateFileTypeAndLoanProgram()   && LMRequest.checkLoanNumberExists() && validateClientInfoForm() && validateMinMaxLoanGuidelines()  )';
                    $validationLoanInfoTab = ' ( validateQuickAndLongAppFormsHMLI()  && LMRequest.checkLoanNumberExists() && loanCalculation.checkLoanTermViolation() )';
                    $validationPropertyInfoTab = ' validatePropertyInfoForm() ';
                    $validationDealAnalysisTab = '';
                    $validationHMDATab = '';
                    $validationServicingTab = '';
                    $validationIncExpTab = 'validateIncomeExpForm()';
                    $validationAssetLiabilityTab = '';
                    $validationBillCommTab = 'validateBillingForm()';
                    $validationAdverseActionTab = '';
                    $validationCreditDecisionTab = '';
                }
            }
            $forms = [
                'CI'    => ['FN' => 'clientInfoForm.php', 'SF' => CONST_URL_BOSSL . 'clientInfoSave.php', 'JS' => $validationName], // change log // trigger
                'PI'    => ['FN' => 'propertyInfoForm.php', 'SF' => CONST_URL_BOSSL . 'propertyInfoSave.php', 'JS' => $validationPropertyInfoTab], // change log
                'IE'    => ['FN' => 'incExpForm.php', 'SF' => CONST_URL_BOSSL . 'IncExpSave.php', 'JS' => $validationIncExpTab], // change log // trigger
                'HA'    => ['FN' => 'hardshipForm.php', 'SF' => CONST_URL_BOSSL . 'hardshipSave.php', 'JS' => ''], // change log // trigger
                'QA'    => ['FN' => 'QAForm.php', 'SF' => CONST_URL_BOSSL . 'QASave.php', 'JS' => 'validateQAInfo()'], // change log // trigger
                'REST'  => ['FN' => $RESTFN, 'SF' => $RESTSF, 'JS' => ''], // change log
                'LP'    => ['FN' => 'LMProposalForm.php', 'SF' => CONST_URL_BOSSL . 'LMProposalSave.php', 'JS' => ''], // change log // trigger
                'CON'   => ['FN' => 'contactInfoForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''],
                'SS'    => ['FN' => 'SSForm.php', 'SF' => CONST_URL_BOSSL . 'SSSave.php', 'JS' => 'validateShorSaleForm()'], // change log // trigger
                'SP'    => ['FN' => 'SSProposalForm.php', 'SF' => CONST_URL_BOSSL . 'SSProposalSave.php', 'JS' => ''], // change log // trigger
                'HUD'   => ['FN' => 'HUDForm.php', 'SF' => CONST_URL_BOSSL . 'HUDSave.php', 'JS' => 'LMRequest.checkLoanNumberExists()'], // change log // trigger
                'AL'    => ['FN' => 'assetLiabilityForm.php', 'SF' => CONST_URL_BOSSL . 'assetLiabilitySave.php', 'JS' => $validationAssetLiabilityTab], // change log // trigger
                'DOC'   => ['FN' => 'DocsForm.php', 'SF' => CONST_URL_BOSSL . 'DocsSave.php', 'JS' => ''], // change log // trigger
                'TA'    => ['FN' => 'taskForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''],
                'ADMIN' => ['FN' => 'fileAdminForm.php', 'SF' => CONST_URL_BOSSL . 'fileAdminSave.php', 'JS' => $validAdminForm], // change log // trigger
                'LE'    => ['FN' => 'legalForm.php', 'SF' => CONST_URL_BOSSL . 'legalSave.php', 'JS' => ''], // change log // trigger
                'MI'    => ['FN' => 'mortgageInfoForm.php', 'SF' => CONST_URL_BOSSL . 'mortgageInfoSave.php', 'JS' => ''], // change log // trigger
                'NOI'   => ['FN' => 'adverseAction.php', 'SF' => CONST_URL_BOSSL . 'adverseActionSave.php', 'JS' => $validationAdverseActionTab], // change log // trigger
                'CD'    => ['FN' => 'creditDecisionForm.php', 'SF' => CONST_URL_BOSSL . 'creditDecisionSave.php', 'JS' => $validationCreditDecisionTab],
            ];

            if (glCustomJobForProcessingCompany::showLoanInfoV2($PCID)) {
                $forms['LIV2'] = ['FN' => 'LMRequest/loanInfoV2/loanInfoV2Form.php', 'SF' => CONST_URL_BOSSL . 'loanInfoV2Save.php', 'JS' => ''];
            }
           // if (PageVariables::$allowPCToViewPostClosingTab) {
                $forms['PCLT'] = ['FN' => 'LMRequest/postClosing/postClosingForm.php', 'SF' => CONST_URL_BOSSL . 'postClosingSave.php', 'JS' => ''];
            //}


            if ($userGroup != 'Agent' || ($userGroup == 'Agent' && $allowAgentWorkflowEdit == 1)) {
                $forms['CW'] = ['FN' => 'CWForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''];
            }
            /*
             * Allow to view new tab intake
             * AWATA, Dave PC, Enrollment Advisory (Client Document)
             */
            if (in_array($PCID, $accessIntakeFileTabPC)) {
                $forms['INT'] = ['FN' => 'clientDocumentIntakeForm.php', 'SF' => CONST_URL_BOSSL . 'clientDocumentIntakeFormSave.php', 'JS' => 'validateClientDocumentIntakeForm()']; // change log // trigger
            }

            /* PC = Loscalzo & Associates */
            if (in_array($PCID, $accessNewIntakeFileTabPC)) {
                $forms['INT'] = ['FN' => 'loscalzoIntakeForm.php', 'SF' => CONST_URL_BOSSL . 'loscalzoIntakeFormSave.php', 'JS' => 'validateLoscalzoIntakeForm()']; // change log // trigger
            }

            /*
             * CFPB Audit tab
             */
            if ($isSLMOnly != 1 && ($allowPCUserToSubmitCFPB == 1 || $allowCFPBAuditing == 1) && count($CFPBInfo) > 0) {
                $forms['CFPB'] = ['FN' => 'CFPBAuditForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''];
            }

            if ($isSLMOnly == 1) {
                unset($forms['LE']);
            }

            if ($userSeeBilling == 1 && $userRole != 'Auditor') {
                $forms['BC'] = ['FN' => 'BCForm.php', 'SF' => CONST_URL_BOSSL . 'BCSave.php', 'JS' => $validationBillCommTab]; // change log // trigger
            }

            if ($allowToAccessRAM == 1) {
                $forms['RAM'] = ['FN' => 'RAMFileForm.php', 'SF' => CONST_URL_BOSSL . 'RAMFileInfoSave.php', 'JS' => 'validateRAMFileForm()']; // change log // trigger
            }

            if ($userRole == 'Auditor') {
                unset($forms['CW']);
                unset($forms['REST']);
            }
            if ($PCID == '656') {
                /** Pac1lending.com **/
                unset($forms['HR']);
            }
            if ($isSLM == 1 || $isSLMOnly == 1) {
                $forms['SLM'] = ['FN' => 'SLMForm.php', 'SF' => CONST_URL_BOSSL . 'SLMSave.php', 'JS' => 'validateSLMInfoForm()']; // change log // trigger
            }
        }

        if ($isHOALien == 1) {
            $forms['ER'] = ['FN' => 'estimatedRepairForm.php', 'SF' => CONST_URL_BOSSL . 'estimatedRepairSave.php', 'JS' => '']; // change log // trigger
        }

        /* Loan Origination File - Show Explanation tab */

        if ($isLO == 1) {/*  This tab is visible only for loan origination files not for loan mod */
            $forms['EXP'] = ['FN' => 'explanationForm.php', 'SF' => CONST_URL_BOSSL . 'explanationSave.php', 'JS' => '']; // change log // trigger
        }

        /* Merchant Funding File - Show Merchant Offers tab (March 26,2016) */

        if ($isMF == 1 || (glCustomJobForProcessingCompany::showLoanTermsToBusinessFunding($PCID) && $ftModuleCode == 'loc')) { //2057 customization (ticket is sc-30749) **exception**
            $forms['MFO'] = ['FN' => 'merchantFundingOfferForm.php', 'SF' => CONST_URL_BOSSL . 'merchantFundingOfferSave.php', 'JS' => '']; // does not exist
            $forms['LT'] = ['FN' => 'MFLoanTerms.php', 'SF' => CONST_URL_BOSSL . 'MFLoanTermsSave.php', 'JS' => '']; // empty file
        }

        /* Short sale File - Show Dash tab */
        if ($showSSTab == 1) {
            $forms['DASH'] = ['FN' => 'SSDashForm.php', 'SF' => CONST_URL_BOSSL . 'SSDashSave.php', 'JS' => '']; // change log // trigger
        }
        $forms['SSS'] = ['FN' => 'emeraldQuickForm.php', 'SF' => CONST_URL_BOSSL . 'saveEmeraldQuickForm.php', 'JS' => '']; // change log // trigger

        /* Loan servicing File - Show summary tab (Sep 23, 2016.) */
        if ($isLSR == 1) {
            $forms['LSS'] = ['FN' => 'loanServicingSummaryForm.php', 'SF' => CONST_URL_BOSSL . 'loanServicingSummarySave.php', 'JS' => '']; // change log // trigger
        }
        if ($allowPCToMarketPlace == 1) {
            if ($allowToViewMarketPlace == 1) {
                $forms['MP'] = ['FN' => 'marketPlaceLoanForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''];
            }
        }

        if ($subscribePCToHOME == 1) {
            if ($subscribeToHOME == 1) {
                $forms['HR'] = ['FN' => $HRFN, 'SF' => $HRSF, 'JS' => ''];
            }
        }
        if ($allowPeerstreet == 1) {
            $forms['PE'] = ['FN' => 'peerStreet.php', 'SF' => null, 'JS' => 'validateHMLOWebForm()']; // change log // trigger

        }

        if ($pcPriceEngineStatus > 0 && $userPriceEngineStatus > 0) {
            $forms['PEN'] = ['FN' => 'filePricingEngine.php', 'SF' => '', 'JS' => ''];
        }

        if ($allowServicing == 1) {
            $forms['SER2'] = ['FN' => 'servicingForm.php', 'SF' => 'javascript:void(0);', 'JS' => ''];
        }

        if ($allowPCToSeeAlowareTab == 1) {
            if ($allowToSeeAlowareTab == 1) {
                $forms['AW'] = ['FN' => 'alowarePage.php', 'SF' => '', 'JS' => ''];
            }
        }

        /** New Hard / Private Money LOS module on (Nov 21, 2016) **/
        //if ($isHMLO == 1) {
        $forms['LI'] = ['FN' => 'HMLOLoanInfoForm.php', 'SF' => CONST_URL_BOSSL . 'HMLOLoanInfoSave.php', 'JS' => '']; // change  // trigger
        $forms['1003'] = ['FN' => 'HMLO1003InfoForm.php', 'SF' => CONST_URL_BOSSL . 'HMLO1003InfoSave.php', 'JS' => '']; // change log // trigger
        $forms['SER'] = ['FN' => 'HMLOServicingForm.php', 'SF' => CONST_URL_BOSSL . 'HMLOServicingSave.php', 'JS' => $validationServicingTab]; // change log // trigger
        $forms['HMLI'] = ['FN' => 'HMLONewLoanInfoForm.php', 'SF' => CONST_URL_BOSSL . 'HMLONewLoanInfoSave.php', 'JS' => $validationLoanInfoTab]; // change log // trigger
        $forms['DA'] = ['FN' => 'dealAnalysisInfoForm.php', 'SF' => CONST_URL_BOSSL . 'dealAnalysisSave.php', 'JS' => $validationDealAnalysisTab]; // change log // trigger
        $forms['GOVT'] = ['FN' => 'govtMntrInfoForm.php', 'SF' => CONST_URL_BOSSL . 'govtMntrInfoSave.php', 'JS' => $validationHMDATab]; // change log // trigger
        $forms['TPS'] = ['FN' => 'thirdPartyServiceInfoForm.php', 'SF' => CONST_URL_BOSSL . 'saveThirdPartyServiceInfo.php', 'JS' => 'validateHMLOWebForm()']; // change log // trigger
        $forms['QAPP'] = ['FN' => 'HMLOLoanInfoForm.php', 'SF' => CONST_URL_BOSSL . 'HMLOLoanInfoSave.php', 'JS' => '']; // change log // trigger

        if ($enabledDrawManagementV2) {
            $forms['BD'] = ['FN' => 'drawManagement/loanFile/drawManagement.php', 'SF' => '', 'JS' => ''];
        } else {
            $forms['BD'] = ['FN' => 'budgetAndDrawsInfo.php', 'SF' => CONST_URL_BOSSL . 'saveBudgetAndDraws.php', 'JS' => ''];
        }

        /* Equipment Financing - EF Module - Selected in the file (PT-#155682423) - April 18, 2018 */
        //if ($isEF == 1) {
        $forms['EF'] = ['FN' => 'equipmentFinancingForm.php', 'SF' => CONST_URL_BOSSL . 'equipmentFinancingSave.php', 'JS' => '']; // change log // trigger
        //}

        /**
         * Description    : Added the New tab for "Entity Info" in Business Funding Modules
         * Date        : Dec 07, 2017
         **/
        //if ($isMF == 1) {
        $forms['FUEI'] = ['FN' => 'fundingEntityInfoForm.php', 'SF' => CONST_URL_BOSSL . 'fundingEntityInfoSave.php', 'JS' => '']; // change log // trigger
        $forms['FUCE'] = ['FN' => 'fundingCreditEnhancementForm.php', 'SF' => CONST_URL_BOSSL . 'fundingCreditEnhancementSave.php', 'JS' => '']; // change log // trigger
        //}


        if (in_array($PCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
            $forms['DS'] = [
                'FN' => 'dealSizerForm.php',
                'SF' => CONST_URL_BOSSL . 'dealSizerSave.php', // change log // trigger
                'JS' => '',
            ];
        }
        /*
         * Allow to view new tab intake
         * AWATA, Dave PC, Enrollment Advisory (Client Document)
         */

        if ($viewSubmitOfferTab == 1) {
            /* Submit Offer tab */
            $forms['SO'] = ['FN' => 'LMRequest/sections/submitOffer.php', 'SF' => CONST_URL_BOSSL . 'submitOfferSave.php', 'JS' => '']; // change log // trigger
            //Stay on the same tab
            if ($activeTab == 'SO') {
                $goToTab = 'SO';
            }
        }

        $forms['MEMO'] = ['FN' => 'creditMemoSection.php', 'SF' => CONST_URL_BOSSL . 'creditMemoSave.php', 'JS' => ' LMRequest.validateCreditMemo()']; // change log // trigger
        //Stay on the same tab
        if ($activeTab == 'MEMO') {
            $goToTab = 'MEMO';
        }
        if (in_array($PCID, $accessIntakeFileTabPC) || in_array($PCID, $accessNewIntakeFileTabPC)) {
            $nextTab['INT'] = 'INT';
        }

        $nextTab['CFPB'] = 'CFPB';
        $nextTab['RAM'] = 'RAM';

        $agentAndBranchAsLabel = 0;
        if ($userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager' || ($allowCFPBAuditing == 1 && $allowUserToUpdateCFPBFile == 1)) {
            $agentAndBranchAsLabel = 1;
        }
        $formAction = 'javascript:void(0)';
        $jsAction = '';
        if (array_key_exists($activeTab, $forms)) {
            $formAction = $forms[$activeTab]['SF'];
            $jsAction = $forms[$activeTab]['JS'];
        }
        $forms['RC'] = [
            'FN' => '/LMRequest/sections/rule_check.php',
        ];
        if (LMRequest::$hasChangeLog) {
            $forms['CL'] = [
                'FN' => 'changeLog.php',
            ];
        }

        $jsAction = ' LMRequest.validateDates() ' . ($jsAction ? ' && ' . $jsAction : '');
        //LMRequest.validateDates()

        ?>
        <div class="card card-custom card-stretch d-flex p-0" id="FileFormBody">
            <div class="card-body p-0">
                <form name="loanModForm" id="loanModForm" class="form-horizontal" enctype="multipart/form-data"
                      method="POST"
                      novalidate
                      action="<?php echo $formAction ?>" <?php if (trim($jsAction) != '') { ?>
                    onsubmit="return <?php echo $jsAction ?>;" <?php } ?> >
                    <input type="hidden" id="submitStatus" value="0">
                    <input type="hidden" name="generatePDF" id="generatePDF" value="">
                    <input type="hidden" name="fileTabNew" id="fileTab" value="<?php echo $fileTab; ?>">
                    <input type="hidden" id="pageLoad" value='No'/>
                    <input type="hidden" name="editOpt" value="<?php echo $editOpt ?>"/>
                    <input type="hidden" name="copyOpt" value="<?php echo $copyOpt ?>"/>
                    <input type="hidden" name="encryptedRId"
                           value="<?php echo cypher::myEncryption($LMRResponseId) ?>"/>
                    <input type="hidden" name="encryptedEId" value="<?php echo cypher::myEncryption($executiveId) ?>"/>
                    <input type="hidden" name="encryptedLId" id="encryptedLId"
                           value="<?php echo cypher::myEncryption($LMRId) ?>">
                    <input type="hidden" name="LMRId" id="fLMRId" value="<?php echo $LMRId ?>">
                    <input type="hidden" name="encryptedBId"
                           value="<?php echo cypher::myEncryption($brokerNumber) ?>"/>
                    <input type="hidden" name="encryptedSBId"
                           value="<?php echo cypher::myEncryption($secondaryBrokerNumber) ?>"/>
                    <input type="hidden" name="encryptedPCID" id="encryptedPCID"
                           value="<?php echo cypher::myEncryption($PCID) ?>"/>
                    <input type="hidden" name="FPCID" id="FPCID"
                           value="<?php echo Strings::showField('FPCID', 'LMRInfo') ?: $PCID; ?>"/>
                    <input type="hidden" name="encryptedCId" value="<?php echo cypher::myEncryption($clientId) ?>">
                    <input type="hidden" name="encryptedIncId"
                           value="<?php echo cypher::myEncryption(Strings::showField('IID', 'incomeInfo')) ?>"/>
                    <input type="hidden" name="encryptedQAId"
                           value="<?php echo cypher::myEncryption(Strings::showField('QAID', 'QAInfo')) ?>"/>
                    <input type="hidden" name="encryptedPropId"
                           value="<?php echo cypher::myEncryption(Strings::showField('PID', 'proposalInfo')) ?>"/>
                    <input type="hidden" name="createdBy" value="<?php echo PageVariables::$userNumber ?>"/>
                    <input type="hidden" name="createdByEnc" value="<?php echo cypher::myEncryption(PageVariables::$userNumber) ?>"/>
                    <input type="hidden" name="createdUserType" id="userGroup" value="<?php echo $userGroup ?>"/>
                    <input type="hidden" name="activeTab" id="activeTab" value="<?php echo $activeTab ?>"/>
                    <input type="hidden" name="tabOpt" id="tabOpt" value="<?php echo $activeTab ?>"/>
                    <input type="hidden" name="goToTab" id="goToTab" value="<?php echo $goToTab ?>"/>
                    <input type="hidden" name="op" value="<?php echo cypher::myEncryption($op) ?>"/>
                    <input type="hidden" name="dataChanged" id="dataChanged" value=""/>
                    <input type="hidden" name="publicUser" id="publicUser"
                           value="<?php echo htmlspecialchars($publicUser) ?>">
                    <input type="hidden" name="userRole" id="userRole" value="<?php echo $userRole ?>">
                    <input type="hidden" name="allowToChangeOrAssignBranchForFile"
                           id="allowToChangeOrAssignBranchForFile"
                           value="<?php echo $allowToChangeOrAssignBranchForFile ?>">
                    <input type="hidden" name="isSLMOnly" id="isSLMOnly" value="<?php echo $isSLMOnly ?>">
                    <input type="hidden" name="oldFPCID" id="oldFPCID" value="<?php echo $oldFPCID ?>">
                    <input type="hidden" name="isSysNotesPrivate" id="isSysNotesPrivate"
                           value="<?php echo $isSysNotesPrivate ?>">
                    <input type="hidden" name="agentAndBranchAsLabel" id="agentAndBranchAsLabel"
                           value="<?php echo $agentAndBranchAsLabel ?>">
                    <input type="hidden" name="allowToUpdateFileAdminSection" id="allowToUpdateFileAdminSection"
                           value="<?php echo $allowToUpdateFileAdminSection ?>">
                    <input type="hidden" name="allowClientToCreateHMLOFile" id="allowClientToCreateHMLOFile"
                           value="<?php echo $allowClientToCreateHMLOFile ?>">
                    <input type="hidden" name="isSave" id="isSave" value="">
                    <input type="hidden" name="rcPerFinanced" id="rcPerFinanced" value="">
                    <input type="hidden" name="isEF" id="isEF" value="<?php echo $isEF; ?>">
                    <input type="hidden" name="selClientId" id="selClientId" value="<?php echo $selClientId; ?>">
                    <input type="hidden" name="goToTabLI" id="goToTabLI" value="">
                    <input type="hidden" name="orignalborEmail" id="orignalborEmail"
                           value="<?php echo $borOrigEmail; ?>">
                    <input type="hidden" name="isEmailChange" id="isEmailChange" value="0">
                    <input type="hidden" name="upEmailInClientPro" id="upEmailInClientPro" value="">
                    <input type="hidden" name="isEmailPopup" id="isEmailPopup" value="0">
                    <input type="hidden" name="originalClientId" value="<?php echo $selClientId; ?>">
                    <input type="hidden" name="fileTypesTxt" id="fileTypesTxt" value="<?php echo $fileTypesTxt; ?>">
                    <input type="hidden" id="allowAutomation" value="<?php echo $allowAutomation; ?>">
                    <?php if ($publicUser == 1) { ?>
                        <input type="hidden" name="branchReferralCode" id="branchReferralCode"
                               value="<?php echo $branchReferralCode ?>">
                        <input type="hidden" name="agentReferralCode" id="agentReferralCode"
                               value="<?php echo $agentReferralCode ?>">
                        <input type="hidden" name="fOpt" id="fOpt" value="<?php echo $fOpt ?>">
                        <input type="hidden" name="encBranchReferralCode" id="encBranchReferralCode"
                               value="<?php echo cypher::myEncryption($branchReferralCode) ?>">
                        <input type="hidden" name="encAgentReferralCode" id="encAgentReferralCode"
                               value="<?php echo cypher::myEncryption($agentReferralCode) ?>">
                        <input type="hidden" name="encFOpt" id="encFOpt"
                               value="<?php echo cypher::myEncryption($fOpt) ?>">
                    <?php } else { ?>
                        <input type="hidden" name="acqualifyPCAccountId" id="acqualifyPCAccountId"
                               value="<?php echo cypher::myEncryption($acqualifyPCAccountId) ?>">
                        <input type="hidden" name="acqualifyPCMinCreditScore" id="acqualifyPCMinCreditScore"
                               value="<?php echo cypher::myEncryption($acqualifyPCMinCreditScore) ?>">
                        <input type="hidden" name="acqualifyBranchId" id="acqualifyBranchId"
                               value="<?php echo cypher::myEncryption($acqualifyBranchId) ?>">
                        <input type="hidden" name="applicantUserid" id="applicantUserid"
                               value="<?php echo cypher::myEncryption($applicantUserid) ?>">
                        <input type="hidden" name="defaultBranchId" id="defaultBranchId"
                               value="<?php echo cypher::myEncryption($defaultBranchId) ?>">
                    <?php } ?>
                    <?php if ($userGroup == 'Agent') { ?>
                        <input type="hidden" id="externalBroker" value="<?php echo $externalBroker ?>">
                    <?php } ?>
                    <?php echo loanForm::simpleHidden('allowNestedEntityMembers', PageVariables::$allowNestedEntityMembers); ?>
                    <!--
                        Active Tab: <?php echo $activeTab; ?>
                        Page: <?php echo $forms[$activeTab]['FN']; ?>

                    -->

                    <?php
                    $lastUpdatedParam = '';
                    $fileRow = '';
                    $triggerRule = 'No';
                    if ($LMRId == 0) { //automation for new file
                        $isFssUpdated = 'Yes';
                        $fileRow = 'Insert';
                        $triggerRule = 'Yes';
                    } else {
                        $isFssUpdated = 'No';
                        $fileRow = 'Update';
                    }
                    if ($publicUser) {
                        $lastUpdatedParam = 'PFS';
                        $triggerRule = 'Yes';
                    }

                    $userAutomationControlAccess = getAutomationControlAccess::getReport(PageVariables::$userNumber);
                    if ($userGroup != 'Employee') {
                        $userAutomationControlAccess = 0;
                    }

                    ?>
                    <!-- automation  -->
                    <input type="hidden" name="lastUpdatedFss" id="lastUpdatedFss" value="">
                    <input type="hidden" name="allAction" id="allAction" value="">
                    <input type="hidden" name="autoTaskIds" id="autoTaskIds" value="">
                    <input type="hidden" name="autoEmailIds" id="autoEmailIds" value="">
                    <input type="hidden" name="autoWebhookIds" id="autoWebhookIds" value="">
                    <input type="hidden" name="autoEmployeeIds" id="autoEmployeeIds" value="">
                    <input type="hidden" name="autoChangeFileStatusId" id="autoChangeFileStatusId" value="">
                    <input type="hidden" name="manual" id="manual" value="">
                    <?php if ($publicUser || in_array($activeTab, [
                            glFileTabs::CI,
                            glFileTabs::HMLI,
                            glFileTabs::PI,
                            glFileTabs::DA,
                            glFileTabs::GOVT,
                            glFileTabs::IE,
                            glFileTabs::AL,
                            glFileTabs::ADMIN,
                            glFileTabs::BC,
                            glFileTabs::NOI,
                            glFileTabs::CD,
                        ])) {
                        //false for BO User, these fields are included from HMLOLoanInfoForm.php
                        ?>
                        <input type="hidden" name="isFssUpdated" id="isFssUpdated" value="<?php echo $isFssUpdated; ?>">
                        <input type="hidden" name="lastUpdatedParam" id="lastUpdatedParam"
                               value="<?php echo $lastUpdatedParam; ?>">
                        <input type="hidden" name="triggerRule" id="triggerRule" value="<?php echo $triggerRule; ?>">
                        <input type="hidden" name="userAutomationControlAccess" id="userAutomationControlAccess"
                               value="<?php echo $userAutomationControlAccess; ?>">
                        <input type="hidden" name="fileRow" id="fileRow" value="<?php echo $fileRow; ?>">
                    <?php } ?>
                    <?php
                    if (array_key_exists($activeTab, $forms)) {
                        if ($debugSubPage) {
                            echo '<div>' . $forms[$activeTab]['FN'] . '</div>';
                        }
                            if ($publicUser == 1) {
                                require $forms[$activeTab]['FN'];
                            } else{
                                require CONST_BO_PATH . $forms[$activeTab]['FN'];
                            }

                    } else {
                        echo "<h1 style=\"color:red;text-align:center;margin-top:30px;\">You are not authorized to view this tab</h1>";
                    }
                    ?>
                    <input type="hidden" name="confirmType" id="confirmType" value="<?php echo $confirmType; ?>">
                    <input type="hidden" name="allowToupdateFileAndClient" id="allowToupdateFileAndClient"
                           value="<?php echo $allowToupdateFileAndClient; ?>">
                    <input type="hidden" id="idNo" name="idNo">
                </form>
            </div>
        </div> <!-- formContainer -->
    </div>
</div>
<?php
if (in_array($activeTab,['PI','LI','QAPP'])) {
    require __DIR__ . '/propertyInfo/modal/importProperties.php';
}?>
<?php
Strings::includeMyScript([
    '/backoffice/LMRequest/js/formControl.js'
]);
?>
<script type="text/javascript">
    <?php  if($cemail != '' && $PCID > 0) {  ?>
    populateClientInfo('loanModForm', '<?php echo htmlspecialchars($cemail); ?>', '<?php echo $PCID; ?>');
    <?php    } ?>

    $(function() {
        let op = "<?php echo $op;?>";
        let selectedFileType = "<?php echo $fileModuleInfo[0]['moduleCode'] ?? '';?>";
        let form_original_data = $("#loanModForm").serialize();
        let typeOfHMLOLoanRequesting = "<?php echo $typeOfHMLOLoanRequesting; ?>";
        if (selectedFileType === 'loc' && typeOfHMLOLoanRequesting === 'Equipment Financing') {
            $("#tab_PI").css('display', 'none');
        }
        let _activeTab = $('#activeTab');
        let activeTab = _activeTab.val();
        if (activeTab !== 'PE') {
            $('#loanModForm input[type="submit"]').attr('disabled', 'disabled');
        }
        $('#psSubmit_legalDocs').attr('disabled', false);
        $('#loanModForm').bind('keyup change', function (e) {

            if (($("#loanModForm").serialize() != form_original_data
                    || (e.target.type === 'file' && e.type === 'change'))
                && activeTab !== 'TPS') {
                if ($('#submitStatus').val() === '0') {
                    e.preventDefault();
                    enableSaveButton();
                }

            } else if (activeTab === 'TPS') {
                $(document).on('change', '.paymentInfo,.billingData,.avm_fields,.flood_fields,.business_credit_fields,.consumer_credit_fields,.criminal_record_fields,.mers_fields,.coborrower_credit_fields,.legalDocs_fields', function () {
                    enableSaveButton();
                });
                if ($('#cra').val() !== '' && $('#thirdPartyServices').val() !== '') {
                    $('#psSubmit').removeAttr('disabled');
                } else {
                    $('#psSubmit').prop('disabled', true);
                }
            } else {
                $('#loanModForm input[type="submit"]').attr('disabled', 'disabled');
            }
        });

        $('.switch-off, .switch-on').click(function (e) {
            e.preventDefault(); // <-- important -->
            enableSaveButton();
        });
        $(".expander").click(function () {
            $(this).next(".subExpander").toggle('fast');
            $(this).not(".caret").children(".fa").toggleClass("fa-folder-o fa-folder-open-o");
            return false; //signal as event handled, don't bubble up the chain
        })
            .not(".expanded").next(".subExpander").hide();
        if (op === 'view') {
            $("#loanModForm input,textarea,select").prop("disabled", true);
            $("#loanModForm .chzn-select").prop('disabled', true).trigger("chosen:updated");
            $("#loanModForm .WorkflowCard_body input").prop('disabled', false);
        }

        let userRole = "<?php echo $userRole;?>";
        if (userRole === 'Branch') {
            getServiceTypes('loanModForm');
        }

        formControl.formFields = <?php echo json_encode($fieldsInfo); ?>;
        formControl.fileTab = "<?php echo htmlspecialchars($fileTab); ?>";
        formControl.publicUser = "<?php echo intval($publicUser); ?>";
        formControl.userGroup = "<?php echo $userGroup; ?>";
        formControl.activeTab = '<?php echo htmlspecialchars($activeTab); ?>';
    });
</script>
<?php if ($activeTab === 'DOC') {
    require __DIR__ . '/../DocsForm/modals/AddEditNote.php';
} ?>

<?php if ($activeTab == 'SO') { ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.5/jquery.validate.min.js"></script>
    <!-- Modal for Submit Offer Tab -->
    <div class="modal fade" id="soModal" tabindex="-1" role="dialog" aria-labelledby="soModalLabel">
        <div class="modal-dialog modal-xl" role="document">
            <form name="createOfferForm" id="createOfferForm" action="<?php echo $formAction; ?>" method="post"
                  enctype="multipart/form-data">
                <input type="hidden" name="encryptedRId" value="<?php echo cypher::myEncryption($LMRResponseId) ?>"/>
                <input type="hidden" name="encryptedEId" value="<?php echo cypher::myEncryption($executiveId) ?>"/>
                <input type="hidden" name="encryptedLId" id="encryptedLId"
                       value="<?php echo cypher::myEncryption($LMRId) ?>">
                <input type="hidden" name="goToTab" id="goToTab" value="<?php echo $goToTab ?>"/>
                <input type="hidden" name="op" value="<?php echo cypher::myEncryption($op) ?>"/>
                <input type="hidden" name="createdBy" value="<?php echo PageVariables::$userNumber ?>"/>
                <input type="hidden" name="offerId" id="offerId" value="">
                <input type="hidden" name="createdByFirstName" id="createdByFirstName"
                       value="<?php echo $_SESSION['firstName']; ?>">
                <input type="hidden" name="createdByLastName" id="createdByLastName"
                       value="<?php echo $_SESSION['lastName']; ?>">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="soModalLabel">Create New Offer</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <i aria-hidden="true" class="ki ki-close"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <?php require CONST_BO_PATH . '/submitOfferForm.php'; ?>
                    </div>
                    <div class="modal-footer">
                        <button type="submit" name="btnSave" id="btnSave" class="btn btn-primary">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal for Submit Offer Tab -->
    <div class="modal fade" id="soOfferNotes" tabindex="-1" role="dialog" aria-labelledby="soOfferNotesLabel">
        <div class="modal-dialog modal-lg" role="document">
            <form name="createOfferNotes" id="createOfferNotes" action="#" method="post">
                <input type="hidden" name="offerNotesId" id="offerNotesId" value="">
                <input type="hidden" name="saveNotes" id="saveNotes" value="">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="soModalLabel">Add/Update Offer Notes</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <i aria-hidden="true" class="ki ki-close"></i>
                        </button>
                    </div>
                    <div class="modal-body">
                        <input type="hidden" name="action" id="actionNotes" value="yes">
                        <div class="form-group col-md-6">
                            <label class="font-weight-bold" for="offerNotes">Offer Notes</label>
                        </div>
                        <div class="col-md-12">
                            <textarea name="offerNotes" id="offerNotes" class="form-control input-sm"></textarea>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" name="btnSaveNote" id="btnSaveNote" class="btn btn-primary">Save</button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function () {
            $('[data-toggle="tooltip"]').tooltip({
                boundary: 'window',
            });
            /*Open Modal*/
            $('#soBtnId').click(function () {
                $("#soModalLabel").text("Create New Offer");
                $(".block-content .text-primary").text("Create Offer");
                $("#btnSave").text("Save");
                $("#soModal").modal('toggle');
                // remove extra add more file div
                $('#appendMoreFile').html('');
                $('#createOfferForm').trigger("reset");
                $("#createOfferForm").data('validator').resetForm();
            });
            /*Actions*/
            $(document).on('click', '.offerActions', function () {
                var id = $(this).attr('id').split('~');
                var action = id[0];
                var conf = '';
                if (action == "soEdit" || action == "soNote") {
                    if (action == "soEdit") {
                        $("#createOfferForm").data('validator').resetForm();
                    } else if (action == "soNote") {
                        $("#saveNotes").val("yes");
                    }
                    $.ajax({
                        url: "<?php echo CONST_URL_BOSSL; ?>submitOfferSave.php",
                        type: "post",
                        data: {action: action, id: id[1]},
                        contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                        beforeSend: function () {

                        },
                        success: function (resp) {
                            if (action == "soEdit") {
                                var obj = JSON.parse(resp);
                                var id = obj.id;
                                var offerCompanyName = obj.offerCompanyName;
                                var offerFirstName = obj.offerFirstName;
                                var offerLastName = obj.offerLastName;
                                var offerEmailAddress = obj.offerEmailAddress;
                                var offerDirectPhone = obj.offerDirectPhone;
                                var offerAltPhone = obj.offerAltPhone;
                                var offerLoanType = obj.offerLoanType;
                                var offerTerm = obj.offerTerm;
                                var offerRate = obj.offerRate;
                                var offerPoint = obj.offerPoint;
                                var offerLoanAmount = obj.offerLoanAmount;
                                var offerLTV = obj.offerLTV;
                                var offerDSCR = obj.offerDSCR;
                                var offerMindFico = obj.offerMindFico;
                                var offerPrePayPenalty = obj.offerPrePayPenalty;
                                var offerFile = obj.offerFile;
                                var offerAdditionalDetail = obj.offerAdditionalDetail;
                                // assign the values
                                $("#offerId").val(id);
                                $("#offerCompanyName").val(offerCompanyName);
                                $("#offerFirstName").val(offerFirstName);
                                $("#offerLastName").val(offerLastName);
                                $("#offerEmailAddress").val(offerEmailAddress);
                                $("#offerDirectPhone").val(offerDirectPhone);
                                $("#offerAltPhone").val(offerAltPhone);
                                $("#offerLoanType").val(offerLoanType);
                                $("#offerTerm").val(offerTerm);
                                $("#offerRate").val(offerRate);
                                $("#offerPoint").val(offerPoint);
                                $("#offerLoanAmount").val(offerLoanAmount);
                                $("#offerLTV").val(offerLTV);
                                $("#offerDSCR").val(offerDSCR);
                                $("#offerMindFico").val(offerMindFico);
                                $("#offerPrePayPenalty").val(offerPrePayPenalty);
                                //$("#offerFile").val(offerFile);// cannot set the value using jQuery
                                $("#offerAdditionalDetail").val(offerAdditionalDetail);
                                //
                                $("#soModalLabel").text("Update Offer");
                                $(".block-content .text-primary").text("Update Offer");
                                $("#btnSave").text("Update");
                                $("#soModal").modal('toggle');
                                var showToast = 0;
                            } else if (action == "soNote") {
                                var obj = JSON.parse(resp);
                                var id = obj.id;
                                var offerNotes = obj.offerNotes;
                                // assign the values
                                $("#offerNotesId").val(id);
                                $("#offerNotes").val(offerNotes);
                                $("#soOfferNotes").modal('toggle');
                                var showToast = 0;
                            }
                            if (showToast) {
                                toastrNotification(msg, 'success', 1500);
                                location.reload();
                            }
                        }
                    });
                } else if (action == "soDelete") {
                    var conf = "Are you sure to Delete the Offer?";
                } else if (action == "soCopy") {
                    var conf = "Are you sure to Copy the Offer?";
                } else if (action == "soDeleteFile") {
                    var conf = "Are you sure to Delete the document?";
                }
                if (conf != '') {
                    $.confirm({
                        icon: 'fa fa-warning',
                        closeIcon: true,
                        title: 'Confirm',
                        content: conf,
                        type: 'red',
                        backgroundDismiss: true,
                        buttons: {
                            yes: function () {
                                $.ajax({
                                    url: "<?php echo CONST_URL_BOSSL; ?>submitOfferSave.php",
                                    type: "post",
                                    data: {action: action, id: id[1]},
                                    contentType: 'application/x-www-form-urlencoded; charset=UTF-8',
                                    beforeSend: function () {

                                    },
                                    success: function (resp) {
                                        if (action == "soDelete") {
                                            var msg = "Offer Deleted Successfully.";
                                            var showToast = 1;
                                        } else if (action == "soCopy") {
                                            var msg = "Offer Copied Successfully.";
                                            var showToast = 1;
                                        } else if (action == "soDeleteFile") {
                                            var msg = "File Deleted Successfully.";
                                            var showToast = 1;
                                        }
                                        if (showToast) {
                                            toastrNotification(msg, 'success', 1500);
                                            location.reload();
                                        }
                                    }
                                });
                            },
                            cancel: function () {

                            },
                        },

                    });
                }
            });
            /* Save Notes */
            $(document).on('click', '#btnSaveNote', function () {
                $.ajax({
                    url: "<?php echo CONST_URL_BOSSL; ?>submitOfferSave.php",
                    type: "post",
                    data: $('#createOfferNotes').serialize(),
                    beforeSend: function () {

                    },
                    success: function (resp) {
                        if (resp == 'noteUpdated') {
                            var msg = "Offer Notes Updated";
                            toastrNotification(msg, 'success', 1500);
                            location.reload();
                        }
                    }
                });
            });


            //This is BO form validation
            //Web form validation is in submitOfferForm.php page
            var soWebForm = $('#soWebForm').val();
            //form validation
            jQuery.validator.addMethod("offerDirectPhoneValidation", function (value, element) {
                value = value.split('Ext', 1)[0];
                let _validationNumber = value.replace(/[^0-9]/ig, "");
                return _validationNumber.length === 10;
            }, "Please enter valid phone number");
            jQuery.validator.addMethod("offerAltPhoneValidation", function (value, element) {
                value = value.split('Ext', 1)[0];
                let _validationNumber = value.replace(/[^0-9]/ig, "");
                return (_validationNumber.length === 10 || _validationNumber.length === 0);
            }, "Please enter valid alt phone number");
            $("#createOfferForm").validate({
                rules: {
                    offerEmailAddress: {
                        email: true
                    },
                    offerDirectPhone: {
                        required: true,
                        offerDirectPhoneValidation: true,
                    },
                    offerAltPhone:{
                        offerAltPhoneValidation: true,
                    }
                },
                messages: {
                    offerCompanyName: "Please enter your company name",
                    offerFirstName: "Please enter your first name",
                    offerLastName: "Please enter your last name",
                    offerEmailAddress: "Please enter valid email address",
                    offerDirectPhone: {
                        required: "Please enter valid phone number",
                        offerDirectPhoneValidation: "Please enter valid phone number",
                    },
                    offerAltPhone:{
                        offerAltPhoneValidation: "Please enter valid alt phone number",
                    },


                    offerLoanType: "Please enter valid loan type",
                    offerTerm: "Please enter valid term",
                    offerRate: "Please enter valid rate (__.___)",
                    offerPoint: "Please enter valid points (__.___)",
                    offerLoanAmount: "Please enter valid loan amount",
                    offerAdditionalDetail: "Please enter valid additional details"
                },
                errorElement: "em",
                errorPlacement: function (error, element) {
                    // Add the `help-block` class to the error element
                    error.addClass("help-block text-danger");

                    if (element.prop("type") === "checkbox") {
                        error.insertAfter(element.parent("label"));
                    } else {
                        if (element.prop("id") === "offerRate" || element.prop("id") === "offerLoanAmount" || element.prop("id") === "offerLoanAmount") {
                            error.insertAfter(element.parent('.input-group'));
                        } else {
                            error.insertAfter(element);
                        }

                    }
                },
                highlight: function (element, errorClass, validClass) {
                    $(element).parents(".col-sm-5").addClass("has-error").removeClass("has-success");
                },
                unhighlight: function (element, errorClass, validClass) {
                    $(element).parents(".col-sm-5").addClass("has-success").removeClass("has-error");
                }
            });
            if (soWebForm == 0) { //avoid this for the web form
                // Masking and unmasking before submit
                //$("#offerPoint").inputmask('9[9].999');
                //$("#offerRate").inputmask('9[9].999');
                // $("#offerDirectPhone").inputmask('999 - 999 - 9999');
                // $("#offerAltPhone").inputmask('999 - 999 - 9999');
                $('#createOfferForm').submit(function (e) {
                    e.preventDefault();
                    if ($("#createOfferForm").valid()) {
                        document.getElementById('createOfferForm').submit();
                        return true;
                    }
                });
            }
        });
    </script>
<?php } ?>

<?php if (SHOW_DEBUG_TOOLS) { ?>
    <script type="text/javascript"
            src="/backoffice/LMRequest/js/debugTools.js?<?php echo CONST_JS_VERSION; ?>"></script>
<?php } ?>

<?php
include_once CONST_BO_PATH . 'automation/sections/previewAutomatedEventPopup.php';
?>
