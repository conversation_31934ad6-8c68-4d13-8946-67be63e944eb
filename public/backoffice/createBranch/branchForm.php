<?php
global $isPLO, $userGroup, $userRole, $executiveId, $PCID,
       $PCAllowToCreateBranch, $processorId;

use models\composite\oBranch\getBranches;
use models\composite\oBranch\getBranchModules;
use models\composite\oBranch\getBranchServices;
use models\composite\oBranch\getBranchThankYouMsg;
use models\composite\oBranch\getPCBranchServices;
use models\composite\oBranch\getPLOCreditCardType;
use models\composite\oBranch\getPromoCodeForBranch;
use models\composite\oCustomDocs\getBranchCustomPackages;
use models\composite\oCustomDocs\getPCCustomDocsContent;
use models\composite\oModules\getLibModules;
use models\composite\oPackage\getLMRAEPackages;
use models\composite\oPackage\getMyFileAutoGeneratedDocs;
use models\composite\oPackage\getStdDIYClientPackages;
use models\composite\oPackage\getStdPackages;
use models\composite\oPC\doesPCExceedsNoOfUsers;
use models\composite\oPC\getacqualifyPCDetails;
use models\composite\oPC\getDigiAndNoDigiLibPackage;
use models\composite\oPC\getMyDetails;
use models\composite\oPC\getMyPackages;
use models\composite\oPC\getPCList;
use models\composite\oPC\getPCModules;
use models\composite\oPC\getProcessingCompanyPackages;
use models\constants\gl\glBrokerTAC;
use models\constants\gl\glHMLOTAC;
use models\constants\gl\glPackageCategoryArray;
use models\constants\gl\glThankYouMsg;
use models\constants\gl\glThankYouMsgBR;
use models\constants\stateTimeZone;
use models\cypher;
use models\lendingwise\tblProcessingCompany;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;

$glPackageCategoryArray = glPackageCategoryArray::$glPackageCategoryArray;
$stateTimeZone = stateTimeZone::$stateTimeZone;
$glBrokerTAC = glBrokerTAC::$glBrokerTAC;
$glHMLOTAC = glHMLOTAC::$glHMLOTAC;
$glThankYouMsg = glThankYouMsg::$glThankYouMsg;
$glThankYouMsgBR = glThankYouMsgBR::$glThankYouMsgBR;

$email = '';
$LMRName = $LMRLastName = '';
$LMRFirstName = '';
$company = '';
$updateMsg = '';
$exEmail = '';
$referralSiteCode = 1;
$LMRInfoArray = [];
$LMRPromoCodeArray = [];
$LMRBccEmail = '';
$LMRCcEmail = '';
$tollFree1 = '';
$tollFree2 = '';
$tollFree3 = '';
$fax1 = '';
$fax2 = '';
$fax3 = '';
$tollFree = '';
$fax = '';
$website = '';
$signCompany = '';
$signTollFree1 = '';
$signTollFree2 = '';
$signTollFree3 = '';
$signFax1 = '';
$signFax2 = '';
$signFax3 = '';
$signTollFree = '';
$signFax = '';
$signWebsite = '';
$LMRLogo = '';
$logoExit = false;
$cellNo1 = '';
$cellNo2 = '';
$cellNo3 = '';
$cellNumber = '';
$signCellNo1 = '';
$signCellNo2 = '';
$signCellNo3 = '';
$signCellNumber = '';
$userType = '';
$subscribedOption = 1;
$address1 = '';
$city = '';
$appStates = '';
$zipCode = '';
$applicationFee = '';
$forensicFee = '';
$docPreparationFee = '';
$attorneyProcessingFee = '';
$mortgae2Fee = '';
$disclosure = '';
$paymentToCompany = '';
$rescissionPhNo1 = '';
$rescissionPhNo2 = '';
$rescissionPhNo3 = '';
$rescissionPhNo = '';
$LMRPackageIdKeyArray = [];
$packageId = 0;
$packageId = 0;
$brokerProcessingFee = '';
$loanModificationFee = '';
$attachWithEmail = 1;
$publishInPQPortal = 0;
$serviceAFee = '';
$serviceBFee = '';
$paymentGateway = 'Authorize.net';
$processingPkgInfoArray = [];
$packageIds = '';
$pkgSelectedArray = [];
$pkgAllSelectedArray = [];
$tempPkgInfoArray = [];
$pkgDiv = 'display:block';
$PLOCreditCardTypeArray = [];
$PLOHearAbout = 0;
$pyDiv = 'display:none';
$allowLMRAEToEditFile = 0;
$PLOAPILoginId = '';
$PLOTransactionKey = '';
$PLOPaypalEmail = '';
$eCheckUrl = '';
$eCheck = 0;
$askReferralAgent = 0;
$branchAELoginUrl = '';
$demoVideoLink = '';
$eBookLink = '';
$allowLMRAEToAccessDocs = 1;
$allowLMRToOnOffAgentLogin = 0;
$publishDoument = 0;
$linkToSpanishVersion = 0;
$askPaymentBeforeLMR = 1;
$allowLMRAEToEditCommission = 0;
$showPoweredByTMPLink = 1;
$allowToViewSslScript = '';
$sslScript = '';
$serviceProvider = '';
$allowAddOn = 0;
$myTimeZone = '';
$redirectUrl = '';
$subscriberID = '';
$googleTrackingCode = '';
$TAC = $TACQA = '';
$allowToUpdateFileAdminSection = 0;
$googleTrackingCodeQA = '';
$sendMarketingEmail = 1;
$allowClientToUploadDocs = 0;
$selectedClientPkgArray = [];
$clientPackageIds = '';
$pkgAllClientSelectedArray = [];
$displayLinkToFICO = 1;
$pkgs = [];
$pkgsKeys = [];
$serviceTypeArray = [];
$fileServices = [];
$allowBranchToLogin = $allowWorkflowEdit = 0;
$allowBranchToGetBorrowerUploadDocsNotification = 1;
$allowToViewMarketPlace = 1;
$allowcaptcha = 0;
$addBranchHearAbout = 0;
$allowLMRToEditAgentProfile = 0;
$customMessage = '';
$phone1 = '';
$phone2 = '';
$phone3 = '';
$phoneExt = '';
$allowToAddAgent = 0;
$useMyServerSetting = 0;
$hostName = '';
$pwd = '';
$portNo = '';
$replyTo = '';
$bounceMail = '';
$allowBranchToCreateFiles = 1;
$allowBranchToCreateTasks = 1;
$allowBranchToSeeDashboard = 1;
$seePrivate = 0;
$accessRestriction = 0;
$allowUserToDeleteUploadedDocs = 1;
$allowUserToEditOwnNotes = 1;
$permissionToREST = 1;
$allowedToExcelReport = 0;
$allowESignService = 0;
$processingCompanyInfoArray = [];
$CustomDocInfoArray = [];
$customPkgSelectedArray = [];
$tcUrl = '';
$tcDiv = 'display:none';
$payDiv = 'display:none';
$eCheckDiv = 'display:block';
$processingCompanyArray = [];
$PCListArray = [];
$processingCompanyId = 0;
$globalAcctNo = '';
$sponsorName = '';
$PBID = 0;
$PCBranchAssocationInfoArray = [];
$PCBracnhKeyArray = [];
$changeDIYPlan = 0;
$newBrokerNumber = 0;
$brCnt = 0;
$agentReferralCode = 1;
$exAgentNumber = 0;
$brokerArray = [];
$LMRClientTypeArray = [];
$LMRClientType = '';
$LMRClientTypeInfoArray = [];
$branchServicesArray = [];
$stdNoDigiDIYSelectedArray = [];
$stdNoDigiSelectedArray = [];
$isPrimary = 0;
$allowToLASubmit = 0;
$allowToCFPBSubmit = 0;
$allowToViewCFPBPipeline = 0;
$defaultPrimaryStatus = '';
$allowAgentToSeeFile = 1;
$allowToSendFax = $allowtoEditCCInfo = 0;
$subscribeToHOME = 0;
$allowEmailCampaign = 1;
$PCModulesArray = [];
if ($isPLO == 1) $allowToSendHomeownerLink = 1; else $allowToSendHomeownerLink = 0;
$allowBranchToSeeCommission = 1;
$allowClientToAccessDocs = 0;
$allowEditToIR = 0;
$allowToSendFileDesignation = 1;
$allowBranchToSeePublicNotes = 1;
$mailingAddress = '';
$mailingCity = '';
$mailingState = '';
$mailingZipCode = '';
$bankName = '';
$routingNumber = '';
$accountNumber = '';
$county = '';
$modulesArray = $tempModulesArray = [];
$tollFreeExt = '';
$allowBranchToAccessRAM = 0;
$allowBranchManagerToLogin = 0;
$isHMLO = 0;
$isLOC = 0;
$HMLOTAC = $HMLOTAQAC = '';
$thankMsg = '';
$thankMsgSV = '';
$AllowToCreateAloware = 0;
$branchThankYouMsgArray = [];
$thankMsgLMLV = '';
$thankMsgLMSV = '';
$thankMsgFOP = '';
$thankMsgSLM = '';
$thankMsgFSS = '';
$thankMsgFSI = '';
$thankMsgUSEI = '';
$thankMsgFU = '';
$thankMsgAQ = $thankMsgBR = '';
$thankMsgUniformApp = '';
$thankAgReg = '';
$defaultPrimaryStatusForFA = '';
$branchList = [];
$allowToupdateFileAndClient = '';
$allowToupdateFileAndClientArr = [];


$agentWelcomeMailStatus = 0;
$agentWelcomeMail = '';
$agentWelcomeMailSubject = '';

$FAborrowerWelcomeMailStatus = 0;
$FAborrowerWelcomeMail = '';
$FAborrowerWelcomeMailSubject = '';
$ccToAgentFA = 0;

$QAborrowerWelcomeMailStatus = 0;
$QAborrowerWelcomeMail = '';
$QAborrowerWelcomeMailSubject = '';
$ccToAgentQA = 0;
$brokerRedirectUrl = '';

$tabNumb = 1;
$redirectUrlForQA = '';
$allowToLockLoanFileBranch = 1;
$thirdPartyServices = 0;
$thirdPartyServicesLegalDocs = 0;
$isPublicWebform = 0;
//Share this file
$shareThisFile = 0;
$allowToSubmitOffer = 0;
$allowToViewCreditScreening = 0;
$enable2FAAuthentication = 0;
$TwoFAType = 'email';
$userPriceEngineStatus = 0;
$loanpassLogin = '';
$loanpassPassword = '';

$notifyBODocUpload = 0;
$notifyLODocUpload = 0;
$notifyBrokerDocUpload = 0;
$notifyDocUploadRequest = 0;
$notifyNewFileCreated = 0;
$allowToAccessInternalLoanProgram = 0;
$allowToViewAutomationPopup = 0;
$allowToCopyFile = 0;
$DocUploadBcc = '';
$DocUploadCc = '';
$allowUsersToSendEsignLinkToAll = null;

if (isset($_REQUEST['tabNumb'])) $tabNumb = trim($_REQUEST['tabNumb']);

if (PageVariables::isSuper()) {
    $PCListArray = getPCList::getReport(['userRole' => $userRole, 'activeStatus' => '1']);
}

$libModulesArray = getLibModules::getReport(['activeStatus' => '1']);                                // | Get Modules Key/Name..
$tempModulesArray = getBranchModules::getReport(['branchID' => $executiveId]);                         // | Branch Modules..

if (array_key_exists($executiveId, $tempModulesArray)) $modulesArray = $tempModulesArray[$executiveId];      // | Get Branch Modules..

if ($executiveId > 0) {
    $ip = [

        'PCID' => PageVariables::isSuper() ? $processorId : $PCID,
        'execID' => $executiveId,
    ];
    $branchList = getBranches::getReport($ip);

    if (count($branchList) > 0) $LMRInfoArray = $branchList['branchList'];
    $bCnt = count($LMRInfoArray);

    if ($bCnt > 0) {
        $tempArray = [];
        $tempArray = $LMRInfoArray[0];
        $LMRName = trim($tempArray['LMRExecutive']);
        $LMRFirstName = trim($tempArray['LMRExecutiveFirstName']);
        $LMRLastName = trim($tempArray['LMRExecutiveLastName']);
        $email = trim($tempArray['executiveEmail']);
        $company = trim($tempArray['company']);
        $LMRBccEmail = trim($tempArray['LMRBccEmail']);
        $LMRCcEmail = trim($tempArray['LMRCcEmail']);
        $tollFree = trim($tempArray['tollFree']);
        $fax = trim($tempArray['fax']);
        $website = trim($tempArray['website']);
        $branchAELoginUrl = trim($tempArray['branchAELoginUrl']);
        $LMRLogo = trim($tempArray['logo']);
        $cellNumber = trim($tempArray['cellNumber']);
        $signCellNumber = trim($tempArray['signCellNumber']);
        $address1 = trim($tempArray['address']);
        $city = trim($tempArray['city']);
        $appStates = trim($tempArray['state']);
        $zipCode = trim($tempArray['zipCode']);
        $applicationFee = trim($tempArray['applicationFee']);
        $forensicFee = trim($tempArray['forensicFee']);
        $docPreparationFee = trim($tempArray['docPreparationFee']);
        $attorneyProcessingFee = trim($tempArray['attorneyProcessingFee']);
        $mortgae2Fee = trim($tempArray['mortgae2Fee']);
        $disclosure = trim($tempArray['disclosure']);
        $paymentToCompany = trim($tempArray['paymentToCompany']);
        $rescissionPhNo = trim($tempArray['rescissionPhNo']);
        $processingCompanyId = trim($tempArray['processingCompanyId']);
        $brokerProcessingFee = trim($tempArray['brokerProcessingFee']);
        $loanModificationFee = trim($tempArray['loanModificationFee']);
        $serviceAFee = trim($tempArray['serviceAFee']);
        $serviceBFee = trim($tempArray['serviceBFee']);
        $userType = trim($tempArray['userType']);
        $tcUrl = trim($tempArray['tcUrl']);
        $demoVideoLink = trim($tempArray['demoVideoLink']);
        $paymentGateway = (trim($tempArray['paymentGateway']) != '') ? trim($tempArray['paymentGateway']) : 'Authorize.net';
        $PLOHearAbout = trim($tempArray['addHearAbout']);
        $allowLMRAEToEditFile = trim($tempArray['allowLMRAEToEditFile']);
        $allowAgentToSeeFile = trim($tempArray['agentFileAccess']);
        $allowLMRAEToAccessDocs = trim($tempArray['allowLMRAEToAccessDocs']);
        $PLOAPILoginId = trim($tempArray['APILoginId']);
        $PLOTransactionKey = trim($tempArray['transactionKey']);
        $PLOPaypalEmail = trim($tempArray['paypalEmailId']);
        $eCheck = trim($tempArray['isECheck']);
        $eCheckUrl = trim($tempArray['eCheckUrl']);
        $subscribedOption = trim($tempArray['subscribedOption']);
        $askReferralAgent = trim($tempArray['askReferralAgent']);
        $allowLMRToOnOffAgentLogin = trim($tempArray['allowLMRToOnOffAgentLogin']);
        $eBookLink = trim($tempArray['eBookLink']);
        $askPaymentBeforeLMR = trim($tempArray['askPaymentBeforeLMR']);
        $allowLMRAEToEditCommission = trim($tempArray['allowLMRAEToEditCommission']);
        $allowClientToAccessDocs = trim($tempArray['allowClientToAccessDocs']);
        $allowEditToIR = trim($tempArray['allowEditToIR']);
        $allowBranchToSeeCommission = trim($tempArray['allowBranchToSeeCommission']);
        $allowBranchManagerToLogin = trim($tempArray['allowBranchManagerToLogin']);
        $sslScript = trim($tempArray['sslScript']);
        $allowToViewSslScript = trim($tempArray['allowToViewSslScript']);
        $serviceProvider = trim($tempArray['serviceProvider']);
        $allowAddOn = trim($tempArray['allowAddOn']);
        $myTimeZone = trim($tempArray['timeZone']);
        $redirectUrl = trim($tempArray['redirectUrl']);
        $subscriberID = trim($tempArray['subscriberID']);
        $googleTrackingCode = urldecode($tempArray['googleTrackingCode']);
        $googleTrackingCodeQA = urldecode($tempArray['googleTrackingCodeQA']);
        $TAC = trim($tempArray['TAC']);
        $TACQA = trim($tempArray['TACQA']);
        $allowToUpdateFileAdminSection = trim($tempArray['allowToUpdateFileAdminSection']);
        $sendMarketingEmail = trim($tempArray['sendMarketingEmail']);
        $allowClientToUploadDocs = trim($tempArray['allowClientToUploadDocs']);
        $displayLinkToFICO = trim($tempArray['displayLinkToFICO']);
        $allowBranchToLogin = trim($tempArray['allowBranchToLogin']);
        $addBranchHearAbout = trim($tempArray['addBranchHearAbout']);
        $allowLMRToEditAgentProfile = trim($tempArray['allowLMRToEditAgentProfile']);
        $customMessage = trim($tempArray['customMessage']);
        $phone = trim($tempArray['directPhone']);
        $allowToAddAgent = trim($tempArray['allowToAddAgent']);
        $useMyServerSetting = trim($tempArray['useMyServerSetting']);
        $allowBranchToCreateFiles = trim($tempArray['allowBranchToCreateFiles']);
        $allowBranchToCreateTasks = trim($tempArray['allowBranchToCreateTasks']);
        $allowBranchToSeeDashboard = trim($tempArray['allowBranchToSeeDashboard']);
        $seePrivate = trim($tempArray['seePrivate']);
        $AllowToCreateAloware = trim($tempArray['AllowToCreateAloware']);
        $allowUserToDeleteUploadedDocs = trim($tempArray['allowedToDeleteUplodedDocs']);
        $allowUserToEditOwnNotes = trim($tempArray['allowedToEditOwnNotes']);
        $allowtoEditCCInfo = trim($tempArray['allowToEditCCInfo']);
        $permissionToREST = trim($tempArray['permissionToREST']);
        $subscribeToHOME = trim($tempArray['subscribeToHOME']);
        $allowToSendFax = trim($tempArray['allowToSendFax']);
        $allowToSendFileDesignation = trim($tempArray['allowToSendFileDesignation']);
        $allowBranchToSeePublicNotes = trim($tempArray['allowBranchToSeePublicNotes']);
        $mailingAddress = trim($tempArray['mailingAddress']);
        $mailingCity = trim($tempArray['mailingCity']);
        $mailingState = trim($tempArray['mailingState']);
        $mailingZipCode = trim($tempArray['mailingZipCode']);
        $bankName = trim($tempArray['bankName']);
        $routingNumber = trim(cypher::myDecryption($tempArray['routingNumber']));
        $accountNumber = trim(cypher::myDecryption($tempArray['accountNumber']));
        $county = trim($tempArray['county']);
        $allowedToExcelReport = trim($tempArray['allowedToExcelReport']);
        $globalAcctNo = trim($tempArray['globalAcctNo']);
        $sponsorName = trim($tempArray['sponsorName']);
        $changeDIYPlan = trim($tempArray['changeDIYPlan']);
        $allowToSendHomeownerLink = trim($tempArray['allowToSendHomeownerLink']);
        $isPrimary = trim($tempArray['isPrimary']);
        $allowToLASubmit = trim($tempArray['allowToLASubmit']);
        $allowToCFPBSubmit = trim($tempArray['allowToCFPBSubmit']);
        $allowToViewCFPBPipeline = trim($tempArray['allowToViewCFPBPipeline']);
        $allowBranchToAccessRAM = trim($tempArray['allowToAccessRAM']);
        $thankMsg = urldecode($tempArray['thankMsg']);
        $thankMsgUniformApp = urldecode($tempArray['thankMsgUniformApp']);
        $thankMsgSV = urldecode($tempArray['thankMsgSV']);
        $defaultPrimaryStatus = urldecode($tempArray['defaultPrimaryStatus']);
        $defaultPrimaryStatusForFA = $tempArray['defaultPrimaryStatusForFA'];
        $redirectUrlForQA = $tempArray['redirectUrlForQA']; // Task : Add redirect url for Full & Quick App webfroms - 02-22-2019
        $allowWorkflowEdit = $tempArray['allowWorkflowEdit'];
        $allowToLockLoanFileBranch = $tempArray['allowToLockLoanFileBranch'];
        $allowBranchToGetBorrowerUploadDocsNotification = $tempArray['allowBranchToGetBorrowerUploadDocsNotification'];
        $allowToViewMarketPlace = $tempArray['allowToViewMarketPlace'];
        $allowcaptcha = $tempArray['allowcaptcha'];
        $shareThisFile = $tempArray['shareThisFile'];
        $allowToupdateFileAndClient = $tempArray['allowToupdateFileAndClient'];
        $thirdPartyServices = $tempArray['thirdPartyServices'];
        $thirdPartyServicesLegalDocs = $tempArray['thirdPartyServicesLegalDocs'];
        $allowToSubmitOffer = $tempArray['allowToSubmitOffer'];
        $allowToViewCreditScreening = $tempArray['allowToViewCreditScreening'];
        $enable2FAAuthentication = $tempArray['enable2FAAuthentication'];
        $TwoFAType = $tempArray['TwoFAType'];
        $userPriceEngineStatus = $tempArray['userPriceEngineStatus'];

        $loanpassLogin = $tempArray['loanpassLogin'];
        $loanpassPassword = $tempArray['loanpassPassword'];

        $notifyBODocUpload = $tempArray['notifyBODocUpload'];
        $notifyLODocUpload = $tempArray['notifyLODocUpload'];
        $notifyBrokerDocUpload = $tempArray['notifyBrokerDocUpload'];
        $notifyDocUploadRequest = $tempArray['notifyDocUploadRequest'];
        $notifyNewFileCreated = $tempArray['notifyNewFileCreated'];

        $DocUploadBcc = $tempArray['DocUploadBcc'];
        $DocUploadCc = $tempArray['DocUploadCc'];
        $allowToAccessInternalLoanProgram = $tempArray['allowToAccessInternalLoanProgram'];
        $allowToViewAutomationPopup = $tempArray['allowToViewAutomationPopup'];
        $allowUsersToSendEsignLinkToAll = $tempArray['allowUsersToSendEsignLinkToAll'];
        $allowToCopyFile = $tempArray['allowToCopyFile'];
        $allowToMassUpdate = $tempArray['allowToMassUpdate'] ?? 0;
        $allowToManageDraws = $tempArray['allowToManageDraws'];

        if (isset($tempArray['allowEmailCampaign'])) $allowEmailCampaign = trim($tempArray['allowEmailCampaign']);
        if ($myTimeZone == '') $myTimeZone = Arrays::recursive_array_search($appStates, $stateTimeZone);

        if ($thankMsg == '') $thankMsg = $glThankYouMsg;
        if ($thankMsgUniformApp == '') $thankMsgUniformApp = $glThankYouMsg;
        if ($thankMsgSV == '') $thankMsgSV = $glThankYouMsg;

        if (($tollFree == '') || ($tollFree == NULL) || ($tollFree == 'NULL')) {
        } else {
            $tollFreeArray = Strings::splitPhoneNumber($tollFree);
            if (count($tollFreeArray) > 0) {
                $tollFree1 = $tollFreeArray['No1'];
                $tollFree2 = $tollFreeArray['No2'];
                $tollFree3 = $tollFreeArray['No3'];
            }
            $tollFreeExt = $tollFreeArray['Ext'];
        }
        if (($phone == '') || ($phone == NULL) || ($phone == 'NULL')) {
        } else {
            $phoneArray = Strings::splitPhoneNumber($phone);
            if (count($phoneArray) > 0) {
                $phone1 = $phoneArray['No1'];
                $phone2 = $phoneArray['No2'];
                $phone3 = $phoneArray['No3'];
            }
            $phoneExt = $phoneArray['Ext'];
        }
        if (($fax == '') || ($fax == NULL) || ($fax == 'NULL')) {
        } else {
            $fax1 = substr($fax, 0, 3);
            $fax2 = substr($fax, 3, 3);
            $fax3 = substr($fax, 6, 4);
        }
        if (($cellNumber == '') || ($cellNumber == NULL) || ($cellNumber == 'NULL')) {
        } else {
            $cellNo1 = substr($cellNumber, 0, 3);
            $cellNo2 = substr($cellNumber, 3, 3);
            $cellNo3 = substr($cellNumber, 6, 4);
        }
        if (($signTollFree == '') || ($signTollFree == NULL) || ($signTollFree == 'NULL')) {
        } else {
            $signTollFree1 = substr($signTollFree, 0, 3);
            $signTollFree2 = substr($signTollFree, 3, 3);
            $signTollFree3 = substr($signTollFree, 6, 4);
        }
        if (($signCellNumber == '') || ($signCellNumber == NULL) || ($signCellNumber == 'NULL')) {
        } else {
            $signCellNo1 = substr($signCellNumber, 0, 3);
            $signCellNo2 = substr($signCellNumber, 3, 3);
            $signCellNo3 = substr($signCellNumber, 6, 4);
        }
        if (($signFax == '') || ($signFax == NULL) || ($signFax == 'NULL')) {
        } else {
            $signFax1 = substr($signFax, 0, 3);
            $signFax2 = substr($signFax, 3, 3);
            $signFax3 = substr($signFax, 6, 4);
        }
        if (($rescissionPhNo == '') || ($rescissionPhNo == NULL) || ($rescissionPhNo == 'NULL')) {
        } else {
            $rescissionPhNo1 = substr($rescissionPhNo, 0, 3);
            $rescissionPhNo2 = substr($rescissionPhNo, 3, 3);
            $rescissionPhNo3 = substr($rescissionPhNo, 6, 4);
        }
    }

    if ($allowToupdateFileAndClient != '') {
        $allowToupdateFileAndClientArr = explode(',', $allowToupdateFileAndClient);
    }
    /* Web Form Integration Tab */
    if ($email != '' && $tabNumb == 9) {
        $exEmail = "'" . $email . "'";
        $ip = [];

        $ip['executiveEmails'] = $exEmail;
        $LMRPromoCodeArray = getPromoCodeForBranch::getReport($ip);
        if (count($LMRPromoCodeArray) > 0) {
            $LMRPromoCodeArray = array_change_key_case($LMRPromoCodeArray, CASE_LOWER);
            if (array_key_exists(strtolower($email), $LMRPromoCodeArray)) {
                $referralSiteCode = $LMRPromoCodeArray[strtolower($email)];
            }
        }
    }
    /* End */

}

$ipArray = [];

$ipArray['PCID'] = $PCID;
if ($userGroup == 'Employee' || (PageVariables::isSuper() && $processingCompanyId > 0)) {
    if (PageVariables::isSuper() && $processingCompanyId > 0) {
        $ipArray['PCID'] = $processingCompanyId;
    }
    $pcInfo = tblProcessingCompany::Get(['PCID' => $processingCompanyId]);
    $ipArray['planType'] = $pcInfo->planType;
    $resultArray = doesPCExceedsNoOfUsers::getReport($ipArray);

    if (count($resultArray) > 0) {
        $noOfUsersAllowed = $resultArray['nUsersAllowed'];
        $noOfUsersCreated = $resultArray['nUsersCreated'];
        $PCAllowToCreate = $resultArray['PCAllowToCreate'];
    }
} else if (PageVariables::isSuper() && $processingCompanyId == 0) {
    $PCAllowToCreate = 1;
}

if ($processingCompanyId == 0) $processingCompanyId = $PCID;

if ($processingCompanyId > 0) {
    $inArray = ['PCID' => $processingCompanyId, 'keyNeeded' => 'n'];
    $PCModulesArray = getPCModules::getReport($inArray);

}
$moduleCodeArray = [];
$moduleCode = '';
for ($k = 0; $k < count($modulesArray); $k++) {
    if ($k > 0) {
        $moduleCode .= ',';
    }
    $moduleCode .= trim($modulesArray[$k]['moduleCode']);
}

if ($executiveId > 0) {
    $branchServicesArray = getPCBranchServices::getReport(['branchID' => $executiveId, 'moduleCode' => $moduleCode]);
}
/*
	if (array_key_exists($executiveId, $branchServicesArray)) {
	    $branchServicesArray = $branchServicesArray[$executiveId];
	}
*/
$branchSelectServicesArray = [];
$branchSelectedServicesArray = [];
if ($executiveId > 0) {
    $branchSelectServicesArray = getBranchServices::getReport(['branchID' => $executiveId, 'moduleCode' => $moduleCode]);
}
if (array_key_exists($executiveId, $branchSelectServicesArray)) {
    $branchSelectedServicesArray = $branchSelectServicesArray[$executiveId];
}


$fileModules = [];
for ($j = 0; $j < count($modulesArray); $j++) {
    //if(trim($modulesArray[$j]['moduleCode']) == 'SLM') { /** Skip SLM from selected services to show in separate DIV **/
    //} else {
    $fileModules[] = $modulesArray[$j]['moduleCode'];
    //}
}
$tempArray = [];
$tempArray = array_keys($glPackageCategoryArray);
for ($f = 0; $f < count($tempArray); $f++) {
    $fileModules[] = $tempArray[$f];
}
/*
	for ($j=0; $j<count($branchServicesArray); $j++) $fileServices[$j] = $branchServicesArray[$j]['LMRClientType'];
    if(in_array('LM' ,$fileServices)) { } else { array_push($fileServices, "LM"); } // For now show the "LM" "SS" "L" "G"
	if(in_array('SS' ,$fileServices)) { } else { array_push($fileServices, "SS"); }
	array_push($fileServices, "G");
	array_push($fileServices, "L");
*/
$tempPkgGroups = array_merge($libModulesArray, $glPackageCategoryArray);

//$tempPkgGroups = $LMRClientTypeArray;
if (($executiveId == 0) && ($userRole == 'Manager') && ($PCAllowToCreateBranch == 1)) {
    $processingCompanyId = $PCID;
}
$showPkgList = 0;
$showDIYChk = 0;

if ((PageVariables::isSuper()) || (($executiveId == 0) && ($PCAllowToCreateBranch == 1)) || ($userRole == 'Manager')) {
    $showPkgList = 1;
} else {
    $showPkgList = 0;
}
if ($userType == 'PLO') {
    $showDIYChk = 1;
} else {
    $showDIYChk = 0;
}

/** Check E-sign Service available or not. **/
if ($processingCompanyId > 0) {
    $myArray = ['activeStatus' => 1, 'PCID' => $processingCompanyId];
    $processingCompanyArray = getMyDetails::getReport($myArray);
    $allowESignService = $processingCompanyArray[$processingCompanyId]['allowESignService'];
}


if ((($executiveId > 0) || (($executiveId == 0) && ($userRole == 'Manager') && ($PCAllowToCreateBranch == 1))) && $tabNumb == 2) {
    if ($processingCompanyId > 0) {
        $inputArray = [];
        $inputArray['PCID'] = $processingCompanyId;
        $processingPkgInfoArray = getProcessingCompanyPackages::getReport($inputArray);
        $inputArray['LMRAEID'] = $executiveId;
        if ($executiveId > 0) $pkgAllSelectedArray = getLMRAEPackages::getReport($inputArray);
        if (count($pkgAllSelectedArray) > 0) {
            if (array_key_exists($executiveId, $pkgAllSelectedArray)) {
                $pkgSelectedArray = $pkgAllSelectedArray[$executiveId];
            }
        }
        $pkgAllClientSelectedArray = getStdDIYClientPackages::getReport($inputArray);
        if (count($pkgAllClientSelectedArray) > 0) {
            if (array_key_exists($executiveId, $pkgAllClientSelectedArray)) {
                $selectedClientPkgArray = $pkgAllClientSelectedArray[$executiveId];
            }
        }
        /*
                    if(count($processingPkgInfoArray) > 0) {
                        $tempPkgInfoArray = array();
                        if(array_key_exists($processingCompanyId, $processingPkgInfoArray)) {
                            $tempPkgInfoArray = $processingPkgInfoArray[$processingCompanyId];
                        }
                    }
                    for($pk=0;$pk<count($pkgSelectedArray);$pk++) {
                        $docId = 0;$documentName = "";$selOpt = "";
                        $docId = trim($pkgSelectedArray[$pk]["pkgID"]);
                        if($pk > 0) {
                            $packageIds .= ", ";
                        }
                        $packageIds .= $docId;
                    }
        */
        for ($pk1 = 0; $pk1 < count($selectedClientPkgArray); $pk1++) {
            $docId = 0;
            $documentName = '';
            $selOpt = '';
            $docId = trim($selectedClientPkgArray[$pk1]['pkgID']);
            if ($pk1 > 0) {
                $clientPackageIds .= ', ';
            }
            $clientPackageIds .= $docId;
        }
        if ($showPkgList == 1) {
            $inArray = [];
            $inArray['PCID'] = $processingCompanyId;

            $pkgs = getMyPackages::getReport($inArray);
            $pkgsKeys = array_keys($pkgs);
            /** Going to list all std pkgs irrespective of their PC - 2012-06-08 **/

        } else {

            $inArray = [];
            $inArray['LMRAEID'] = $executiveId;

            $rsArray = getStdDIYClientPackages::getReport($inArray);
            if (array_key_exists(0, $rsArray)) $stdNoDigiDIYSelectedArray = $rsArray['0'];
            if (array_key_exists(1, $rsArray)) $customizedNoDigiDIYSelectedArray = $rsArray['1'];

            if (count($stdNoDigiDIYSelectedArray) == 0) {
                $inLArray['customDoc'] = '0';
                if (trim($allowESignService) == 1) {
                } else {
                    $inLArray['digiSign'] = '0';
                }
                $stdNoDigiDIYSelectedArray = getDigiAndNoDigiLibPackage::getReport($inLArray);
            }
            /** Going to list all std pkgs irrespective of their PC - 2012-06-08 **/

            // stored proc doesn't exist - 2022-11-08
//            $resultArray = getStdPackages::getReport($inArray);
//            if (array_key_exists(0, $resultArray)) $stdNoDigiSelectedArray = $resultArray['0'];
//            if (array_key_exists(1, $resultArray)) $customizedNoDigiSelectedArray = $resultArray['1'];

            if (!sizeof($stdNoDigiSelectedArray)) {
                $inLArray['customDoc'] = '0';
                if (trim($allowESignService) == 1) {
                } else {
                    $inLArray['digiSign'] = '0';
                }
                $stdNoDigiSelectedArray = getDigiAndNoDigiLibPackage::getReport($inLArray);
                /** Going to list all std pkgs irrespective of their PC - 2012-06-08 **/

            }
        }
        /** Custom Doc Pkg **/
        $inputArray = [];
        if ($showPkgList == 1) {
            $inputArray['PCID'] = $processingCompanyId;
            $CustomDocInfoArray = getPCCustomDocsContent::getReport($inputArray);

            $inputArray['branchID'] = $executiveId;
            $customPkgSelectedArray = getBranchCustomPackages::getReport($inputArray);
            if (count($customPkgSelectedArray) > 0) {
                if (array_key_exists($executiveId, $customPkgSelectedArray)) {
                    $customPkgSelectedArray = $customPkgSelectedArray[$executiveId];
                }
            }

        } else {
            $inputArray['branchID'] = $executiveId;
            $customPkgSelectedArray = getBranchCustomPackages::getReport($inputArray);
            if (count($customPkgSelectedArray) > 0) {
                if (array_key_exists($executiveId, $customPkgSelectedArray)) {
                    $CustomDocInfoArray = $customPkgSelectedArray[$executiveId];
                }
            }
            if (count($customPkgSelectedArray) == 0) {
                $inputArray['PCID'] = $processingCompanyId;
                $CustomDocInfoArray = getPCCustomDocsContent::getReport($inputArray);
            }
        }

        /** Custom Doc Pkg **/

    }
}

$PLOCCTypeArray = [];
$PLOCreditCardTypeArray = [];
$PLOsCCType = '';

$inputArray = [];
$inputArray['LMRAEID'] = $executiveId;
$PLOCCTypeArray = getPLOCreditCardType::getReport($inputArray);

$branchThankYouMsgArray = getBranchThankYouMsg::getReport(['executiveId' => $executiveId]);


if (count($branchThankYouMsgArray ?? []) > 0) {
    for ($tm = 0; $tm < count($branchThankYouMsgArray); $tm++) {
        $thankMsgLMLV = urldecode($branchThankYouMsgArray[$tm]['thankMsgLMLV']);
        $thankMsgLMSV = urldecode($branchThankYouMsgArray[$tm]['thankMsgLMSV']);
        $thankMsgFOP = urldecode($branchThankYouMsgArray[$tm]['thankMsgFOP']);
        $thankMsgSLM = urldecode($branchThankYouMsgArray[$tm]['thankMsgSLM']);
        $thankMsgFSS = urldecode($branchThankYouMsgArray[$tm]['thankMsgFSS']);
        $thankMsgFSI = urldecode($branchThankYouMsgArray[$tm]['thankMsgFSI']);
        $thankMsgUSEI = urldecode($branchThankYouMsgArray[$tm]['thankMsgUSEI']);
        $thankMsgFU = urldecode($branchThankYouMsgArray[$tm]['thankMsgFU']);
        $thankMsgAQ = urldecode($branchThankYouMsgArray[$tm]['thankMsgAQ']);
        $thankAgReg = urldecode($branchThankYouMsgArray[$tm]['thankAgReg']);
        $thankMsgBR = urldecode($branchThankYouMsgArray[$tm]['thankMsgBR']);

        $agentWelcomeMailStatus = $branchThankYouMsgArray[$tm]['agentWelcomeMailStatus'];
        $agentWelcomeMail = urldecode($branchThankYouMsgArray[$tm]['agentWelcomeMail']);
        $agentWelcomeMailSubject = urldecode($branchThankYouMsgArray[$tm]['agentWelcomeMailSubject']);

        $FAborrowerWelcomeMailStatus = $branchThankYouMsgArray[$tm]['FAborrowerWelcomeMailStatus'];
        $FAborrowerWelcomeMail = urldecode($branchThankYouMsgArray[$tm]['FAborrowerWelcomeMail']);
        $FAborrowerWelcomeMailSubject = urldecode($branchThankYouMsgArray[$tm]['FAborrowerWelcomeMailSubject']);
        $ccToAgentFA = ($branchThankYouMsgArray[$tm]['ccToAgentFA']);

        $QAborrowerWelcomeMailStatus = $branchThankYouMsgArray[$tm]['QAborrowerWelcomeMailStatus'];
        $QAborrowerWelcomeMail = urldecode($branchThankYouMsgArray[$tm]['QAborrowerWelcomeMail']);
        $QAborrowerWelcomeMailSubject = urldecode($branchThankYouMsgArray[$tm]['QAborrowerWelcomeMailSubject']);
        $ccToAgentQA = ($branchThankYouMsgArray[$tm]['ccToAgentQA']);

        $brokerRedirectUrl = urldecode($branchThankYouMsgArray[$tm]['brokerRedirectUrl']);
    }
}

$showEditBranchCommission = 'display:none';
if ($allowBranchToSeeCommission == 1) {
    $showEditBranchCommission = 'display:table-row';
}
//Pricing Engine
$showPriceEngineSwitch = 'display:none';
if ($userPriceEngineStatus == 1) {
    $showPriceEngineSwitch = 'display:table-row';
}

if ($thankMsgLMLV == '') $thankMsgLMLV = $glThankYouMsg;
if ($thankMsgLMSV == '') $thankMsgLMSV = $glThankYouMsg;
if ($thankMsgFOP == '') $thankMsgFOP = $glThankYouMsg;
if ($thankMsgSLM == '') $thankMsgSLM = $glThankYouMsg;
if ($thankMsgFSS == '') $thankMsgFSS = $glThankYouMsg;
if ($thankMsgFSI == '') $thankMsgFSI = $glThankYouMsg;
if ($thankMsgUSEI == '') $thankMsgUSEI = $glThankYouMsg;
if ($thankMsgFU == '') $thankMsgFU = $glThankYouMsg;
if ($thankMsgAQ == '') $thankMsgAQ = $glThankYouMsg;
if ($thankMsgBR == '') $thankMsgBR = $glThankYouMsgBR;
if ($thankAgReg == '') $thankAgReg = stripslashes(addslashes($glBrokerTAC));

$pkgListResult = [];

//    if ($tabNumb == 2 && $userRole != 'Super') {
if ($tabNumb == 2) {
    /* List all PCs selected pkgs for PC Mgr & list assigned branch docs for Branch users  */

    $ip['activeStatus'] = 1;
    if ($allowESignService == 1 || PageVariables::isSuper()) $ip['digiSign'] = 1;
    else $ip['digiSign'] = 0;
    $ip['PCID'] = $processingCompanyId;
    if ($userRole == 'Branch') $ip['branchID'] = $executiveId;
    //

    if ($userType == 'PLO') $ip['DIYClient'] = '1';
    $ip['opt'] = 'category';
    if (count($fileModules) > 0) $ip['selectedMC'] = implode(',', array_values($fileModules));

    $pkgListResult = getMyFileAutoGeneratedDocs::getReport($ip);
}
if (count($PLOCCTypeArray) > 0) {
    if (array_key_exists($executiveId, $PLOCCTypeArray)) {
        $PLOCreditCardTypeArray = $PLOCCTypeArray[$executiveId];
    }
}

for ($pcl = 0; $pcl < count($PLOCreditCardTypeArray); $pcl++) {
    if ($pcl > 0) {
        $PLOsCCType .= ',';
    }
    $PLOsCCType .= trim($PLOCreditCardTypeArray[$pcl]['creditCardType']);

}
if ($userType == '') $userType = 'LMR AE';
$glUserTypeArray = ['LMR AE', 'PLO'];

if ($userType == 'PLO') {
    $tcDiv = 'display:block';
    $pkgDiv = 'display:none';
    if ($paymentGateway == 'Authorize.net') {
        $pyDiv = 'display:block';
    }
    if ($paymentGateway == 'Paypal') {
        $payDiv = 'display:block';
    }
    if ($paymentGateway == 'Free') {
        $eCheckDiv = 'display:none';
    }
    if ($paymentGateway == 'None') {
        $eCheckDiv = 'display:block';
    }
}
if (in_array('HMLO', $fileModules) || in_array('EF', $fileModules)) $isHMLO = 1;
if (in_array('loc', $fileModules) || in_array('L', $fileModules) || in_array('C', $fileModules)) $isLOC = 1;
if (trim($TAC) == '' || trim($TACQA) == '') {
    if (in_array('HMLO', $fileModules)) {
        $TAC = $glHMLOTAC;
        $TACQA = $glHMLOTAC;
    } else {
        $inArray['companyName'] = $company;
        $inArray['website'] = $website;
        $inArray['state'] = $appStates;
        $TAC = Strings::getDefaultTAC($inArray);
        $TACQA = Strings::getDefaultTAC($inArray);
    }
} else {
    $TAC = $TAC;
    $TACQA = $TACQA;
}
if ($tabNumb == 1) $newHref = ''; else  $newHref = "onclick=\"javascript:showTabUrl('1','" . cypher::myEncryption($executiveId) . "');\"";

$pcAcqualifyDetails = [];
$pcAcqualifyId = 0;
if ($PCID > 0) {
    $pcAcqualifyDetails = getacqualifyPCDetails::getReport(['pcid' => $PCID]);
    $pcAcqualifyId = $pcAcqualifyDetails[0]['accountId'];
}
?>
<div class="card card-custom p-0 card-sticky " id="kt_page_sticky_card">
    <div class="card-header px-2 py-2 border" style="">
        <div class="pipelineNavigation ">
            <ul class="nav nav-pills">
                <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                    <a class="nav-link px-4 py-2  <?php if ($tabNumb == 1) {
                        echo 'active';
                    } ?>" href="#" data-toggle="tab" <?php echo $newHref; ?>>
                        <span class="nav-text font-weight-bold" id="button_branchinfo">Branch Info</span>
                    </a>
                </li>
                <?php
                if ($executiveId > 0) {
                    if ($tabNumb == 2 || $executiveId == 0) {
                        $newHref = '';
                    } else if ($executiveId > 0) {
                        $newHref = "onclick=\"javascript:showTabUrl('2','" . cypher::myEncryption($executiveId) . "');\"";
                    }
                    ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 2) {
                            echo 'active';
                        } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                            <span class="nav-text font-weight-bold" id="button_doclib">Docs Library</span>
                        </a>
                    </li>
                    <?php
                    if ($tabNumb == 9 || $executiveId == 0) {
                        $newHref = '';
                    } else if ($executiveId > 0) {
                        $newHref = "onclick=\"javascript:showTabUrl('9','" . cypher::myEncryption($executiveId) . "');\"";
                    }
                    ?>
                    <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                        <a class="nav-link px-4 py-2 <?php if ($tabNumb == 9) {
                            echo 'active';
                        } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                            <span class="nav-text font-weight-bold" id="button_wfi"> Web Form Integration</span>
                        </a>
                    </li>
                    <?php
                    if ($userType == 'PLO') {
                        if ($tabNumb == 5 || $executiveId == 0) {
                            $newHref = '';
                        } else if ($executiveId > 0) {
                            $newHref = "onclick=\"javascript:showTabUrl('5','" . cypher::myEncryption($executiveId) . "');\"";
                        }
                        ?>
                        <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                            <a class="nav-link px-4 py-2 <?php if ($tabNumb == 5) {
                                echo 'active';
                            } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                                <span class="nav-text font-weight-bold" id="button_addplans">Add Plans</span>
                            </a>
                        </li>
                        <?php
                    } else {
                        if ($tabNumb == 7 || $executiveId == 0) {
                            $newHref = '';
                        } else if ($executiveId > 0) {
                            $newHref = "onclick=\"javascript:showTabUrl('7','" . cypher::myEncryption($executiveId) . "');\"";
                        }
                        if (in_array('HMLO', $fileModules)) { //do nothing

                        }
                        if (in_array('LM', $fileModules)) {
                            /** Hide the Legal Contract tab in HMLO Modules on March 18, 2017 **/
                            ?>
                            <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                                <a class="nav-link px-4 py-2 <?php if ($tabNumb == 7) {
                                    echo 'active';
                                } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                                    <span class="nav-text font-weight-bold" id="button_legalcontract">Legal Contract</span>
                                </a>
                            </li>
                            <?php
                        }
                    }
                    ?>
                    <?php if ($isHMLO == 1 || $isLOC == 1) {
                        if ($tabNumb == 11) {
                            $newHref = '';
                        } else {
                            $newHref = "onclick=\"javascript:showTabUrl('11','" . cypher::myEncryption($executiveId) . "');\"";
                        }
                        ?>
                        <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
                            <a class="nav-link px-4 py-2 <?php if ($tabNumb == 11) {
                                echo 'active';
                            } ?>" data-toggle="tab" <?php echo $newHref; ?>>
                                <span class="nav-text font-weight-bold" id="button_reqdocs">Required Docs</span>
                            </a>
                        </li>
                        <?php
                    }
                    ?>
                <?php } ?>
            </ul>
        </div>
        <div class="float-right">
            <div class="row">
                <?php
                if (($userGroup == 'Employee') && ($executiveId == 0)) {
                    if ($PCAllowToCreate == 1) { ?>
                        <span class="text-danger font-weight-boldest">You have subscribed for <?php echo $noOfUsersAllowed ?> users. You have <?php echo $noOfUsersCreated ?> users in your list.</span>
                        <?php
                    }
                } ?>
                <?php if ($tabNumb == 1) { ?>
                    <div class="ml-5 mr-5">
                        <button id="toggle-all" name="toggle-all" class="btn btn-primary">Open All</button>
                    </div>
                <?php } else { ?>
                    <div class="ml-5 mr-5">
                        <a href="javascript:void(0)"
                           class="btn btn-sm btn-primary btn-text-primary  btn-icon ml-2 tooltipClass "
                           data-placement="left"
                           title="<?php echo $LMRFirstName . ' ' . $LMRLastName . ' (' . $email . ')'; ?>">
                            <i class=" fas fa-info-circle "></i>
                        </a>
                    </div>
                    <?php
                } ?>
            </div>
        </div>
    </div>
    <div class="card-body p-2 m-0" id="employeeCreateDiv">

        <?php if ($tabNumb == 1) require __DIR__ . '/LMRExecutiveForm.php';
        if ($tabNumb == 2) require __DIR__ . '/branchPackageAndPaymentForm.php';
        if ($tabNumb == 9) require __DIR__ . '/branchWebForm.php';
        //   if ($tabNumb == 3)  include "uploadBranchDocForm.php";
        if (PageVariables::isSuper() && $tabNumb == 4) {
            require __DIR__ . '/LMRFeeForm.php';
        }
        if ($userType == 'PLO' && $tabNumb == 5) {
            require __DIR__ . '/LMRPlans.php';
        }
        //if ($userType == "PLO" && $tabNumb == 6) include "PLOHearAbout.php";
        if ($userType == 'LMR AE' && $tabNumb == 7) {
            require __DIR__ . '/legalContractForBranch.php';
        }
        //if ($userType == "LMR AE" && $tabNumb == 8) include "branchHearAbout.php";

        if ($tabNumb == 10) {
            require __DIR__ . '/branchServices.php';
        }

        if ($tabNumb == 11 && ($isHMLO == 1 || $isLOC == 1)) {
            require __DIR__ . '/branchWebFormChecklist.php';
        }
        ?>
    </div>
</div>

<?php if ($tabNumb == 1) { ?>
    <script type='text/javascript'>

        function hideSelectAllLoanPrograms() {
            var ks = 0;
            try {
                eval("obj = document.LMRForm['branchModuleType[]']");
                len = obj.length;
            } catch (e) {
            }

            for (var i = 0; i < obj.length; i++) {
                if (obj[i].selected) ks++;
            }
            if (ks > 1) {
                $('.selectAllHideShow').show();
            } else {
                $('.selectAllHideShow').hide();
            }
        }

        function showAndHideModuleServiceTypesForBranch(t, val, opt) {
            if (t == 'branchModuleType[]') {
                if (opt == 'show') {
                    $('#div_' + val).find(':checkbox').each(function () {
                        $(this).attr('checked', true);
                        $(this).prop("checked", true);
                    });
                    if ($('#div_' + val).length > 0) {
                        eval("document.getElementById('div_" + val + "').style.display = 'block'");
                    }
                } else {
                    $('#div_' + val).find(':checkbox').each(function () {
                        $(this).attr('checked', false);
                        $(this).prop("checked", false);
                    });
                    if ($('#div_' + val).length > 0) {
                        eval("document.getElementById('div_" + val + "').style.display = 'none'");
                    }
                }
            }
        }

        $(".branchInfo").click(function () {
            $("#subBranchInfo").toggle('fast');
            $(this).not(".caret").children(".fa").toggleClass("fa-plus-square fa-minus-square");
            $(this, ".caret").children(".fa").toggleClass("fa-caret-right fa-caret-down");
            return false;
        });

        $(".branchConfiguration").click(function () {
            $("#subBranchConfiguration").toggle('fast');
            $(this).not(".caret").children(".fa").toggleClass("fa-minus-square fa-plus-square");
            $(this, ".caret").children(".fa").toggleClass("fa-caret-right fa-caret-down");
            return false;
        });

        $(".userPermission").click(function () {
            $("#subUserPermission").toggle('fast');
            $(this).not(".caret").children(".fa").toggleClass("fa-minus-square fa-plus-square");
            $(this, ".caret").children(".fa").toggleClass("fa-caret-right fa-caret-down");
            return false;
        });

        $(".branchUserPermission").change(function () {
            let opt = parseInt($('#allowBranchToLogin').val()) ?? 0;

            if (userRole == 'Super' || userRole == 'Manager') {
                if (opt == 1) {
                    document.getElementById('allowLMRAEToEditFile').checked = false;
                    document.getElementById('allowLMRAEToEditFile1').checked = false;
                    document.getElementById('allowLMRAEToEditFile2').checked = true;
                }
                //toggleSwitchNew('seePublicId', 'allowBranchToSeePublicNotes', opt, '0' );
                toggleSwitchNew('allowLMRToOnOffAgentLog', 'allowLMRToOnOffAgentLogin', opt, '0');
                toggleSwitchNew('allowLMRToEditAgentPro', 'allowLMRToEditAgentProfile', opt, '0');
                toggleSwitchNew('allowToAddAgt', 'allowToAddAgent', opt, '0');
                toggleSwitchNew('allowBrToCreateFiles', 'allowBranchToCreateFiles', opt, '0');
                toggleSwitchNew('allowBrToCreateTasks', 'allowBranchToCreateTasks', opt, '0');
                toggleSwitchNew('allowBranchToSeeDash', 'allowBranchToSeeDashboard', opt, '0');
                toggleSwitchNew('allowBranchToSeeComm', 'allowBranchToSeeCommission', opt, '0');

                toggleSwitchNew('seePri', 'seePrivate', '0', '0');
                toggleSwitchNew('allowToUpFileAdminSec', 'allowToUpdateFileAdminSection', '0', '0');
                toggleSwitchNew('allowLMRAEToAccDocs', 'allowLMRAEToAccessDocs', '0', '0');
                toggleSwitchNew('allowLMRAEToEditComm', 'allowLMRAEToEditCommission', '0', '0');
                toggleSwitchNew('allowedToDeleteUpDocs', 'allowedToDeleteUplodedDocs', '0', '0');
                toggleSwitchNew('allowedToEdNotes', 'allowedToEditOwnNotes', '0', '0');
                toggleSwitchNew('allowedMassEmail', 'allowEmailCampaign', '0', '0');
                toggleSwitchNew('allowToUserSendFax', 'allowToSendFax', '0', '0');
                toggleSwitchNew('subscribeToHOMEForPC', 'subscribeToHOME', '0', '0');
                toggleSwitchNew('allowedToExcelRep', 'allowedToExcelReport', '0', '0');
            }
            return false;
        });

        /* validation for upload branch logo */
        var _URL = window.URL || window.webkitURL;
        $("#logoFile").change(function (e) {
            var file, img;
            if ((file = this.files[0])) {
                img = new Image();
                img.onload = function () {
                    if (this.width > 25000 && this.height > 15000) {   /* i added a power of 100 for now to stop this */
                        toastrNotification('The Image size should be 150px by 250px', 'error');
                        $("#logoFile").val('');
                        return false;
                    } else {
                        return true;
                    }
                };
                img.src = _URL.createObjectURL(file);
            }
        });

        //Expand All / Collapse All
        $('#toggle-all').click(function () {
            var status = $(this).text();
            if (status == 'Close All') {
                $(this).text('Open All');
                $('.accordion').hide();
                $('.icon-nm').addClass('ki-arrow-down').removeClass('ki-arrow-up');
            } else if (status == 'Open All') {
                $(this).text('Close All');
                $('.accordion').show();
                $('.icon-nm').addClass('ki-arrow-up').removeClass('ki-arrow-down');
            }
        });

    </script>
<?php } ?>

<!-- branchForm.php -->
