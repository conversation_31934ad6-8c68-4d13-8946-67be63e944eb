<?php
global $executiveId, $oBranch, $modulesArray, $email, $processingCompanyId, $appStates, $referralSiteCode, $company, $city, $LMRName, $tollFree,
$cellNumber, $fax, $address1, $zipCode, $LMRClientTypeArray, $useMyServerSetting, $myTimeZone;
global $PCModulesArray, $userRole, $PCAllowToCreate, $userGroup, $allowEmpToCreateBranch, $isPublicWebform, $packageIds,
$tempPkgInfoArray, $PCAllowToCreateBranch, $PLOsCCType, $LMRFirstName, $LMRLastName, $LMRLogo, $phone,
$allowEmpToCreateAloware, $serviceProvider, $glUserTypeArray, $userType;
global $website, $PCID, $sponsorName, $globalAcctNo, $displayLinkToFICO, $subscriberID, $showPoweredByTMPLink, $askPaymentBeforeLMR, $askReferralAgent,
$allowClientToUploadDocs, $allowAgentToSeeFile, $allowToSendFileDesignation, $isPrimary;
global $LMRBccEmail, $LMRCcEmail, $branchAELoginUrl, $allowcaptcha, $pcAcqualifyStatus, $pcAcqualifyId, $allowToViewCreditScreening, $PCListArray,
$paymentToCompany, $subscribedOption, $allowAddOn, $allowBranchToAccessRAM, $subscribedOption, $paymentToCompany, $allowAddOn,
$allowBranchToLogin, $AllowToCreateAloware, $seePrivate, $allowLMRAEToEditFile;
global $allowToUpdateFileAdminSection, $allowLMRAEToAccessDocs, $allowBranchToSeeCommission, $showEditBranchCommission, $allowLMRAEToEditCommission,
$allowLMRToOnOffAgentLogin, $allowLMRToEditAgentProfile, $allowToAddAgent;
global $allowBranchToCreateFiles, $allowBranchToCreateTasks, $allowBranchToSeeDashboard, $allowUserToDeleteUploadedDocs, $allowUserToEditOwnNotes,
$allowtoEditCCInfo, $allowEmailCampaign, $allowToSendFax, $fileModules, $subscribeToHOME;
global $allowedToExcelReport, $isPLO, $allowToSendHomeownerLink, $changeDIYPlan, $allowToLASubmit, $allowClientToAccessDocs, $allowEditToIR,
$allowWorkflowEdit, $allowToLockLoanFileBranch, $allowBranchToGetBorrowerUploadDocsNotification, $glThirdPartyServices;
global $thirdPartyServices, $thirdPartyServicesLegalDocs, $allowPCUsersToMarketPlace, $allowToViewMarketPlace,
$shareThisFile, $allowToSubmitOffer, $enable2FAAuthentication, $TwoFAType, $pcPriceEngineStatus, $userPriceEngineStatus, $branchServicesArray,
$glLMRClientTypeArray;
global $notifyBODocUpload, $notifyLODocUpload, $notifyBrokerDocUpload, $notifyDocUploadRequest, $notifyNewFileCreated;
global $DocUploadBcc, $DocUploadCc, $showPriceEngineSwitch, $loanpassLogin, $loanpassPassword, $allowToAccessInternalLoanProgram, $allowToManageDraws;

global $allowToViewAutomationPopup, $allowToCopyFile, $allowToMassUpdate, $allowUsersToSendEsignLinkToAll;
use models\composite\oBranch\checkBranchPreferredBroker;
use models\composite\oBranch\getPCBranchAssociateInfo;
use models\composite\oBranch\savePreferredAgentForBranch;
use models\composite\oBroker\checkBrokerExists;
use models\composite\oBroker\saveAgentInfo;
use models\composite\oEmployee\getUserAssignedWorkflow;
use models\composite\oPC\getEMailServerInfo;
use models\composite\oPC\getMyDetails;
use models\composite\oPC\PCInfo;
use models\composite\oThirdPartyServices\getThirdPartyServicesUserDetails;
use models\composite\oWorkflow\getPCWorkflow;
use models\constants\accessRestrictionPC;
use models\constants\accessSecondaryWFPC;
use models\constants\gl\glRAMAccessPC;
use models\constants\gl\glThirdPartyServicesCRA;
use models\constants\gl\glUserGroup;
use models\constants\SMSServiceProviderArray;
use models\constants\timeZoneArray;
use models\Database2;
use models\cypher;
use models\lendingwise\tblBranch;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;

$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
$glRAMAccessPC = glRAMAccessPC::$glRAMAccessPC;
$accessRestrictionPC = accessRestrictionPC::$accessRestrictionPC;
$SMSServiceProviderArray = SMSServiceProviderArray::$SMSServiceProviderArray;
$timeZoneArray = timeZoneArray::$timeZoneArray;

$PCInfo = PCInfo::getReport(['PCID' => $processingCompanyId]);
$allowAutomation = $PCInfo['allowAutomation'];

$PCName = '';
$resArray = [];
$allowToViewFSSF = 0;
$allowToCFPBSubmitForPC = 0;
$PCModulesInfoArray = [];
$selectAllHideShow = 'display : none;';
$pcCaptcha = '';

$pcCaptcha = $PCInfo['captcha'];
$private = 0;
if (isset($_REQUEST['priv'])) {
    $private = trim($_REQUEST['priv']);
}
if ($executiveId > 0) {
    $PCBranchAssocationInfoArray = [];
    $PBID = 0;
    $PCBranchAssocationInfoArray = getPCBranchAssociateInfo::getReport(['branchId' => $executiveId, 'activeStatus' => '1']);
    if (count($PCBranchAssocationInfoArray) > 0) {
        if (array_key_exists($executiveId, $PCBranchAssocationInfoArray)) {
            $PBID = trim($PCBranchAssocationInfoArray[$executiveId]['PBID']);
        }
    }
}

if (!$modulesArray) {
    $modulesArray = [];
}

if (!$LMRClientTypeArray) {
    $LMRClientTypeArray = [];
}

if (count($modulesArray) > 1) {
    $selectAllHideShow = 'display : block;';
}

$bIp = ['email' => $email];


$PCInfoArray = getMyDetails::getReport(['PCID' => $processingCompanyId]);
if (count($PCInfoArray) > 0) {
    if (array_key_exists($processingCompanyId, $PCInfoArray)) {
        $allowToCFPBSubmitForPC = trim($PCInfoArray[$processingCompanyId]['allowToCFPBSubmitForPC']);
    }
}

$glThirdPartyServicesCRA = glThirdPartyServicesCRA::init();
$thirdPartyServiceCSRArray = explode(',', PageVariables::$thirdPartyServiceCSR);
$thirdPartyServicesUserDetails = getThirdPartyServicesUserDetails::getReport('Branch', $executiveId);

$showThirdPartyEditSwitch = 'display:none';
if ($thirdPartyServices == 1) {
    $showThirdPartyEditSwitch = 'display:table-row';
}
$showThirdPartyEditSwitchLegalDocs = 'display:none';
if ($thirdPartyServicesLegalDocs == 1) {
    $showThirdPartyEditSwitchLegalDocs = 'display:table-row';
}

$stateArray = Arrays::fetchStates();
/** Fetch all States **/

$branchCountyInfo = Arrays::fetchStateCounty(['keyNeeded' => 'no', 'stateCode' => $appStates]);
/** Fetch all States **/

$smtpDisp = 'none';

$myServerInfoArray = [];
$ESID = 0;
$hostName = '';
$SMTPUserName = '';
$pwd = '';
$portNo = '';
$replyTo = '';
$bounceMail = '';
if ($executiveId > 0) {
    $myInpArray['senderIds'] = $executiveId;
    $myInpArray['senderUserType'] = 'Branch';
    $myServerInfoArray = getEMailServerInfo::getReport($myInpArray);
    if (count($myServerInfoArray) > 0) {
        $hostName = $myServerInfoArray['Branch'][$executiveId]['hostName'];
        $SMTPUserName = $myServerInfoArray['Branch'][$executiveId]['userName'];
        $pwd = $myServerInfoArray['Branch'][$executiveId]['pwd'];
        $portNo = $myServerInfoArray['Branch'][$executiveId]['portNo'];
        $replyTo = $myServerInfoArray['Branch'][$executiveId]['replyTo'];
        $bounceMail = $myServerInfoArray['Branch'][$executiveId]['bounceMail'];
        $ESID = $myServerInfoArray['Branch'][$executiveId]['ESID'];
    }
    $tblBranch = tblBranch::Get(['executiveId' => $executiveId]);
} else {
    $tblBranch = new tblBranch();
}
if ($processingCompanyId > 0) {
    $exResultArray = [];
    $exInArray['TABLENAME'] = 'tblProcessingCompany';
    $exInArray['FIELDNAMEARRAY'] = ['processingCompanyName', 'allowToViewFSSF'];

    $exInArray['CONDITION'] = " where activeStatus = '1' and PCID = '" . $processingCompanyId . "'";
    $exResultArray = Database2::getInstance()->getSelectedFieldsForFile($exInArray);
    if (count($exResultArray) > 0) {
        $PCName = trim($exResultArray['processingCompanyName']);
        $allowToViewFSSF = trim($exResultArray['allowToViewFSSF']);
    }
}

$PCWorkflowArray = [];
if ($processingCompanyId > 0) {
    $PCWorkflowArray = getPCWorkflow::getReport(['PCID' => $processingCompanyId]);
}
$assignedWorkflow = [];
$assignedWFIDs = [];

if ($executiveId > 0) {
    $assignedWorkflow = getUserAssignedWorkflow::getReport(['UID' => $executiveId, 'UType' => 'Branch']);
}
for ($pc = 0; $pc < count($assignedWorkflow); $pc++) {
    $assignedWFIDs[] = trim($assignedWorkflow[$pc]['WFID']);
}
if (count($LMRClientTypeArray) > 0) {
    $LMRClientTypeInfoArray = [];
    if (array_key_exists($processingCompanyId, $LMRClientTypeArray)) {
        $LMRClientTypeInfoArray = $LMRClientTypeArray[$processingCompanyId];
    }
    for ($c1 = 0; $c1 < count($LMRClientTypeInfoArray); $c1++) {
        $tempArray = [];
        $clientType = '';
        $tempArray = $LMRClientTypeInfoArray[$c1];
        $clientType = trim($tempArray['LMRClientType']);
        if ($c1 > 0) {
            $LMRClientType .= ', ';
        }
        $LMRClientType .= $clientType;
    }
    $LMRClientTypeArray = explode(', ', $LMRClientType);
}
if ($useMyServerSetting == 1) {
    $smtpDisp = 'block';
}

if (trim($myTimeZone) == '') {
    $myTimeZone = CONST_SERVER_TIME_ZONE;
}
$moduleTypes = '';
if (count($PCModulesArray) > 0) {
    for ($g = 0; $g < count($PCModulesArray); $g++) {
        $tempArray1 = $PCModulesArray[$g];
        $moduleType = trim($tempArray1['moduleCode']);
        $PCModulesInfoArray [] = $moduleType;
    }
    $moduleTypes = implode(', ', $PCModulesInfoArray);
}
$allowToEditLoanStage = 0;
if (isset($tblBranch->allowToEditLoanStage)) {
    $allowToEditLoanStage = $tblBranch->allowToEditLoanStage;
}

if($PCID == $processingCompanyId) {
    $showManageDrawsToggle = PageVariables::PC()->drawManagement && PageVariables::PC()->enableDrawManagementV2 ?? 0;
} else {
    $showManageDrawsToggle = 1;
}

?>
<?php
if (($userRole == 'Super') || ($executiveId > 0) || ($PCAllowToCreate == 1) || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1) || ($userRole == 'Manager') || $isPublicWebform == 1) {
    ?>
<form name="LMRForm" id="LMRForm" ENCTYPE="multipart/form-data" method="post"
      action="<?php echo CONST_BO_URL; ?>branchSave.php">
    <?php
} else {
    ?>
    <form name="LMRForm" id="LMRForm" ENCTYPE="multipart/form-data" method="post" action="javascript:void(0);">
        <?php
}
?>
        <input type="hidden" name="eId" id="eId" value="<?php echo $executiveId; ?>">
        <input type="hidden" name="packageIds" id="packageIds" value="<?php echo $packageIds; ?>">
        <input type="hidden" name="pkgCnt" id="pkgCnt" value="<?php echo count($tempPkgInfoArray) ?>">
        <input type="hidden" name="allowToCreateBranch" id="allowToCreateBranch"
               value="<?php echo $PCAllowToCreateBranch ?>">
        <input type="hidden" name="selectedCreditCardType" id="selectedCreditCardType"
               value="<?php echo $PLOsCCType ?>">
        <input type="hidden" name="PBID" id="PBID" value="<?php echo $PBID ?>">
        <input type="hidden" name="userRole" id="userRole" value="<?php echo $userRole ?>">
        <input type="hidden" name="isPublicWebform" id="isPublicWebform" value="<?php echo $isPublicWebform ?>">

        <input type="hidden" name="ESID" value="<?php echo $ESID ?>">
        <?php if ($userRole != 'Super') { ?>
            <input type="hidden" name="assignedToProcessingCompany" id="assignedToProcessingCompany"
                   value="<?php echo $processingCompanyId ?>">
        <?php } ?>

        <div class="card card-custom mb-2">
            <div class="card-header card-header-tabs-line bg-gray-100">
                <div class="card-title">
                    <h3 class="card-label">
                        Branch Info:
                    </h3>
                </div>
                <div class="card-toolbar">
                    <a href="javascript:void(0);"
                       class="btn btn-light-primary btn-text-primary btn-hover-primary btn-icon"
                       data-card-tool="toggle">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                </div>
            </div>
            <div class="card-body px-4 m-0 accordion">
                <div class="form-group row">
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="email">Email</label>
                        <?php if ($executiveId == 0) { ?>
                            <input class="form-control  mandatory" type="text" name="email" id="email"
                                   value="<?php echo $email ?>"
                                   size="40" maxlength="75" onblur="return checkBranchEmailExists();"
                                   autocomplete="off">
                        <?php } else {
                            ?>
                            <input type="hidden" name="email" id="email" value="<?php echo $email; ?>">

                            <?php
                            echo " <div class=\"input-group\"><input  class=\"form-control form-controller-solid  datatable-input\" type=\"text\"  value=\"$email\" disabled>
                        <div class=\"input-group-append\"><span class=\"input-group-text\"><a data-id=\"userId=" . cypher::myEncryption($executiveId) . '&userType=' . cypher::myEncryption('Branch') . '&email=' . $email . "\" href='' data-href=\"" . CONST_URL_POPS . "changeEmailPopup.php\" data-name=\"" . $LMRName . " > Change Email \"  data-toggle='modal' data-target='#exampleModal1'><i class='fas fa-pencil-alt' title=\"Click here to change email address\"></i></a></span>
                        </div>
                        </div>
                        ";
                        } ?>
                    </div>
                    <?php if ($executiveId == 0) { ?>
                        <div class="col-lg-6 mb-7">
                            <label class="font-weight-bold" for="confirmEmail">Confirm Email</label>
                            <input class="form-control  mandatory" type="text" name="confirmEmail"
                                   id="confirmEmail" size="40" maxlength="75">
                        </div>
                    <?php } ?>
                </div>
                <div class="form-group row">
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="LMRFirstName">Branch User First Name</label>
                        <input class="form-control  mandatory" type="text" name="LMRFirstName" id="LMRFirstName"
                               size="27"
                               maxlength="30" value="<?php echo $LMRFirstName ?>">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="LMRLastName">Branch User Last Name</label>
                        <input type="text" name="LMRLastName" id="LMRLastName"
                               value="<?php echo $LMRLastName ?>"
                               size="27" class="form-control" maxlength="50" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="LMRName">Branch Name</label>
                        <input class="form-control  mandatory" type="text" name="LMRName" id="LMRName" size="27"
                               maxlength="50" value="<?php echo $LMRName ?>">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="company">Company Name</label>
                        <input type="text" name="company" id="company" value="<?php echo $company ?>"
                               size="27" maxlength="55" class="mandatory form-control" autocomplete="off">
                    </div>

                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold">Time Zone</label>
                        <select name="timeZone" id="timeZone" class="form-control">
                            <?php
                            $timeZoneKeyArray = [];
$timeZoneKeyArray = array_keys($timeZoneArray);
for ($tz = 0; $tz < count($timeZoneKeyArray); $tz++) {
    $timeZone = trim($timeZoneKeyArray[$tz]);
    ?>
                                <option value="<?php echo $timeZone ?>" <?php echo Arrays::isSelected($timeZone, $myTimeZone); ?>><?php echo $timeZoneArray[$timeZone] ?></option>
                                <?php
}
?>
                        </select>
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="tollFree">Toll Free/Main</label>
                        <input type="text" class="form-control mask_phone" name="tollFree" id="tollFree"
                               value="<?php echo $tollFree ?>" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold">Address</label>
                        <input type="text" name="address1" id="address1" value="<?php echo $address1 ?>"
                               class="form-control"
                               size="40" maxlength="75" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold">City</label>
                        <input type="text" name="city" id="city" value="<?php echo $city ?>" size="20"
                               class="form-control"
                               maxlength="30" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold">State</label>
                        <select name="state" id="state" class="form-control"
                                onchange="populateStateTimeZone('LMRForm', 'state', 'timeZone');populateBranchCounty('LMRForm', 'state', 'county');">
                            <option value=''> - Select -</option>
                            <?php
for ($j = 0; $j < count($stateArray); $j++) {
    $sOpt = '';
    $sOpt = Arrays::isSelected(trim($stateArray[$j]['stateCode']), $appStates);
    echo "<option value=\"" . trim($stateArray[$j]['stateCode']) . "\" " . $sOpt . '>' . trim($stateArray[$j]['stateName']) . '</option>';
}
?>
                        </select>
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold">Zip Code</label>
                        <input type="text" name="zipCode" id="zipCode" value="<?php echo $zipCode ?>"
                               class="form-control zipCode" size="10" maxlength="10" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold">Cell Number</label>
                        <input type="text"
                               class="form-control mask_cell <?php if ($allowEmpToCreateAloware == 0) {
                                   echo 'ignoreValidation';
                               } ?> " name="cellNumber" id="cellNumber"
                               value="<?php echo $cellNumber ?>" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold">
                            Please select service provider
                            <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                               data-toggle="tooltip"
                               title="Please select service provider if you would like to receive task reminders via SMS"></i></label>
                        <select name="serviceProvider" id="serviceProvider" class="form-control">
                            <option value="">- Select -</option>
                            <?php
                            $spKeyArray = array_keys($SMSServiceProviderArray);
for ($sp = 0; $sp < count($spKeyArray); $sp++) {
    $spKey = $spKeyArray[$sp];
    $myServiceProvider = $SMSServiceProviderArray[$spKey];
    ?>
                                <option value="<?php echo $spKey ?>" <?php echo Arrays::isSelected($spKey, $serviceProvider); ?>><?php echo $myServiceProvider ?></option>
                                <?php
}
?>
                        </select>
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold">Local/Direct Phone</label>
                        <input type="text" class="form-control mask_phone" name="phone" id="phone"
                               value="<?php echo $phone ?>" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold">Fax</label>
                        <input type="text" class="form-control mask_cell" name="fax" id="fax"
                               value="<?php echo $fax ?>" autocomplete="off">
                    </div>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold" for="website">Website</label>
                        <input type="text" name="website" id="website" value="<?php echo $website ?>"
                               size="40" maxlength="150" class="form-control" autocomplete="off">
                    </div>
                    <?php if ($userRole == 'Super' || ($processingCompanyId == 1379 && $userRole == 'Manager')) { ?>
                        <div class="col-lg-6 mb-7">
                            <label class="font-weight-bold">Subscriber ID</label>
                            <input type="text" name="subscriberID" value="<?php echo $subscriberID ?>"
                                   class="form-control"
                                   size="30" maxlength="25">
                        </div>
                    <?php } else { ?>
                        <input type="hidden" name="showPoweredByTMPLink" id="showPoweredByTMPLink"
                               value="<?php echo $showPoweredByTMPLink ?>">
                        <?php
                    }
?>
                    <?php $logoClass = 'col-lg-6';
$src = '';
if ($LMRLogo != '') {
    $logoExit = file_exists(CONST_BRANCH_LOGO_PATH . $LMRLogo);
    if ($logoExit) {
        $src = CONST_BRANCH_LOGO_URL . $LMRLogo;
        $logoClass = 'col-lg-4';
    } else {
        $logoClass = 'col-lg-6';
    }
} else {
    $logoClass = 'col-lg-6';
} ?>
                    <div class="<?php echo $logoClass; ?> mb-7">
                        <label class="font-weight-bold" for="logoFile">Logo</label>
                        <input type="FILE" name="logoFile" id="logoFile" size="30" value="" class="branchLogo"
                               autocomplete="off" class="form-control">
                        <span class="form-text text-muted">(The file types allowed are JPEG,PNG,GIF.)</span>
                    </div>
                    <?php if ($logoClass == 'col-lg-4') { ?>
                        <div class="col-lg-1 mb-7">
                            <a class="manualPopover change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1 my-10"
                               data-name="Branch Logo"
                               data-html="true" data-content="<img src='<?php echo $src; ?>'>"><i
                                        class="fa fa-eye"></i>
                            </a>
                        </div>
                        <div class="col-lg-1 mb-7">
                            <a class="change btn btn-xs btn-light btn-text-primary btn-hover-primary btn-icon m-1 my-10"
                               href="javascript:branchLogoDelete('<?php echo cypher::myEncryption($executiveId) ?>')"
                               data-html="true" data-toggle="tooltip"><i class="tooltipClass flaticon2-trash"
                                                                         title="Click to Delete Logo"></i>
                            </a>
                        </div>
                    <?php } ?>
                    <div class="col-lg-6 mb-7">
                        <label class="font-weight-bold"> <?php
        if ($userRole == 'Manager') {
            ?>
                                Branch Type
                                <?php
        } else {
            ?>
                                User Type
                            <?php }
        ?></label>
                        <?php
                        if ($userRole == 'Super') {
                            for ($u = 0; $u < count($glUserTypeArray); $u++) {
                                $eleAndDle = ' disabled ';
                                if ($glUserTypeArray[$u] == 'PLO') {
                                    $showlink = "showOrhideTCDiv('tcDiv','show')";
                                    $showName = 'DIY';
                                } else {
                                    $showlink = "showOrhideTCDiv('tcDiv','hide')";
                                    $showName = 'Branch';
                                }
                                if ($userRole == 'Super') {
                                    $eleAndDle = ' enabled ';
                                } else {
                                    $eleAndDle = ' disabled ';
                                }
                                echo "<div class=\"radio-inline\">";
                                echo "<label for='userType$u' class=\"radio radio-solid font-weight-bolder\">
                                            <input type=\"radio\" name=\"userType\" id='userType$u'
                                                   value = \"" . $glUserTypeArray[$u] . "\" " . Strings::isChecked($glUserTypeArray[$u], $userType) . " onclick=\"" . $showlink . "\" " . $eleAndDle . ">
                                            <span></span>
                                           $showName
                                        </label>";
                                echo '</div>';
                            }
                        } else {
                            if ($userType == 'PLO') {
                                $showName = 'DIY';
                            } else {
                                $showName = 'Branch';
                            }

                            ?>
                            <input type="hidden" name="userType" value="<?php echo $userType ?>">
                            <input type="text" value="<?php echo $showName ?>" class="form-control" disabled>
                            <?php
                        }
?>
                    </div>

                    <div class="col-md-6">
                        <label class="font-weight-bold">Avatar</label>
                        <?php if ($tblBranch->avatar) { ?>
                            <i class="fa fa-copy copy-link tooltipClass text-primary"
                               title="Click To Copy Avatar Link"
                               id="<?php echo $tblBranch->getURL(); ?>"></i>
                        <?php } ?>
                        <input type="FILE"
                               name="branchAvatar"
                               id="branchAvatar"
                               size="30"
                               autocomplete="off"
                               class="branchAvatar form-control">
                        <?php if ($tblBranch->avatar) { ?>
                            <div class="symbol symbol-100 mr-5 mt-2 ">
                                <div class="symbol-label"
                                     style="background-image:url('<?php echo $tblBranch->getURL(); ?>')"></div>
                                Merge Tag: <?php echo $tblBranch->getMergeTag(); ?>
                                <i class="fa fa-copy copy-link tooltipClass text-primary"
                                   title="Click To Copy Merge tag Path"
                                   id="<?php echo $tblBranch->getMergeTag(); ?>"></i>
                            </div>
                        <?php } ?>
                        <span class="form-text text-muted">(The file types allowed are JPEG,PNG,GIF.)</span>
                    </div>

                    <?php if ($executiveId == 0) { ?>
                        <div class="col-lg-6 mb-7">
                            <label class="font-weight-bold" for="pwd">Password</label>
                            <input type="password" name="pwd" id="pwd" value="" size="25" maxlength="15"
                                   class="mandatory form-control" autocomplete="off">
                        </div>
                        <div class="col-lg-6 mb-7">
                            <label class="font-weight-bold" for="confirmPwd">Confirm Password</label>
                            <input type="password" name="confirmPwd" id="confirmPwd" value="" size="25"
                                   maxlength="15" class="mandatory form-control" autocomplete="off">
                        </div>
                    <?php } ?>
                    <?php
                    //------------------------- 1126=abrams law, 854=mader, 820=daves, 792=resolution law group
                    $sponsortemp = '';
if (isset($_GET['sponsor'])) {
    $sponsortemp = $_GET['sponsor'];
}
if ($PCID == '1126' || $PCID == '854' || $PCID == '820' || $PCID == '792' || $sponsortemp == '1') {
    ?>
                        <div class="col-lg-6 mb-7">
                            <label class="font-weight-bold">Sponsor Name</label>
                            <input type="text" name="sponsorName" id="sponsorName " class="form-control"
                                   value="<?php echo $sponsorName ?>" size="40" maxlength="48"
                                   autocomplete="off">
                        </div>
                        <div class="col-lg-6 mb-7">
                            <label class="font-weight-bold">Sponsor's global account number</label>
                            <input type="text" name="globalAcctNo" id="globalAcctNo " class="form-control"
                                   value="<?php echo $globalAcctNo ?>" size="40" maxlength="48"
                                   autocomplete="off">
                        </div>
                    <?php } ?>
                    <input type="hidden" name="displayLinkToFICO" id="displayLinkToFICO"
                           value="<?php echo $displayLinkToFICO ?>">
                </div><!-- row close -->
            </div>
        </div>
        <?php if ($isPublicWebform == 0) { ?>
            <div class="card card-custom mb-2">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label"> Branch Configuration:
                        </h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="javascript:void(0);"
                           class="btn btn-light-primary btn-text-primary btn-hover-primary btn-icon"
                           data-card-tool="toggle">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body px-4 m-0 accordion" style="display: none">
                    <div class="row">
                        <?php if ($userType == 'PLO') { ?>
                            <div class="col-12">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-5"> When to ask for payment</label>
                                    <div class="col-lg-7 ">
                                        <div class="radio-list">
                                            <label for="askPaymentBeforeLMR"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="askPaymentBeforeLMR"
                                                       id="askPaymentBeforeLMR"
                                                       value="1" <?php echo Strings::isChecked($askPaymentBeforeLMR, '1') ?>>
                                                <span></span>
                                                BEFORE creating <b>"LOAN MODIFICATION FILE"
                                            </label>
                                            <label for="askPaymentBeforeLMR1"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="askPaymentBeforeLMR"
                                                       id="askPaymentBeforeLMR1"
                                                       value="0" <?php echo Strings::isChecked($askPaymentBeforeLMR, '0') ?>>
                                                <span></span>
                                                AFTER creating <b>"LOAN MODIFICATION FILE"
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-5">Would you like to ask your client "by whom they were
                                        referred"?</label>
                                    <div class="col-lg-7">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control" type="checkbox"
                                                       value="<?php echo $askReferralAgent ?>" id="tcDiv15"
                                                       <?php if ($askReferralAgent == 1) { ?> checked="checked" <?php } ?>
                                                            onchange="toggleSwitch('tcDiv15','askReferralAgent','1','0' );"/>
                                                <input type="hidden" name="askReferralAgent" id="askReferralAgent"
                                                       value="<?php echo $askReferralAgent ?>">

                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-5">Do you want the DIY client to be able to upload
                                        docs?</label>
                                    <div class="col-lg-7">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control" type="checkbox"
                                                           value="<?php echo $allowClientToUploadDocs ?>" id="tcDiv16"
                                                           <?php if ($allowClientToUploadDocs == 1) { ?> checked="checked" <?php } ?>
                                                                onchange="toggleSwitch('tcDiv16','allowClientToUploadDocs','1','0' );"/>
                                                    <input type="hidden" name="allowClientToUploadDocs"
                                                           id="allowClientToUploadDocs"
                                                           value="<?php echo $allowClientToUploadDocs ?>">

                                                    <span></span>
                                                </label>
                                            </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <?php if (($userRole == 'Super') || ($userRole == 'Manager')) { ?>
                            <div class="col-12">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-5"> Allow Loan Officer Users to See</label>
                                    <div class="col-lg-7 ">
                                        <div class="radio-list">
                                            <label for="allowAgentToSeeFile"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="allowAgentToSeeFile"
                                                       id="allowAgentToSeeFile"
                                                       value="1" <?php echo Strings::isChecked($allowAgentToSeeFile, '1') ?>>
                                                <span></span>
                                                Assigned Files Only
                                            </label>
                                            <label for="allowAgentToSeeFile1"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="allowAgentToSeeFile"
                                                       id="allowAgentToSeeFile1"
                                                       value="3" <?php echo Strings::isChecked($allowAgentToSeeFile, '3') ?>>
                                                <span></span>
                                                All files in my Branch
                                            </label>
                                            <label for="allowAgentToSeeFile2"
                                                   class="radio radio-solid font-weight-bolder">
                                                <input type="radio" name="allowAgentToSeeFile"
                                                       id="allowAgentToSeeFile2"
                                                       value="2" <?php echo Strings::isChecked($allowAgentToSeeFile, '2') ?>>
                                                <span></span>
                                                Files in Lead Status/or status set on Branch / Assigned Files
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-5"> Do you want to send File Designation information to
                                        your clients with public
                                        notes? <i
                                                class="tooltipAjax fas fa-info-circle text-primary "
                                                data-html="true"
                                                data-toggle="tooltip"
                                                title="If you choose no, when you send emails to your clients using the public notes feature they will not receive assigned employee or assigned agent contact details I.E. Name, Phone # , and Email Address."></i></label>
                                    <div class="col-lg-7">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToSendFileDesignation == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToSendFileDesignation ?>"
                                                               id="sendFileDesignation"
                                                               onchange="toggleSwitch('sendFileDesignation','allowToSendFileDesignation','1','0' );"/>
                                                        <input type="hidden" name="allowToSendFileDesignation"
                                                               id="allowToSendFileDesignation"
                                                               value="<?php echo $allowToSendFileDesignation ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <div class="col-12">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-5">Lock branch so it cannot be deleted? <i
                                            class="tooltipAjax fas fa-info-circle text-primary "
                                            data-html="true"
                                            data-toggle="tooltip"
                                            title="If you use the iframe codes for this branch in your website, then lock this branch which will NOT be allowed to delete because deleting it will break your website"></i></label>
                                <div class="col-lg-7">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($isPrimary == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $isPrimary ?>"
                                                               id="primarybranch"
                                                               onchange="toggleSwitch('primarybranch','isPrimary','1','0' );"/>
                                                        <input type="hidden" name="isPrimary" id="isPrimary"
                                                               value="<?php echo $isPrimary ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 d-none">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-5"> Use SMTP Server: <i
                                            class="tooltipAjax fas fa-info-circle text-primary "
                                            data-html="true"
                                            data-toggle="tooltip"
                                            title="You may use your own SMTP Email settings, which will deliver most email correspondence via your own email ID."></i></label>
                                <div class="col-lg-7 ">
                                    <div class="radio-list">
                                        <label for="useMyServerSetting"
                                               class="radio radio-solid font-weight-bolder">
                                            <input type="radio" name="useMyServerSetting"
                                                   id="useMyServerSetting"
                                                   onclick="showSMTPInfo('0','SMTPTable');"
                                                   value="0" <?php echo Strings::isChecked($useMyServerSetting, '0') ?>>
                                            <span></span>
                                            Use Company level settings
                                        </label>
                                        <label for="useMyServerSetting1"
                                               class="radio radio-solid font-weight-bolder">
                                            <input type="radio" name="useMyServerSetting"
                                                   id="useMyServerSetting1"
                                                   onclick="showSMTPInfo('1','SMTPTable');"
                                                   value="1" <?php echo Strings::isChecked($useMyServerSetting, '1') ?>>
                                            <span></span>
                                            Let me specify my own SMTP server details
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <table width="100%" id="SMTPTable" style="display:<?php echo $smtpDisp; ?>">
                                <tr>
                                    <td>
                                        SMTP Hostname:
                                    </td>
                                    <td colspan="2">
                                        <?php if ($userRole == 'Super') { ?>
                                            <input type="text" name="hostName" id="hostName"
                                                   value="<?php echo $hostName ?>" size="40">
                                        <?php } else {
                                            echo $hostName;
                                        } ?>
                                    </td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>
                                        SMTP Username:
                                    </td>
                                    <td colspan="2">
                                        <?php if ($userRole == 'Super') { ?>
                                            <input type="text" name="userName" id="userName"
                                                   value="<?php echo $SMTPUserName ?>" size="40"
                                                   autocomplete="off">
                                        <?php } else {
                                            echo $SMTPUserName;
                                        } ?>
                                    </td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>
                                        SMTP Password:
                                    </td>
                                    <td colspan="2">
                                        <?php if ($userRole == 'Super') { ?>
                                            <input type="password" name="SMTPPwd" id="SMTPPwd"
                                                   value="<?php echo $pwd ?>" autocomplete="off">
                                        <?php } elseif ($userRole == 'Manager') {
                                            echo $pwd;
                                        } else {
                                            echo 'XXXX';
                                        } ?>
                                    </td>
                                    <td>
                                        <?php if ($userRole == 'Super') {
                                        } else {
                                            echo '<h4>&nbsp;&nbsp;&nbsp;If You Want To Change Please Contact Admin</h4>';
                                        } ?>

                                    </td>

                                </tr>
                                <tr>
                                    <td>
                                        SMTP Port:
                                    </td>
                                    <td colspan="2">
                                        <?php if ($userRole == 'Super') { ?>
                                            <input type="text" name="portNo" id="portNo"
                                                   value="<?php echo $portNo ?>">
                                        <?php } else {
                                            echo $portNo;
                                        } ?>
                                    </td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>
                                        Reply To:
                                    </td>
                                    <td colspan="2">
                                        <?php if ($userRole == 'Super') { ?>
                                            <input type="text" name="replyTo" id="replyTo"
                                                   value="<?php echo $replyTo ?>" size="40">
                                        <?php } else {
                                            echo $replyTo;
                                        } ?>
                                    </td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>
                                        Bounce Mail:
                                    </td>
                                    <td colspan="2">
                                        <?php if ($userRole == 'Super') { ?>
                                            <input type="text" name="bounceMail" id="bounceMail"
                                                   value="<?php echo $bounceMail ?>" size="40">
                                        <?php } else {
                                            echo $bounceMail;
                                        } ?>
                                    </td>
                                    <td></td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-12">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-5">New Lead Notification Handling <i
                                            class="tooltipAjax fas fa-info-circle text-primary "
                                            data-html="true"
                                            data-toggle="tooltip"
                                            title="You can add more than one email by using a semi colon."></i></label>
                                <div class="col-lg-7">
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-5">Email <b>Bcc</b></label>
                                <div class="col-lg-7">
                                    <input type="text" name="LMRBccEmail" id="LMRBccEmail" class="form-control"
                                           value="<?php echo $LMRBccEmail ?>" maxlength="125"
                                           autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-5">Email <b>Cc</b></label>
                                <div class="col-lg-7">
                                    <input type="text" name="LMRCcEmail" id="LMRCcEmail" class="form-control"
                                           value="<?php echo $LMRCcEmail ?>" maxlength="125" autocomplete="off">
                                </div>
                            </div>
                        </div>


                        <div class="col-12">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-5">Document Upload Notification Handling <i
                                            class="tooltipAjax fas fa-info-circle text-primary "
                                            data-html="true"
                                            data-toggle="tooltip"
                                            title="You can add more than one email by using a semi colon."></i></label>
                                <div class="col-lg-7">
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-5">Email <b>Bcc</b></label>
                                <div class="col-lg-7">
                                    <input type="text" name="DocUploadBcc" id="DocUploadBcc" class="form-control"
                                           value="<?php echo $DocUploadBcc; ?>" maxlength="125"
                                           autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-5">Email <b>Cc</b></label>
                                <div class="col-lg-7">
                                    <input type="text" name="DocUploadCc" id="DocUploadCc" class="form-control"
                                           value="<?php echo $DocUploadCc; ?>" maxlength="125" autocomplete="off">
                                </div>
                            </div>
                        </div>


                        <div class="col-12">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-5">Please provide branch login URL</label>
                                <div class="col-lg-7">
                                    <input type="text" name="branchAELoginUrl" id="branchAELoginUrl"
                                           class="form-control"
                                           value="<?php echo $branchAELoginUrl ?>" maxlength="145"
                                           autocomplete="off">
                                </div>
                            </div>
                        </div>
                        <div class="col-12 <?php if (!$pcCaptcha) {
                            echo 'd-none';
                        } ?>">
                            <div class="form-group row align-items-center">
                                <label class="col-lg-5">Enable Captcha on web forms? <i
                                            class="tooltipAjax fas fa-info-circle text-primary "
                                            data-html="true"
                                            data-toggle="tooltip"
                                            title="If yes, Captcha will be enabled on the web forms."></i></label>
                                <div class="col-lg-7">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowcaptcha == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowcaptcha ?>"
                                                               id="allowToCaptcha"
                                                               onchange="toggleSwitch('allowToCaptcha','allowcaptcha','1','0' );"/>
                                                        <input type="hidden" name="allowcaptcha" id="allowcaptcha"
                                                               value="<?php echo $allowcaptcha ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                </div>
                            </div>
                        </div>

                        <?php if ($userRole == 'Super') { ?>
                            <div class="col-12">
                                <label class="h3 text-danger">Super Admin Permissions :</label>
                            </div>
                            <div class="col-12">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-5">Assigned To Processing Company</label>
                                    <div class="col-lg-7">
                                        <select name="assignedToProcessingCompany"
                                                id="assignedToProcessingCompany"
                                                class="mandatory form-control"
                                                onchange="createEmployee.checkAllowedToLogin(this.value);populatePCModulesAndServices(this.value);">
                                            <option value=""> - Select -</option>
                                            <?php
                                            for ($pc = 0; $pc < count($PCListArray); $pc++) {
                                                $tempPCID = 0;
                                                $processingCompany = '';
                                                $processingSta = 1;
                                                $tempPCID = trim($PCListArray[$pc]['PCID']);
                                                $processingCompany = trim($PCListArray[$pc]['processingCompanyName']);
                                                $processingSta = trim($PCListArray[$pc]['activeStatus']);
                                                ?>
                                                <option value="<?php echo $tempPCID ?>" <?php echo Arrays::isSelected($tempPCID, $processingCompanyId); ?> <?php if ($processingSta == 0) { ?> class="red" <?php } ?>><?php echo $processingCompany ?></option>
                                                <?php
                                            }
                            ?>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <label class="h4">Company Name Accepting Payment</label>
                            </div>
                            <div class="col-12">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-5">Company Name</label>
                                    <div class="col-lg-7">
                                        <input type="text" name="paymentToCompany" id="paymentToCompany"
                                               class="form-control"
                                               value="<?php echo $paymentToCompany ?>" maxlength="150"
                                               autocomplete="off">
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-5">Subscribed</label>
                                    <div class="col-lg-7">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($subscribedOption == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $subscribedOption ?>"
                                                               id="subscrib"
                                                               onchange="toggleSwitch('subscrib','subscribed','1','0' );"/>
                                                        <input type="hidden" name="subscribed" id="subscribed"
                                                               value="<?php echo $subscribedOption ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-12">
                                <div class="form-group row align-items-center">
                                    <label class="col-lg-5">Allow Branch to add on feature in the
                                        dashboard</label>
                                    <div class="col-lg-7">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowAddOn == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowAddOn ?>"
                                                               id="alwAddOn"
                                                               onchange="toggleSwitch('alwAddOn','allowAddOn','1','0' );"/>
                                                        <input type="hidden" name="allowAddOn" id="allowAddOn"
                                                               value="<?php echo $allowAddOn ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                    </div>
                                </div>
                            </div>
                            <?php if (in_array($processingCompanyId, $glRAMAccessPC)) { ?>
                                <div class="col-12">
                                    <div class="form-group row align-items-center">
                                        <label class="col-lg-5">Allowed to access RAM?</label>
                                        <div class="col-lg-7">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowBranchToAccessRAM == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowBranchToAccessRAM ?>"
                                                               id="BranchToAccessRAM"
                                                               onchange="toggleSwitch('BranchToAccessRAM','allowBranchToAccessRAM','1','0' );"/>
                                                        <input type="hidden" name="allowBranchToAccessRAM"
                                                               id="allowBranchToAccessRAM"
                                                               value="<?php echo $allowBranchToAccessRAM ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                        </div>
                                    </div>
                                </div>

                            <?php }
                            } else { ?>
                            <input type="hidden" name="subscribed" id="subscribed"
                                   value="<?php echo $subscribedOption ?>">
                            <input type="hidden" name="paymentToCompany" id="paymentToCompany"
                                   value="<?php echo $paymentToCompany ?>">
                            <input type="hidden" name="allowAddOn" id="allowAddOn"
                                   value="<?php echo $allowAddOn ?>">
                            <?php
                            } ?>

                    </div><!-- row end -->
                </div>
            </div>
            <div class="card card-custom mb-2">
                <div class="card-header card-header-tabs-line bg-gray-100">
                    <div class="card-title">
                        <h3 class="card-label">
                            User Permission Settings:
                        </h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="javascript:void(0);"
                           class="btn btn-light-primary btn-text-primary btn-hover-primary btn-icon"
                           data-card-tool="toggle">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body px-4 m-0 accordion" id="detailedUserPermission">
                    <div class="card card-custom example example-compact"
                         id="userEditingVisibilityPermissions" >
                        <div class="card-header">
                            <h3 class="card-title">Editing and Visibility Permissions</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1)) { ?>
                                    <div class="col-6" id="branchLoginDiv1">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-3 font-weight-bold">Allow to login?
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip"
                                                   title="This will enable or disable ability to login. Also, your billing is tied to # of users with login rights enabled."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <?php if ($PCAllowToCreate == 1 || $allowBranchToLogin == 1) { ?>
                                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control branchUserPermission"
                                                       type="checkbox" <?php if ($allowBranchToLogin == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowBranchToLogin ?>"
                                                       id="allowBranchToLog"
                                                       onchange="toggleSwitch('allowBranchToLog', 'allowBranchToLogin','1','0' );"/>
                                                <input type="hidden" name="allowBranchToLogin"
                                                       id="allowBranchToLogin"
                                                       value="<?php echo $allowBranchToLogin ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                                <?php } else { ?>
                                                    <span class="h5">No</span>
                                                    <span class="form-text text-muted">( Note: You cannot edit this field since you have exceeded the # of allow users )</span>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1)) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow Branch to edit File(s)?
                                                <i class="tooltipAjax fas fa-info-circle text-primary"
                                                   data-html="true" data-toggle="tooltip"
                                                   title="If Yes, the user will be able to edit all files, regardless of the file status-related permission. If NO, the user will NOT be able to edit any loan files in the system. If Yes for 'Follow Company Settings' the user will only be able to edit files based on the platform settings-> File Status-> User Type Permissions."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <div class="radio-inline">
                                                    <label for="allowLMRAEToEditFile"
                                                           class="radio radio-solid font-weight-bolder">
                                                        <input type="radio" name="allowLMRAEToEditFile"
                                                               id="allowLMRAEToEditFile"
                                                               value="1" <?php echo Strings::isChecked($allowLMRAEToEditFile, '1') ?>>
                                                        <span></span>
                                                        Yes
                                                    </label>
                                                    <label for="allowLMRAEToEditFile1"
                                                           class="radio radio-solid font-weight-bolder">
                                                        <input type="radio" name="allowLMRAEToEditFile"
                                                               id="allowLMRAEToEditFile1"
                                                               value="0" <?php echo Strings::isChecked($allowLMRAEToEditFile, '0') ?>>
                                                        <span></span>
                                                        No
                                                    </label>
                                                    <label for="allowLMRAEToEditFile2"
                                                           class="radio radio-solid font-weight-bolder">
                                                        <input type="radio" name="allowLMRAEToEditFile"
                                                               id="allowLMRAEToEditFile2"
                                                               value="2" <?php echo Strings::isChecked($allowLMRAEToEditFile, '2') ?>>
                                                        <span></span>
                                                        <a target="_blank"
                                                           href="/backoffice/createProcessingCompany.php?pcId=<?php echo cypher::myEncryption($PCID) ?>&tabNumb=6">follow
                                                            Company
                                                            Settings</a>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow Branch to create files?
                                                <i class="tooltipAjax fas fa-info-circle text-primary"
                                                   data-html="true" data-toggle="tooltip"
                                                   title="If NO, the user will not be able to create loan files in the system."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowBranchToCreateFiles == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowBranchToCreateFiles ?>"
                                                                   id="allowBrToCreateFiles"
                                                                   onchange="toggleSwitch('allowBrToCreateFiles','allowBranchToCreateFiles','1','0' );"/>
                                                            <input type="hidden" name="allowBranchToCreateFiles"
                                                                   id="allowBranchToCreateFiles"
                                                                   value="<?php echo $allowBranchToCreateFiles ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow Branch to change Status and Substatus if
                                                the File is Editable for Them?
                                                <i class="tooltipAjax fas fa-info-circle text-primary"
                                                   data-html="true" data-toggle="tooltip"
                                                   title="If NO...The branch manager user will only be able to edit details on a file if the file is still in the Pre-qualification stages. Once the file is in QC, Processing, or In Bank... it will be locked and that user will only be able to add comments/notes to the file."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowToUpdateFileAdminSection == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowToUpdateFileAdminSection ?>"
                                                                   id="allowToUpFileAdminSec"
                                                                   onchange="toggleSwitch('allowToUpFileAdminSec','allowToUpdateFileAdminSection','1','0' );"/>
                                                            <input type="hidden" name="allowToUpdateFileAdminSection"
                                                                   id="allowToUpdateFileAdminSection"
                                                                   value="<?php echo $allowToUpdateFileAdminSection ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <?php if (!in_array($PCID, $accessRestrictionPC)) { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allow to Excel Export File Data?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary"
                                                       data-html="true" data-toggle="tooltip"
                                                       title="If yes, the user can export excel reports from the pipeline."></i>
                                                </label>
                                                <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowedToExcelReport == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowedToExcelReport ?>"
                                                                   id="allowedToExcelRep"
                                                                   onchange="toggleSwitch('allowedToExcelRep','allowedToExcelReport','1','0' );"/>
                                                            <span></span>
                                                        </label>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <input type="hidden" name="allowedToExcelReport" id="allowedToExcelReport"
                                           value="<?php echo $allowedToExcelReport ?>">
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to create tasks? </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowBranchToCreateTasks == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowBranchToCreateTasks ?>"
                                                                   id="allowBrToCreateTasks"
                                                                   onchange="toggleSwitch('allowBrToCreateTasks','allowBranchToCreateTasks','1','0' );"/>
                                                            <input type="hidden" name="allowBranchToCreateTasks"
                                                                   id="allowBranchToCreateTasks"
                                                                   value="<?php echo $allowBranchToCreateTasks ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to Edit/Delete Notes Made by This
                                                User?</label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowUserToEditOwnNotes == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowUserToEditOwnNotes ?>"
                                                                   id="allowedToEdNotes"
                                                                   onchange="toggleSwitch('allowedToEdNotes','allowedToEditOwnNotes','1','0' );"/>
                                                            <input type="hidden" name="allowedToEditOwnNotes"
                                                                   id="allowedToEditOwnNotes"
                                                                   value="<?php echo $allowUserToEditOwnNotes ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <?php if (($userRole == 'Super') || ($userRole == 'Manager') || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1)) { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allow to see private notes?
                                                    <i class="tooltipAjax fas fa-info-circle text-primary"
                                                       data-html="true" data-toggle="tooltip"
                                                       title="If yes, the user will be able to see notes marked as private."></i>
                                                </label>
                                                <div class="col-lg-4">
                                                        <span class="switch switch-icon">
                                                            <label>
                                                                <input class="form-control"
                                                                       type="checkbox" <?php if ($seePrivate == 1) { ?> checked="checked" <?php } ?>
                                                                       value="<?php echo $seePrivate ?>" id="seePri"
                                                                       onchange="toggleSwitch('seePri','seePrivate','1','0' );"/>
                                                                <input type="hidden" name="seePrivate" id="seePrivate"
                                                                       value="<?php echo $seePrivate ?>">
                                                                <span></span>
                                                            </label>
                                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                        <input type="hidden" name="allowBranchToSeePublicNotes"
                                               id="allowBranchToSeePublicNotes" value="1">
                                    <?php } ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow Branch to control on / off Loan
                                                Officer/Broker login?</label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowLMRToOnOffAgentLogin == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowLMRToOnOffAgentLogin ?>"
                                                                   id="allowLMRToOnOffAgentLog"
                                                                   onchange="toggleSwitch('allowLMRToOnOffAgentLog','allowLMRToOnOffAgentLogin','1','0' );"/>
                                                            <input type="hidden" name="allowLMRToOnOffAgentLogin"
                                                                   id="allowLMRToOnOffAgentLogin"
                                                                   value="<?php echo $allowLMRToOnOffAgentLogin ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow Branch to add/edit Loan Officer/Mortgage
                                                Broker profile?</label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowLMRToEditAgentProfile == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowLMRToEditAgentProfile ?>"
                                                                   id="allowLMRToEditAgentPro"
                                                                   onchange="toggleSwitch('allowLMRToEditAgentPro','allowLMRToEditAgentProfile','1','0' );"/>
                                                            <input type="hidden" name="allowLMRToEditAgentProfile"
                                                                   id="allowLMRToEditAgentProfile"
                                                                   value="<?php echo $allowLMRToEditAgentProfile ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Would you like to allow this Branch to see
                                                commission? </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowBranchToSeeCommission == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowBranchToSeeCommission ?>"
                                                                   id="allowBranchToSeeComm"
                                                                   onchange="toggleSwitch('allowBranchToSeeComm','allowBranchToSeeCommission','1','0' );showEditSwitch('editBranchCommission','allowBranchToSeeCommission');;"/>
                                                            <input type="hidden" name="allowBranchToSeeCommission"
                                                                   id="allowBranchToSeeCommission"
                                                                   value="<?php echo $allowBranchToSeeCommission ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6 editBranchCommission"
                                         style="<?php echo $showEditBranchCommission; ?>">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Would you like to allow this Branch to edit
                                                commission? </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowLMRAEToEditCommission == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowLMRAEToEditCommission ?>"
                                                                   id="allowLMRAEToEditComm"
                                                                   onchange="toggleSwitch('allowLMRAEToEditComm','allowLMRAEToEditCommission','1','0' );"/>
                                                            <input type="hidden" name="allowLMRAEToEditCommission"
                                                                   id="allowLMRAEToEditCommission"
                                                                   value="<?php echo $allowLMRAEToEditCommission ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to add New Loan Officer/Broker in
                                                the long/short iframe <i
                                                        class="tooltipAjax fas fa-info-circle text-primary "
                                                        data-html="true"
                                                        data-toggle="tooltip"
                                                        title="If Yes, new Loan Officer/Broker/affiliates can register a profile directly from the iFrame/Web form."></i></label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowToAddAgent == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowToAddAgent ?>"
                                                                   id="allowToAddAgt"
                                                                   onchange="toggleSwitch('allowToAddAgt','allowToAddAgent','1','0' );"/>
                                                            <input type="hidden" name="allowToAddAgent"
                                                                   id="allowToAddAgent"
                                                                   value="<?php echo $allowToAddAgent ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } else {
                                    ?>
                                    <input type="hidden" name="allowAgentToSeeFile" id="allowAgentToSeeFile"
                                           value="<?php echo $allowAgentToSeeFile ?>">
                                    <input type="hidden" name="allowLMRAEToEditFile" id="allowLMRAEToEditFile"
                                           value="<?php echo $allowLMRAEToEditFile ?>">
                                    <input type="hidden" name="allowToUpdateFileAdminSection"
                                           id="allowToUpdateFileAdminSection"
                                           value="<?php echo $allowToUpdateFileAdminSection ?>">
                                    <input type="hidden" name="allowLMRAEToAccessDocs" id="allowLMRAEToAccessDocs"
                                           value="<?php echo $allowLMRAEToAccessDocs ?>">
                                    <input type="hidden" name="allowLMRAEToEditCommission"
                                           id="allowLMRAEToEditCommission"
                                           value="<?php echo $allowLMRAEToEditCommission ?>">
                                    <input type="hidden" name="allowLMRToOnOffAgentLogin"
                                           id="allowLMRToOnOffAgentLogin"
                                           value="<?php echo $allowLMRToOnOffAgentLogin ?>">
                                    <input type="hidden" name="allowLMRToEditAgentProfile"
                                           id="allowLMRToEditAgentProfile"
                                           value="<?php echo $allowLMRToEditAgentProfile ?>">
                                    <input type="hidden" name="allowToSendFax" id="allowToSendFax"
                                           value="<?php echo $allowToSendFax ?>">
                                    <input type="hidden" name="subscribeToHOME" id="subscribeToHOME"
                                           value="<?php echo $subscribeToHOME ?>">
                                    <input type="hidden" name="allowedToExcelReport" id="allowedToExcelReport"
                                           value="<?php echo $allowedToExcelReport ?>">
                                    <input type="hidden" name="allowToAddAgent" id="allowToAddAgent"
                                           value="<?php echo $allowToAddAgent ?>">
                                    <input type="hidden" name="allowBranchToCreateFiles"
                                           id="allowBranchToCreateFiles"
                                           value="<?php echo $allowBranchToCreateFiles ?>">
                                    <input type="hidden" name="allowBranchToCreateTasks"
                                           id="allowBranchToCreateTasks"
                                           value="<?php echo $allowBranchToCreateTasks ?>">
                                    <input type="hidden" name="allowBranchToSeeDashboard"
                                           id="allowBranchToSeeDashboard"
                                           value="<?php echo $allowBranchToSeeDashboard ?>">
                                    <input type="hidden" name="seePrivate" id="seePrivate"
                                           value="<?php echo $seePrivate ?>">
                                    <input type="hidden" name="allowedToDeleteUplodedDocs"
                                           id="allowedToDeleteUplodedDocs"
                                           value="<?php echo $allowUserToDeleteUploadedDocs ?>">
                                    <input type="hidden" name="allowedToEditOwnNotes" id="allowedToEditOwnNotes"
                                           value="<?php echo $allowUserToEditOwnNotes ?>">
                                    <?php
                                } ?>
                                <?php if (in_array('SS', $fileModules) || in_array('LM', $fileModules)) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow user to submit loan audit </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowToLASubmit == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowToLASubmit ?>"
                                                                   id="LASubmission"
                                                                   onchange="toggleSwitch('LASubmission','allowToLASubmit','1','0' );"/>
                                                              <input type="hidden" name="allowToLASubmit"
                                                                     id="allowToLASubmit"
                                                                     value="<?php echo $allowToLASubmit ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if ($processingCompanyId == 1399 || $processingCompanyId == 2 || $processingCompanyId == 488) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow Client to Access Document Library When
                                                Logged In? </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowClientToAccessDocs == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowClientToAccessDocs ?>"
                                                                   id="ClientToAccessDocs"
                                                                   onchange="toggleSwitch('ClientToAccessDocs','allowClientToAccessDocs','1','0' );"/>
                                                              <input type="hidden" name="allowClientToAccessDocs"
                                                                     id="allowClientToAccessDocs"
                                                                     value="<?php echo $allowClientToAccessDocs ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if ($userRole == 'Manager' || $userRole == 'Super') { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to edit interest rate / Cost of Capital /
                                                Yield spread? </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowEditToIR == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowEditToIR ?>"
                                                                   id="allowToEditToIR"
                                                                   onchange="toggleSwitch('allowToEditToIR','allowEditToIR','1','0' );"/>
                                                              <input type="hidden" name="allowEditToIR"
                                                                     id="allowEditToIR"
                                                                     value="<?php echo $allowEditToIR ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1)) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allowed to Edit Bank or Card Information
                                                <i class="tooltipAjax fas fa-info-circle text-primary"
                                                   data-html="true" data-toggle="tooltip"
                                                   title="If Yes, the user can edit the data in the bank and card info fields under the billing tab."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowtoEditCCInfo == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowtoEditCCInfo ?>"
                                                                   id="allowToSeeBillingSection"
                                                                   onchange="toggleSwitch('allowToSeeBillingSection','allowtoEditCCInfo','1','0' );"/>
                                                            <input type="hidden" name="allowtoEditCCInfo"
                                                                   id="allowtoEditCCInfo"
                                                                   value="<?php echo $allowtoEditCCInfo ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if ($userRole == 'Manager' || $userRole == 'Super') { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to Lock/unlock loan information? </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowToLockLoanFileBranch == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowToLockLoanFileBranch ?>"
                                                                   id="allowToLockLoanFileBranchId"
                                                                   onchange="toggleSwitch('allowToLockLoanFileBranchId','allowToLockLoanFileBranch','1','0' );"/>
                                                              <input type="hidden" name="allowToLockLoanFileBranch"
                                                                     id="allowToLockLoanFileBranch"
                                                                     value="<?php echo $allowToLockLoanFileBranch ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow access to Internal Loan Program? </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowToAccessInternalLoanProgram == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowToAccessInternalLoanProgram ?>"
                                                                   id="allowToAccessInternalLoanProgramId"
                                                                   onchange="toggleSwitch('allowToAccessInternalLoanProgramId','allowToAccessInternalLoanProgram','1','0' );"/>
                                                              <input type="hidden"
                                                                     name="allowToAccessInternalLoanProgram"
                                                                     id="allowToAccessInternalLoanProgram"
                                                                     value="<?php echo $allowToAccessInternalLoanProgram ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>

                                <div class="col-6">
                                    <div class="form-group row align-items-center">
                                        <label class="col-lg-8">Allow to Copy file?
                                            <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                               data-toggle="tooltip"
                                               title=" If turned on, users will be able to utilize the copy file feature from the pipeline."></i></label>
                                        <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowToCopyFile == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowToCopyFile ?>"
                                                                   id="allowToCopyFileId"
                                                                   onchange="toggleSwitch('allowToCopyFileId','allowToCopyFile','1','0' );"/>
                                                              <input type="hidden" name="allowToCopyFile"
                                                                     id="allowToCopyFile"
                                                                     value="<?php echo $allowToCopyFile ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card card-custom example example-compact">
                        <div class="card-header">
                            <h3 class="card-title">Documents and Notifications</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group row align-items-center">
                                        <label class="col-lg-8">Notify if Borrowers/Clients upload a document?
                                            <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                               data-toggle="tooltip"
                                               title="If Yes, this user will receive notifications from doc uploads (only if they are assigned to the file)."></i>
                                        </label>
                                        <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowBranchToGetBorrowerUploadDocsNotification == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowBranchToGetBorrowerUploadDocsNotification ?>"
                                                       id="allowBranchToGetBorrowerUploadDocs"
                                                       onchange="toggleSwitch('allowBranchToGetBorrowerUploadDocs','allowBranchToGetBorrowerUploadDocsNotification','1','0' );"/>
                                                  <input type="hidden"
                                                         name="allowBranchToGetBorrowerUploadDocsNotification"
                                                         id="allowBranchToGetBorrowerUploadDocsNotification"
                                                         value="<?php echo $allowBranchToGetBorrowerUploadDocsNotification ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                        </div>
                                    </div>
                                </div>
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1)) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to delete/replace uploaded docs,e-signed docs
                                                and binder docs
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If Yes, user can delete the uploaded docs which he has uploaded. If No, user can not delete any of the document"></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowUserToDeleteUploadedDocs == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowUserToDeleteUploadedDocs ?>"
                                                               id="allowedToDeleteUpDocs"
                                                               onchange="toggleSwitch('allowedToDeleteUpDocs','allowedToDeleteUplodedDocs','1','0' );"/>
                                                        <input type="hidden" name="allowedToDeleteUplodedDocs"
                                                               id="allowedToDeleteUplodedDocs"
                                                               value="<?php echo $allowUserToDeleteUploadedDocs ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if ($userRole == 'Manager' || $userRole == 'Super') { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allowed to share file?
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If yes, Share this file will be enabled on the form."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($shareThisFile == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $shareThisFile ?>" id="shareThisFile"
                                                               onchange="toggleSwitch('shareThisFile','sharethisFile','1','0' );"/>
                                                          <input type="hidden" name="shareThisFile" id="sharethisFile"
                                                                 value="<?php echo $shareThisFile ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1)) {
                                    if (in_array('SS', $fileModules) || in_array('LM', $fileModules) || in_array('MF', $fileModules)) { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allowed to send Client Portal, Login Access
                                                    Information
                                                    <i class="tooltipAjax fas fa-info-circle text-primary"
                                                       data-html="true" data-toggle="tooltip"
                                                       title="<?php if ($isPLO == 1 || $userGroup == 'Super') { ?>If you purchased the Private Label option, your client will have access to login and view their file in real time.<?php } else { ?>This is a private label option<?php } ?>"></i>
                                                </label>
                                                <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowToSendHomeownerLink == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowToSendHomeownerLink ?>"
                                                                   id="allowToSendHomeowner"
                                                                   onchange="toggleSwitch('allowToSendHomeowner','allowToSendHomeownerLink','1','0' );"/>
                                                            <input type="hidden" name="allowToSendHomeownerLink"
                                                                   id="allowToSendHomeownerLink"
                                                                   value="<?php echo $allowToSendHomeownerLink ?>">
                                                            <span></span>
                                                        </label>
                                                    <?php if ($isPLO == 1 || $userGroup == 'Super') { ?>
                                                        <i class="tooltipAjax fas fa-lock text-danger"
                                                           title="If you purchased the Private Label option, your client will have access to login and view their file in real time."></i>
                                                    <?php } else { ?>
                                                        <i class="tooltipAjax fas fa-lock text-danger"
                                                           title="This feature is available with Private Label package"></i>
                                                    <?php } ?>
                                                        </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php }
                                    }
            ?>
                                <?php if (($userRole == 'Manager' || $userRole == 'Super')) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allowed to access & administer docs?
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If yes, the user will be able to view all uploaded docs, rename them, change the status of them, and add required doc types when needed. If NO, they will only be able to see & upload required docs with user specific permissions."></i>
                                            </label>
                                            <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($allowLMRAEToAccessDocs == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $allowLMRAEToAccessDocs ?>"
                                                           id="allowLMRAEToAccDocs"
                                                           onchange="toggleSwitch('allowLMRAEToAccDocs','allowLMRAEToAccessDocs','1','0' );"/>
                                                    <input type="hidden" name="allowLMRAEToAccessDocs"
                                                           id="allowLMRAEToAccessDocs"
                                                           value="<?php echo $allowLMRAEToAccessDocs ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1)) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow users to send Fax?</label>
                                            <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToSendFax == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToSendFax ?>"
                                                               id="allowToUserSendFax"
                                                               onchange="toggleSwitch('allowToUserSendFax','allowToSendFax','1','0' );"/>
                                                        <input type="hidden" name="allowToSendFax" id="allowToSendFax"
                                                               value="<?php echo $allowToSendFax ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if (($userRole == 'Manager' || $userRole == 'Super')) { ?>
                                    <!-- New permissions added -->
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Notify if Back Office users upload a document?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip" title="Requires user is assigned to file"></i>
                                            </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control" type="checkbox"
                                                                   value="<?php echo $notifyBODocUpload; ?>"
                                                                   id="notifyBODocUploadCheckbox"
                                                                <?php if ($notifyBODocUpload == 1) { ?> checked="checked" <?php } ?>
                                                                onchange="toggleSwitch('notifyBODocUploadCheckbox', 'notifyBODocUpload','1','0' );"/>
                                                            <input type="hidden" name="notifyBODocUpload"
                                                                   id="notifyBODocUpload"
                                                                   value="<?php echo $notifyBODocUpload; ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Notify if Loan Officer users upload a document?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip" title="Requires user is assigned to file"></i>
                                            </label>
                                            <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control" type="checkbox"
                                                           value="<?php echo $notifyLODocUpload; ?>"
                                                           id="notifyLODocUploadCheckbox"
                                                        <?php if ($notifyLODocUpload == 1) { ?> checked="checked" <?php } ?>
                                                        onchange="toggleSwitch('notifyLODocUploadCheckbox', 'notifyLODocUpload','1','0' );"/>
                                                    <input type="hidden" name="notifyLODocUpload" id="notifyLODocUpload"
                                                           value="<?php echo $notifyLODocUpload; ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Notify if Broker users upload a document?
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip" title="Requires user is assigned to file"></i>
                                            </label>
                                            <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control" type="checkbox"
                                                           value="<?php echo $notifyBrokerDocUpload; ?>"
                                                           id="notifyBrokerDocUploadCheckbox"
                                                        <?php if ($notifyBrokerDocUpload == 1) { ?> checked="checked" <?php } ?>
                                                        onchange="toggleSwitch('notifyBrokerDocUploadCheckbox', 'notifyBrokerDocUpload','1','0' );"/>
                                                    <input type="hidden" name="notifyBrokerDocUpload"
                                                           id="notifyBrokerDocUpload"
                                                           value="<?php echo $notifyBrokerDocUpload; ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Notify if document upload request is sent to the
                                                borrower/client?
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip" title="Requires user is assigned to file"></i>
                                            </label>
                                            <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control" type="checkbox"
                                                           value="<?php echo $notifyDocUploadRequest; ?>"
                                                           id="notifyDocUploadRequestCheckbox"
                                                       <?php if ($notifyDocUploadRequest == 1) { ?> checked="checked" <?php } ?>
                                                       onchange="toggleSwitch('notifyDocUploadRequestCheckbox', 'notifyDocUploadRequest','1','0' );"/>
                                                    <input type="hidden" name="notifyDocUploadRequest"
                                                           id="notifyDocUploadRequest"
                                                           value="<?php echo $notifyDocUploadRequest; ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Notify if new file is created?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip"
                                                   title="Will notify this user if a new file is created in their associated branches"></i>
                                            </label>
                                            <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control" type="checkbox"
                                                           value="<?php echo $notifyNewFileCreated; ?>"
                                                           id="notifyNewFileCreatedCheckbox"
                                                        <?php if ($notifyNewFileCreated == 1) { ?> checked="checked" <?php } ?>
                                                        onchange="toggleSwitch('notifyNewFileCreatedCheckbox', 'notifyNewFileCreated','1','0' );"/>
                                                    <input type="hidden" name="notifyNewFileCreated"
                                                           id="notifyNewFileCreated"
                                                           value="<?php echo $notifyNewFileCreated; ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow users to send borrower e-signable document(s)
                                                to All Users?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip" title=""></i>
                                            </label>
                                            <div class="col-lg-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox"
                                                           value="<?php echo $allowUsersToSendEsignLinkToAll; ?>"
                                                           id="allowUsersToSendEsignLinkToAllCheckbox"
                                                        <?php if ($allowUsersToSendEsignLinkToAll == 1) { ?> checked="checked" <?php } ?>
                                                        onchange="toggleSwitch('allowUsersToSendEsignLinkToAllCheckbox', 'allowUsersToSendEsignLinkToAll','1','0' );"/>
                                                    <input type="hidden" name="allowUsersToSendEsignLinkToAll"
                                                           id="allowUsersToSendEsignLinkToAll"
                                                           value="<?php echo $allowUsersToSendEsignLinkToAll; ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- //New permissions added// -->
                                <?php } ?>
                            </div>
                        </div>
                    </div>
                    <div class="card card-custom example example-compact">
                        <div class="card-header">
                            <h3 class="card-title">Special Reports or Features</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <div class="form-group row align-items-center">
                                        <label class="col-lg-8">Enable Two-Factor Authentication (2FA)?
                                            <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                               data-toggle="tooltip"
                                               title="An additional layer of security for your account. A 2nd form of authentication can be sent by email or SMS message to provide you with a pin to enter on login. If you choose sms please be sure you have set your cell phone number on your profile"></i>
                                        </label>
                                        <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($enable2FAAuthentication == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $enable2FAAuthentication ?>"
                                                               id="allowAuthentication"
                                                               onchange="toggleSwitch('allowAuthentication', 'enable2FAAuthentication','1','0' );showEditSwitch('2FASelection','enable2FAAuthentication');"/>
                                                        <input type="hidden" name="enable2FAAuthentication"
                                                               id="enable2FAAuthentication"
                                                               value="<?php echo $enable2FAAuthentication ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                        </div>
                                    </div>
                                    <div class="form-group row align-items-center 2FASelection" <?php if ($enable2FAAuthentication == 0) {
                                        echo "style='display:none;'";
                                    } ?>>
                                        <div class="col-lg-12">
                                            <div class="radio-inline">
                                                <label for="TwoFATypeEmail"
                                                       class="radio radio-solid font-weight-bolder">
                                                    <input type="radio" name="TwoFAType" id="TwoFATypeEmail"
                                                           value="email" <?php if ($TwoFAType == 'email') {
                                                               echo 'checked';
                                                           } ?> >
                                                    <span></span>
                                                    Email
                                                </label>
                                                <label for="TwoFATypeSms" class="radio radio-solid font-weight-bolder">
                                                    <input type="radio" name="TwoFAType" id="TwoFATypeSms"
                                                           value="sms" <?php if ($TwoFAType == 'sms') {
                                                               echo 'checked';
                                                           } ?>>
                                                    <span></span>
                                                    SMS
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1)) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to see dashboard?
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If No, The entire dashboard page will be disabled. The user will go straight to their pipeline."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowBranchToSeeDashboard == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowBranchToSeeDashboard ?>"
                                                               id="allowBranchToSeeDash"
                                                               onchange="toggleSwitch('allowBranchToSeeDash','allowBranchToSeeDashboard','1','0' );"/>
                                                        <input type="hidden" name="allowBranchToSeeDashboard"
                                                               id="allowBranchToSeeDashboard"
                                                               value="<?php echo $allowBranchToSeeDashboard ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                            </div>
                                        </div>
                                    </div>

                                    <?php if (PageVariables::$isPCAllowEmailCampaign && ($userRole == 'Manager' || $userGroup == glUserGroup::USER_GROUP_SUPER)) { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allow to mass email?</label>
                                                <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowEmailCampaign == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowEmailCampaign ?>"
                                                                   id="allowedMassEmail"
                                                                   onchange="toggleSwitch('allowedMassEmail','allowEmailCampaign','1','0' );"/>
                                                            <input type="hidden" name="allowEmailCampaign"
                                                                   id="allowEmailCampaign"
                                                                   value="<?php echo $allowEmailCampaign ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>

                                <?php } ?>
                                <?php if ($userRole == 'Manager' || $userRole == 'Super') { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allowed to mass update
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip"
                                                   title="This will enable an additional column in the main pipeline. For Managers, they will be able to do functions which include, deactivating files, update status, Assign Employees and many others. For Non-Managers, Branch(s), and Agents (Loan Officer and Brokers), they will only be allowed to Deactivate files.."></i></label>
                                            <div class="col-lg-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowToMassUpdate == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowToMassUpdate ?>"
                                                       id="allowToMassUpdateCheckbox"
                                                       onchange="toggleSwitch('allowToMassUpdateCheckbox','allowToMassUpdate','1','0' );"/>
                                                <input type="text"
                                                       name="allowToMassUpdate"
                                                       id="allowToMassUpdate"
                                                       value="<?php echo $allowToMassUpdate; ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allowed to Submit Offers?
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If yes, Submit Offer will be enabled."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToSubmitOffer == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowToSubmitOffer ?>"
                                                               id="allowToSubmitOffer"
                                                               onchange="toggleSwitch('allowToSubmitOffer','allowtosubmitoffer','1','0' );"/>
                                                          <input type="hidden" name="allowToSubmitOffer"
                                                                 id="allowtosubmitoffer"
                                                                 value="<?php echo $allowToSubmitOffer ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to view workflow? </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowWorkflowEdit == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowWorkflowEdit ?>"
                                                                   id="allowToWorkflowEdit"
                                                                   onchange="toggleSwitch('allowToWorkflowEdit','allowWorkflowEdit','1','0' );"/>
                                                              <input type="hidden" name="allowWorkflowEdit"
                                                                     id="allowWorkflowEdit"
                                                                     value="<?php echo $allowWorkflowEdit ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') && ($PCInfoArray[$processingCompanyId]['allowToCreateAloware'] == 1)) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to access CallWise
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip"
                                                   title="CallWise is a 3rd party service that allows your team to make calls, and see & send text messages inside the loan file--> CallWise tab. Contact us to activate this service."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($AllowToCreateAloware == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $AllowToCreateAloware ?>"
                                                               onchange="toggleSwitch('empToCreateAloware','AllowToCreateAloware','1','0' );"/>
                                                        <input type="hidden" name="AllowToCreateAloware"
                                                               id="AllowToCreateAloware"
                                                               value="<?php echo $AllowToCreateAloware ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1)) {
                                    if ($userRole == 'Super' || $userRole == 'Manager' && in_array('LM', $fileModules) || in_array('SS', $fileModules)) { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Subscribe to HOME Report?</label>
                                                <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($subscribeToHOME == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $subscribeToHOME ?>"
                                                                   id="subscribeToHOMEForPC"
                                                                   onchange="toggleSwitch('subscribeToHOMEForPC','subscribeToHOME','1','0' );"/>
                                                            <input type="hidden" name="subscribeToHOME"
                                                                   id="subscribeToHOME"
                                                                   value="<?php echo $subscribeToHOME ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php }
                                    } ?>
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') && ($allowPCUsersToMarketPlace)) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Enable Marketplace Tab?
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If yes, user will be able to use the marketplace feature."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowToViewMarketPlace == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $allowToViewMarketPlace ?>"
                                                                   id="allowToViewMarketPlaceForm"
                                                                   onchange="toggleSwitch('allowToViewMarketPlaceForm','allowToViewMarketPlace','1','0' );"/>
                                                              <input type="hidden" name="allowToViewMarketPlace"
                                                                     id="allowToViewMarketPlace"
                                                                     value="<?php echo $allowToViewMarketPlace ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if (($userRole == 'Super') || ($userRole == 'Manager') || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1)) { ?>
                                    <?php if ($userType == 'PLO') { ?>
                                        <div class="col-6">
                                            <div class="form-group row align-items-center">
                                                <label class="col-lg-8">Allow to upgrade/downgrade DIY clients
                                                    sometimes? </label>
                                                <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($changeDIYPlan == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $changeDIYPlan ?>" id="tcDiv19"
                                                                   onchange="toggleSwitch('tcDiv19','changeDIYPlan','1','0' );"/>
                                                            <span></span>
                                                        </label>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    <?php } ?>
                                    <input type="hidden" name="changeDIYPlan" id="changeDIYPlan"
                                           value="<?php echo $changeDIYPlan ?>">
                                <?php } ?>
                                <?php if ($showManageDrawsToggle && ($userRole == 'Super' || $userRole == 'Manager')) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow to Manage Draws?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                data-toggle="tooltip"
                                                title="if Yes, user can edit borrower submitted draws."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                                type="checkbox" <?php if ($allowToManageDraws == 1) { ?> checked="checked" <?php } ?>
                                                                value="<?php echo $allowToManageDraws ?>"
                                                                id="manageDraws"
                                                                onchange="toggleSwitch('manageDraws','allowToManageDraws','1','0' );"/>
                                                        <input type="hidden" name="allowToManageDraws"
                                                                id="allowToManageDraws"
                                                                value="<?php echo $allowToManageDraws; ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if (($userRole == 'Manager' || $userRole == 'Super') && ($pcAcqualifyStatus > 0 && $pcAcqualifyId > 0)) { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Allow To View Credit Screen?
                                                <i class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                                   data-toggle="tooltip"
                                                   title="If yes, user will be able to View Credit Screen"></i>
                                            </label>
                                            <div class="col-lg-4">
                                                <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowToViewCreditScreening == 1) { ?> checked="checked" <?php } ?>
                                                        value="<?php echo $allowToViewCreditScreening ?>"
                                                               id="allowToViewCreditMenu"
                                                               onchange="toggleSwitch('allowToViewCreditMenu','allowToViewCreditScreening','1','0' );"/>
                                                        <input type="hidden" name="allowToViewCreditScreening"
                                                               id="allowToViewCreditScreening"
                                                               value="<?php echo $allowToViewCreditScreening ?>">
                                                        <span></span>
                                                    </label>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <?php if ($allowAutomation && in_array($userRole, ['Super', 'Manager'])) { //check for PC automation?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">
                                                Display any automations that are triggered by this user?
                                                <i class="tooltipAjax fas fa-info-circle text-primary " data-html="true"
                                                   data-toggle="tooltip" title=""
                                                   data-original-title="If yes, user will be able to see the automations that are triggered."></i>
                                            </label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($allowToViewAutomationPopup == 1) { ?> checked="checked" <?php } ?>
                                                                   id="allowToViewAutomationPopupCheckbox"
                                                                   onchange="toggleSwitch('allowToViewAutomationPopupCheckbox', 'allowToViewAutomationPopup','1','0' );"/>
                                                            <input type="hidden" name="allowToViewAutomationPopup"
                                                                   id="allowToViewAutomationPopup"
                                                                   value="<?php echo $allowToViewAutomationPopup; ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                <?php } ?>
                                <div class="col-6">
                                    <div class="form-group row align-items-center">
                                        <label class="col-lg-8">Allowed to Edit File Tracker?
                                            <i class="tooltipAjax fas fa-info-circle text-primary "
                                            data-html="true"
                                            data-toggle="tooltip"
                                            title="If yes, user will be able to edit the loan file stage."></i>
                                        </label>
                                        <div class="col-lg-4">
                                    <span class="switch switch-icon">
                                        <label>
                                            <input class="form-control"

                                                type="checkbox" <?php if ($allowToEditLoanStage == 1) { ?> checked="checked" <?php } ?>
                                                value="<?php echo $allowToEditLoanStage ?>"
                                                id="allowToEditLoanStage_switch"
                                                onchange="toggleSwitch('allowToEditLoanStage_switch', 'allowToEditLoanStage','1','0' );"/>
                                            <input type="hidden" name="allowToEditLoanStage"
                                                id="allowToEditLoanStage"
                                                value="<?php echo $allowToEditLoanStage ?>">
                                            <span></span>
                                        </label>
                                    </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-12 mb-2">
                                    <div class="separator separator-solid"></div>
                                </div>
                                <?php if ($userRole == 'Manager' || $userRole == 'Super') { ?>
                                    <div class="col-6">
                                        <div class="form-group row align-items-center">
                                            <label class="col-lg-8">Enable Pricing Engine?</label>
                                            <div class="col-lg-4">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control" <?php if ($pcPriceEngineStatus == 0) {
                                                                echo 'disabled';
                                                            } ?>
                                                                   type="checkbox" <?php if ($userPriceEngineStatus == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $userPriceEngineStatus ?>"
                                                                   id="userPriceEngineStatusCheckbox"
                                                                   onchange="toggleSwitch('userPriceEngineStatusCheckbox', 'userPriceEngineStatus','1','0' );
                                                                   showEditSwitch('priceEngineSwitch','userPriceEngineStatus');"/>
                                                            <input type="hidden" name="userPriceEngineStatus"
                                                                   id="userPriceEngineStatus"
                                                                   value="<?php echo $userPriceEngineStatus ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-6"></div>
                                <?php } ?>
                                <div class="col-6 priceEngineSwitch" style="<?php echo $showPriceEngineSwitch; ?>">
                                    <div class="form-group row">
                                        <label class="col-3">Login</label>
                                        <div class="col-6">
                                            <input type="text" name="loanpassLogin" id="loanpassLogin"
                                                   class="form-control" autocomplete="off"
                                                   value="<?php echo $loanpassLogin; ?>"
                                                <?php if ($pcPriceEngineStatus == 0) {
                                                    echo 'disabled';
                                                } ?>>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6 priceEngineSwitch" style="<?php echo $showPriceEngineSwitch; ?>">
                                    <div class="form-group row">
                                        <label class="col-3">Password</label>
                                        <div class="col-6">
                                            <input type="password" name="loanpassPassword" id="loanpassPassword"
                                                   class="form-control"
                                                   autocomplete="off" value="<?php echo $loanpassPassword; ?>"
                                                <?php if ($pcPriceEngineStatus == 0) {
                                                    echo 'disabled';
                                                } ?>>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php if (($userRole == 'Manager' || $userRole == 'Super') && (PageVariables::$glThirdPartyServices > 0 || PageVariables::$glThirdPartyServicesLegalDocs > 0)) { ?>
                <div class="card card-custom mb-2">
                    <div class="card-header card-header-tabs-line bg-gray-100">
                        <div class="card-title">
                            <h3 class="card-label">Integrations</h3>
                        </div>
                        <div class="card-toolbar">
                            <a href="javascript:void(0)"
                               class="btn btn-light-primary btn-text-primary btn-hover-primary btn-icon"
                               data-card-tool="toggle" data-section="">
                                <i class="ki icon-nm ki-arrow-down"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body accordion" style="display: none">
                        <div class="form-group row">
                            <?php if (PageVariables::$glThirdPartyServices > 0) { ?>
                                <div class="card card-custom col-md-12 mb-3">
                                    <div class="card-header card-header-tabs-line bg-gray-100">
                                        <div class="card-title">
                                            <h3 class="card-label">Credit Reporting Agencies:</h3>
                                        </div>
                                        <div class="card-toolbar" id="craHeader"
                                             onclick="Validation.controlChildElements('craHeader','craProducts','showHide')">
                                                    <span class="switch switch-icon">
                                                        <label>
                                                            <input class="form-control"
                                                                   type="checkbox" <?php if ($thirdPartyServices == 1) { ?> checked="checked" <?php } ?>
                                                                   value="<?php echo $thirdPartyServices ?>"
                                                                   id="thirdPartyLink"
                                                                   onchange="toggleSwitch('thirdPartyLink','thirdPartyServices','1','0' );showEditSwitch('thirdPartySwitch','thirdPartyServices');"/>
                                                            <input type="hidden" name="thirdPartyServices"
                                                                   id="thirdPartyServices"
                                                                   value="<?php echo $thirdPartyServices ?>">
                                                            <span></span>
                                                        </label>
                                                    </span>
                                            <div class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                                                 data-card-tool="toggle" data-toggle="tooltip" data-placement="top"
                                                 title="" data-original-title="Toggle Card">
                                                <i class="ki icon-nm ki-arrow-down"></i>
                                            </div>
                                        </div>
                                    </div>


                                    <div class="row pt-4" id="craProducts" style="display: none;">
                                        <?php foreach ($glThirdPartyServicesCRA as $cra => $craValue) {
                                            if (in_array($cra, $thirdPartyServiceCSRArray)) {
                                                $thirdPartyPassword = $thirdPartyServicesUserDetails['cra_' . $cra]['password'];
                                                ?>
                                                <div class="col-6 thirdPartySwitch pr-6"
                                                     style="<?php echo $showThirdPartyEditSwitch; ?>">
                                                    <div class="form-group row align-items-center">
                                                        <h6 class="col-lg-12"> <?= $craValue->Name; ?> </h6>
                                                        <label class="col-lg-2">User Name</label>
                                                        <div class="col-lg-10">
                                                            <input type="text"
                                                                   name="<?= 'thirdPartyServicesData[' . $cra . '][username]' ?>"
                                                                   id="thirdPartyUsername" class="form-control"
                                                                   value="<?= $thirdPartyServicesUserDetails['cra_' . $cra]['username']; ?>">
                                                        </div>
                                                        <label class="col-lg-2">Password</label>
                                                        <div class="col-lg-10">
                                                            <input type="text" autocomplete="off"
                                                                   name="<?= 'thirdPartyServicesData[' . $cra . '][password]' ?>"
                                                                   id="thirdPartyPassword" class="form-control"
                                                                   value="<?= $thirdPartyPassword ? '*****' . substr($thirdPartyPassword, -4) : ''; ?>">
                                                        </div>
                                                    </div>
                                                    <hr>
                                                </div>

                                            <?php }
                                            } ?>
                                    </div>
                                </div>
                            <?php }
                            if (PageVariables::$glThirdPartyServicesLegalDocs > 0) { ?>
                                <div class="card card-custom col-md-12 mb-3">
                                    <div class="card-header card-header-tabs-line bg-gray-100">
                                        <div class="card-title">
                                            <h3 class="card-label">Legal Docs:</h3>
                                        </div>
                                        <div class="card-toolbar" id="legalDocsHeader" onclick="Validation.controlChildElements('legalDocsHeader','legalDocsProducts','showHide')">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control" type="checkbox" <?php if ($thirdPartyServicesLegalDocs == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $thirdPartyServicesLegalDocs ?>" id="thirdPartyLinkLegalDocs"
                                                           onchange="toggleSwitch('thirdPartyLinkLegalDocs', 'thirdPartyServicesLegalDocs','1','0' );showEditSwitch('thirdPartySwitchLegalDocs','thirdPartyServicesLegalDocs');"/>
                                                    <input type="hidden" name="thirdPartyServicesLegalDocs"
                                                           id="thirdPartyServicesLegalDocs"
                                                           value="<?php echo $thirdPartyServicesLegalDocs ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                            <div class="tooltipClass btn btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass" data-card-tool="toggle"
                                                 data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card" style="visibility: hidden;">
                                                <i class="ki icon-nm ki-arrow-down"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            <?php } ?>
        <?php } ?>
        <?php
        $branchModuleInfoArray = [];

for ($i = 0; $i < count($modulesArray); $i++) {
    $branchModuleInfoArray[] = trim($modulesArray[$i]['moduleCode']);
}
?>
        <div class="card card-custom mb-2">
            <div class="card-header card-header-tabs-line bg-gray-100  ">
                <div class="card-title">
                    <h3 class="card-label">Loan Programs:</h3>
                </div>
                <div class="card-toolbar ">
                    <a href="javascript:void(0);"
                       class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass "
                       data-card-tool="toggle"
                       data-section="loanProgramSection"
                       data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                        <i class="ki ki-arrow-down icon-nm"></i>
                    </a>
                </div>
            </div>
            <div class="card-body px-4 m-0 accordion loanProgramSection_body" id="loanProgramSection">
                <div class="row mb-2">
                    <div class="col-lg-3 mb-0">
                        <label class="font-weight-bold">Select desired type of service modules <i
                                    class="tooltipAjax fas fa-info-circle text-primary" data-html="true"
                                    data-toggle="tooltip"
                                    title="Modules selected will show up in the branch web form, see below"></i></label>
                    </div>
                    <?php if ($executiveId > 0 || $userRole != 'Super') { ?>
                        <div class="col-lg-6">
                            <select data-placeholder=" - Select Module Type - " name="branchModuleType[]"
                                    id="branchModuleType"
                                    class="chzn-select form-control form-controller-solid"
                                    onchange="hideSelectAllLoanPrograms();" multiple="">
                                <?php
                        $PCModuleCnt = 0;
                        $PCModuleCnt = count($PCModulesArray);

                        $np = 0;
                        $npRes = 0;
                        $noOfTime = 2;
                        $np = ($PCModuleCnt) / $noOfTime;

                        $np = floor($np);
                        $npRes = ($PCModuleCnt) % $noOfTime;

                        if ($npRes > 0) {
                            $np = $np + 1;
                        }

                        $lc1 = 0;

                        if ($processingCompanyId > 0) {
                            $myTmpLMRClientTypeArray = $branchServicesArray;
                        } else {
                            $myTmpLMRClientTypeArray[]['LMRClientType'] = 'LM';
                            $myTmpLMRClientTypeArray[]['LMRClientType'] = 'SS';
                        }
                        if ($userRole == 'Super' && $executiveId == 0) {
                            $LMRClientTypeArray = array_keys($glLMRClientTypeArray);
                        }
                        for ($k = 0; $k < count($PCModulesArray); $k++) {

                            $sOpt = '';
                            $sOpt = Arrays::isSelectedArray($branchModuleInfoArray, trim($PCModulesArray[$k]['moduleCode']));
                            echo "<option value=\"" . trim($PCModulesArray[$k]['moduleCode']) . "\" " . $sOpt . '>' . trim($PCModulesArray[$k]['moduleName']) . '</option>';
                        }

                        ?>
                            </select>
                        </div>
                    <?php } ?>
                </div>
                <div class="row mb-2">
                    <div class="col-lg-12 mb-0" id="PCModuleAndServices">
                        <?php require __DIR__ . '/branchServices.php'; ?>
                    </div>
                </div>
            </div>
        </div>

        <div style="width:300px;">
            <div style="width:220px;" class="left">
                <?php
                /*
                * Allow Secondary WF feature for the PCs =
                * Dave PC, Enrollment Advisory, Law offices
                */
                if (($userRole == 'Manager' || $userRole == 'Administrator' || $userRole == 'Super') && (in_array($processingCompanyId, $accessSecondaryWFPC))) {
                    ?>
                    <h3>Assigned Workflows</h3>
                    <select data-placeholder=" - Select Workflow - " name="WFID[]" id="WFID" class="chzn-select odd"
                            multiple="" class="form-control">
                        <?php
                        for ($w = 0; $w < count($PCWorkflowArray); $w++) {
                            ?>
                            <option value="<?php echo trim($PCWorkflowArray[$w]['WFID']) ?>" <?php echo Arrays::isSelectedArray($assignedWFIDs, trim($PCWorkflowArray[$w]['WFID'])) ?>><?php echo trim($PCWorkflowArray[$w]['WFName']) ?></option>
                            <?php
                        }
                    ?>
                    </select>
                    <?php
                }
?>
            </div>
        </div>


        <?php
        if (($userRole == 'Super') || ($executiveId > 0) || ($PCAllowToCreate == 1) || ($userGroup == 'Employee' && $allowEmpToCreateBranch == 1) || ($userRole == 'Manager') || $isPublicWebform == 1) {
            ?>
            <div class="row d-flex justify-content-center">
                <button type="submit" name="save" id="save"
                        class="btn btn-primary font-weight-bold">Save
                </button>
                <?php if ($isPublicWebform == 0) { ?>
                    <button type="submit" name="saveLMR" id="saveLMR" value="Save & Next"
                            class="btn btn-primary font-weight-bold ml-2">Save
                        & Next
                    </button>
                <?php } ?>
            </div>
            <?php
        }
?>

    </form>

    <?php if ($private != 1 && $isPublicWebform == 1) { ?>
        <div class="col-md-12" style="padding-bottom:20px;">
            <div class="col-md-4"></div>
            <div class="col-md-4"></div>
            <div class="col-md-4 text-right">
        <span class="col-md-12"
              style="letter-spacing: 1px; font-size: 14px; font-weight: 600; color: #666; font-family: 'Lato', sans-serif;text-align: right">
             Fueled By:<br>
        </span>
                <a href="https://www.lendingwise.com/" target="_blank">
                    <img src="<?php echo CONST_SITE_URL; ?>assets/images/logonew.svg" alt="Fueled By LendingWise"
                         style="width: 180px;">
                </a>
            </div>
        </div>
    <?php } ?>


    <script>
        $("input[name='allowLMRAEToEditFile']").click(function () {
            var val = $("input[name=allowLMRAEToEditFile]:checked").val();
            if (val == 1 || val == 2) {
                $('#allowToupdateFileAndClientRow').css('display', 'table-row')
            } else {
                $('#allowToupdateFileAndClientRow').hide();
            }
        });
        var LWFormControls = function () {

            let _formSubmitValidation = function () {
                let formIdToSubmit = $('#LMRForm');
                //  validateLMRForm11();
                // validateLMRForm();
                jQuery.validator.addMethod(
                    "validateUploadFormNew",
                    function (value, element) {
                        console.log('validateUploadFormNew Called');
                        return validateLMRForm()


                    },
                    jQuery.validator.messages.validateUploadFormNew
                );
                formIdToSubmit.validate({
                    ignore: ".ignoreValidation",
                    rules: {
                        email: {
                            required: true,
                            email: true
                        },
                        emailConfirm: {
                            equalTo: "#email"
                        },
                        LMRName: "required",
                        LMRFirstName: "required",
                        company: "required",
                        pwd: {
                            required: true,
                            minlength: 6
                        },
                        confirmPwd: {
                            equalTo: "#pwd"
                        },
                        /*'validationField': {
                            validateUploadFormNew: true,
                        },*/
                        cellNumber: "required",
                        assignedToProcessingCompany: "required",
                        'branchModuleType[]': "required",

                    },
                    messages: {
                        email: {
                            required: "Please Enter Email address.",
                            email: "Please Enter Valid Email address."
                        },
                        emailConfirm: "Please Confirm Your Email address.",
                        LMRName: "Please Enter Branch Name",
                        LMRFirstName: "Please Enter First Name",
                        company: "Please Enter the Company",
                        pwd: {
                            required: "Enter Your Password.",
                            minlength: "Your Password Should be at least 6 Characters."
                        },
                        confirmPwd: "Password does not match.",
                        /*'validationField': {
                            validateUploadFormNew: "Error",
                        },*/
                        cellNumber: "Please enter cell number",
                        assignedToProcessingCompany: "Please assigned to processing company",
                        'branchModuleType[]': "Please Select the Modules",

                    },
                    errorElement: "em",
                    errorPlacement: function (error, element) {
                        // Add the `invalid-feedback` class to the error element
                        error.addClass("invalid-feedback");

                        if (element.prop("type") === "checkbox") {
                            error.insertAfter(element.next("label"));
                        } else {
                            if (element.prop("name") == 'role') {
                                element.next('.input-group-append').after(error);
                            } else {
                                error.insertAfter(element);
                            }
                        }
                    },
                    highlight: function (element, errorClass, validClass) {
                        $(element).addClass("is-invalid").removeClass("is-valid");
                        if (element.length > 0 && element.id == 'branchModuleType') {
                            //open the accordion
                            $('#loanProgramSection').css('display', 'block');
                        }
                    },
                    unhighlight: function (element, errorClass, validClass) {
                        $(element).addClass("is-valid").removeClass("is-invalid");
                    },
                    submitHandler: function (form) {

                        // https://stackoverflow.com/questions/6974684/how-to-send-formdata-objects-with-ajax-requests-in-jquery
                        let ajaxUrl = $(formIdToSubmit).attr('action');
                        let formData = new FormData($(form)[0]);

                        console.log({
                            ajaxUrl: ajaxUrl,
                        });

                        $.ajax({
                            url: ajaxUrl,
                            type: "POST",
                            data: formData,
                            processData: false,
                            contentType: false,
                            dataType: "json",
                            beforeSend: function () {
                                BlockDiv('employeeCreateDiv');
                            },
                            complete: function () {
                                UnBlockDiv('employeeCreateDiv');
                            },
                            success: function (res, status, xhr) {
                                //closeModal();
                                console.log({
                                    res: res,
                                });
                                if (parseInt(res.code) === 100) {
                                    toastrNotification(res.msg, 'success');
                                    setTimeout(function () {
                                        window.location.href = res.redirecturl;
                                    }, 3000);
                                } else {
                                    toastrNotification(res.msg, 'error');
                                }
                            },
                            error: function (jqXhr, textStatus, errorMessage) {
                                toastrNotification(errorMessage, 'error');
                            }
                        });
                    }
                });
            }
            return {
                // public functions
                init: function () {
                    _formSubmitValidation();
                }
            };
        }();

        $(document).ready(function () {
            LWFormControls.init();
        });


        function showEditSwitch(targetName, parent) {
            var pVal = $('#' + parent).val();
            if (pVal == 1) {
                $("." + targetName).css("display", "table-row");
            } else {
                $("." + targetName).css("display", "none");
            }
        }
    </script>

    <!-- LMRExecutiveForm.php -->
