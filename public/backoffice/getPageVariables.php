<?php
global $publicUser;

use models\constants\CFPBAuditingPC;
use models\constants\gl\glRAMAccessPC;
use models\cypher;
use models\PageVariables;
use models\standard\Strings;
use models\standard\UserAccess;

$glRAMAccessPC = glRAMAccessPC::$glRAMAccessPC;
$CFPBAuditingPC = CFPBAuditingPC::$CFPBAuditingPC;

$userGroup = null;
$userRole = null;
$userNumber = null;
$userFName = null;
$userLName = null;
$userLogo = null;
$PCTimeZone = null;
$userTimeZone = null;
$isPCActiveAloware = null;
$allowToViewCFPBPipeline = null;
$allowUserToAccessRAM = null;

if (isset($_SESSION['allow'])) {
    $allow = trim(Strings::GetSess('allow'));
}
if (isset($_SESSION['firstName'])) {
    $userFName = trim(Strings::GetSess('firstName'));
}
if (isset($_SESSION['lastName'])) {
    $userLName = trim(Strings::GetSess('lastName'));
}
if (isset($_SESSION['userNumber'])) {
    $userNumber = trim(Strings::GetSess('userNumber'));
}
if (isset($_SESSION['userEmail'])) {
    $userEmail = trim(Strings::GetSess('userEmail'));
}
if (isset($_SESSION['userGroup'])) {
    $userGroup = trim(Strings::GetSess('userGroup'));
}
if (isset($_SESSION['userRole'])) {
    $userRole = trim($_SESSION['userRole']);
}
if (isset($_SESSION['branchesName'])) {
    $branchesName = trim($_SESSION['branchesName']);
}
$PCID = null;
if (isset($_SESSION['PCID'])) {
    $PCID = trim($_SESSION['PCID']);
}
if (isset($_REQUEST['LID'])) {
    $LID = trim(trim(cypher::myDecryption($_REQUEST['LID'])));
}
if (isset($_SESSION['isPCActive'])) {
    $isUserActive = trim($_SESSION['isPCActive']);
}
if (isset($_SERVER['HTTPS'])) {
    $chkHttps = trim($_SERVER['HTTPS']);
}
if (isset($_SESSION['privateSite'])) {
    $privateSite = trim(Strings::GetSess('privateSite'));
}
if (isset($_SESSION['userLogo'])) {
    $userLogo = trim(Strings::GetSess('userLogo'));
}
if (isset($_SESSION['userPhone'])) {
    $userPhone = trim(Strings::GetSess('userPhone'));
}
if (isset($_SESSION['userCell'])) {
    $userCell = trim(Strings::GetSess('userCell'));
}
if (isset($_SESSION['private'])) {
    $private = trim(Strings::GetSess('private'));
}
if (isset($_SESSION['allowClientToCreateHMLOFile'])) {
    $allowClientToCreateHMLOFile = trim(Strings::GetSess('allowClientToCreateHMLOFile'));
}
$userName = trim($userFName . ' ' . $userLName);


if($userGroup && $userRole && $userNumber) {
    PageVariables::$PCID = $PCID; // set the existing PCID

    PageVariables::setUser(
        $userGroup,
        $userRole,
        $userNumber,
        $publicUser
    );

    // trigger the lazy load to get the User's PCID for superusers.
    $user = PageVariables::User();
    $PCID = PageVariables::$PCID ?: $PCID;
}

if (trim($userLogo)) {
    $logoExit = false;
    $logoExit = file_exists(CONST_PATH_PC_LOGO . $userLogo);
    if ($logoExit) {
        list($userLogoWidth, $userLogoHeight) = getimagesize(CONST_PATH_PC_LOGO . $userLogo);
        $userLogo = CONST_PC_LOGO_URL . $userLogo;
    } else {
        $userLogo = '';
    }
} else {
    $userLogo = '';
}

$restrict = Strings::GetSess("allowPageView['Employee']['accessRestricted']");

/*
* Check for direct access to files from external links. See if REQUEST has user type (ut). If it matches with logged in userGroup(from session) allow to enter. Else redirect to login page.
*/
$ut = '';
if (isset($_REQUEST['ut'])) {
    $ut = cypher::myDecryption(trim($_REQUEST['ut']));
}
if ($userRole == 'Auditor' || $userRole == 'CFPB Auditor' || $userRole == 'Auditor Manager') {
    $crtPage = basename(trim($_SERVER['PHP_SELF']));
}

if ($ut && $ut != $userGroup) {
    header('Location:' . UserAccess::getRedirect());
    exit();
}

/** Get user's permissions from the respective profile
 ** eg: Check whether logged-in user is allowed to edit file
 **/


$pcClient_id = '';
$allowPeerstreet = 0;
$shareThisFile = 0;
$viewSubmitOfferTab = 0;
$clientSelectRefferalCode = 0;
$allowAutomation = 0;
$allowServicing = 0;
$allowESignService = 0;
$isPCAllowEmailCampaign = 0;
$allowToSelectFromEmail = 0;
$allowToMassUpdate = 0;
$allowToManageDraws = 0;
$avatar = '';
$allowToSeeFeesInDashboard = 0;
$allowToEditLoanStage = 0;

if ($PCID > 0) {
    PageVariables::setPCID($PCID);

    if (PageVariables::PC()) {
        $isPLO = PageVariables::PC()->isPLO;
        $subscribeToREST = PageVariables::PC()->subscribeToREST;
        $isPCActive = PageVariables::PC()->activeStatus;
        $PCExpiryDate = PageVariables::PC()->PCExpiryDate;
        $PCOwed = PageVariables::PC()->owedFlag;
        $allowToEditLenderList = PageVariables::PC()->allowToEditLenderList;
        $processorAssignedCompany = PageVariables::PC()->processingCompanyName;
        $PCTimeZone = PageVariables::PC()->timeZone;
        $allowToLASubmitForPC = PageVariables::PC()->allowToLASubmitForPC;
        $allowToCFPBSubmitForPC = PageVariables::PC()->allowToCFPBSubmitForPC;
        $allowPCUserToSubmitCFPB = PageVariables::PC()->allowPCUserToSubmitCFPB;
        $subscribePCToHOME = PageVariables::PC()->subscribeToHOME;
        $theme = PageVariables::PC()->theme;
        $isSysNotesPrivate = PageVariables::PC()->isSysNotesPrivate;
        $showFSCntInDash = PageVariables::PC()->showFSCntInDash;
        $showStateMapInDash = PageVariables::PC()->showStateMapInDash;
        $showStartLoanNumber = PageVariables::PC()->showStartLoanNumber;

        $pcClient_id = PageVariables::PC()->client_id;
        $allowPeerstreet = PageVariables::PC()->allowPeerstreet;
        $isPCActiveAloware = PageVariables::PC()->allowToCreateAloware;
        $allowPCUsersToMarketPlace = PageVariables::PC()->allowPCToMarketPlace;
        $allowPCUsersToMarketPlacePublic = PageVariables::PC()->allowPCToMarketPlacePublic;
        $glThirdPartyServices = PageVariables::PC()->thirdPartyServices;
        $glThirdPartyServicesLegalDocs = PageVariables::PC()->thirdPartyServicesLegalDocs;
        $thirdPartyServiceCSR = PageVariables::PC()->thirdPartyServiceCSR;
        $thirdPartyServicesProducts = PageVariables::PC()->thirdPartyServicesProducts;
        // $shareThisFile = PageVariables::PC()->shareThisFile ?? ''; -- not part of processing company
        $loadMenu = PageVariables::PC()->loadMenu;
        $pcAcqualifyStatus = PageVariables::PC()->pcAcqualifyStatus;
        $allowAutomation = PageVariables::PC()->allowAutomation;
        $allowServicing = PageVariables::PC()->allowServicing;
        $pcPriceEngineStatus = PageVariables::PC()->pcPriceEngineStatus;
        $allowESignService = PageVariables::PC()->allowESignService;
        $isPCAllowEmailCampaign = PageVariables::PC()->allowEmailCampaign;
        $docWizard = PageVariables::PC()->docWizard;
        $showSysGenNote = PageVariables::PC()->showSysGenNote;
    }

    $exResultArray = PageVariables::PCServices();
    $moduleServiceArray = [];

    foreach ($exResultArray as $service) {
        $myServicesArray[] = $service->LMRClientType;
        if (!isset($moduleServiceArray[$service->moduleCode])) {
            $moduleServiceArray[$service->moduleCode] = [];
        }

        $moduleServiceArray[$service->moduleCode][] = $service->LMRClientType;

    }

    $exResultArray = PageVariables::PCModules();

    foreach ($exResultArray as $module) {
        $myModulesArray[] = $module->moduleCode;
    }
}

$allowToSeeBillingSectionForFile = 0;
if ($userGroup == 'Super' || $userGroup == 'REST') {

    $allowToSeeAlowareTab = PageVariables::User()->allowToSeeAlowareTab;
    $allowToEditMyFile = PageVariables::User()->allowToEditMyFile;
    $allowToUpdateFileAdminSection = PageVariables::User()->allowToUpdateFileAdminSection;
    $userSeeBilling = PageVariables::User()->userSeeBilling;
    $allowToAccessDocs = PageVariables::User()->allowToAccessDocs;
    $allowToDeleteUploadedDocs = PageVariables::User()->allowToDeleteUploadedDocs;
    $allowToCreateFiles = PageVariables::User()->allowToCreateFiles;
    $allowToCreateTasks = PageVariables::User()->allowToCreateTasks;
    $permissionToREST = PageVariables::User()->permissionToREST;
    $allowExcelDownload = PageVariables::User()->allowExcelDownload;
    $viewPrivateNotes = PageVariables::User()->viewPrivateNotes;
    $viewPublicNotes = PageVariables::User()->viewPublicNotes;
    $allowToEditOwnNotes = PageVariables::User()->allowToEditOwnNotes;
    $changeDIYPlan = PageVariables::User()->changeDIYPlan;
    $allowToEditCommission = PageVariables::User()->allowToEditCommission;
    $allowToSeeCommission = PageVariables::User()->allowToSeeCommission;
    $PCAllowToCreateBranch = PageVariables::User()->PCAllowToCreateBranch;
    $allowToViewAllFiles = PageVariables::User()->allowToViewAllFiles;
    $allowEmailCampaign = PageVariables::User()->allowEmailCampaign;
    $allowedToSendHomeownerLink = PageVariables::User()->allowedToSendHomeownerLink;
    $allowToLASubmitForPC = PageVariables::User()->allowToLASubmitForPC;
    $allowToLASubmission = PageVariables::User()->allowToLASubmission;
    $subscribeToHOME = PageVariables::User()->subscribeToHOME;
    $subscribePCToHOME = PageVariables::User()->subscribePCToHOME;
    $allowToCFPBSubmitForPC = PageVariables::User()->allowToCFPBSubmitForPC;
    $allowToCFPBSubmission = PageVariables::User()->allowToCFPBSubmission;
    $allowPCUserToSubmitCFPB = PageVariables::User()->allowPCUserToSubmitCFPB;
    $allowToViewCFPBPipeline = PageVariables::User()->allowToViewCFPBPipeline;
    $allowToAccessAdminReqDocs = PageVariables::User()->allowToAccessAdminReqDocs;
    $allowToViewCreditScreening = PageVariables::User()->allowToViewCreditScreening;
    $isPCAllowEmailCampaign = PageVariables::User()->isPCAllowEmailCampaign;
    $allowToSelectFromEmail = PageVariables::User()->allowToSelectFromEmail;
    $allowToEditLoanStage = PageVariables::User()->allowToEditLoanStage;

    if ($userGroup == 'Super') {
        $allowToSendFax = PageVariables::User()->allowToSendFax;
        $allowToAccessRAM = PageVariables::User()->allowToAccessRAM;
        $thirdPartyServices = PageVariables::User()->thirdPartyServices;
        $glThirdPartyServices = PageVariables::User()->glThirdPartyServices;
        $allowPeerstreet = PageVariables::User()->allowPeerstreet;
        $viewSubmitOfferTab = PageVariables::User()->viewSubmitOfferTab;
    }

    $allowDashboard = PageVariables::User()->allowDashboard;
} else if ($userGroup == 'Sales') {
    $allowDashboard = PageVariables::User()->allowDashboard;
} else if ($userGroup == 'Client') {
    $userTimeZone = PageVariables::User()->userTimeZone;
    $allowToLockLoanFile = PageVariables::User()->allowToLockLoanFile;
    $allowToViewMarketPlace = PageVariables::User()->allowToViewMarketPlace;
    $viewSubmitOfferTab = PageVariables::User()->viewSubmitOfferTab;
    $clientSelectRefferalCode = PageVariables::User()->clientSelectRefferalCode;

} elseif ($userGroup == 'Agent' && $userNumber > 0) {

    $allowToSeeAlowareTab = PageVariables::User()->allowToSeeAlowareTab;
    $allowToEditMyFile = PageVariables::User()->allowToEditMyFile;
    $allowToUpdateFileAdminSection = PageVariables::User()->allowToUpdateFileAdminSection;
    $userSeeBilling = PageVariables::User()->userSeeBilling;
    $allowToDeleteUploadedDocs = PageVariables::User()->allowToDeleteUploadedDocs;
    $allowDashboard = PageVariables::User()->allowDashboard;
    $allowToCreateFiles = PageVariables::User()->allowToCreateFiles;
    $allowToCreateTasks = PageVariables::User()->allowToCreateTasks;
    $changeDIYPlan = PageVariables::User()->changeDIYPlan;
    $allowToAccessDocs = PageVariables::User()->allowToAccessDocs;
    $allowedToSendHomeownerLink = PageVariables::User()->allowedToSendHomeownerLink;
    $viewPrivateNotes = PageVariables::User()->viewPrivateNotes;
    $allowToEditOwnNotes = PageVariables::User()->allowToEditOwnNotes;
    $permissionToREST = PageVariables::User()->permissionToREST;
    $allowExcelDownload = PageVariables::User()->allowExcelDownload;
    $userTimeZone = PageVariables::User()->userTimeZone;
    $allowToLASubmission = PageVariables::User()->allowToLASubmission;
    $subscribeToHOME = PageVariables::User()->subscribeToHOME;
    $allowEmailCampaign = PageVariables::User()->allowEmailCampaign;
    $allowToSendFax = PageVariables::User()->allowToSendFax;

    $allowToEditCommission = PageVariables::User()->allowToEditCommission;
    $allowToSeeCommission = PageVariables::User()->allowToSeeCommission;
    $allowedToSendFileDesignation = PageVariables::User()->allowedToSendFileDesignation;
    $viewPublicNotes = PageVariables::User()->viewPublicNotes;

    $allowToCFPBSubmission = PageVariables::User()->allowToCFPBSubmission;
    $allowToViewCFPBPipeline = PageVariables::User()->allowToViewCFPBPipeline;
    $allowUserToAccessRAM = PageVariables::User()->allowToAccessRAM;
    $allowAgentWorkflowEdit = PageVariables::User()->allowAgentWorkflowEdit;
    $allowToLockLoanFile = PageVariables::User()->allowToLockLoanFile;
    $allowToViewMarketPlace = PageVariables::User()->allowToViewMarketPlace;
    $allowToupdateFileAndClient = PageVariables::User()->allowToupdateFileAndClient;
    $thirdPartyServices = PageVariables::User()->thirdPartyServices;
    $shareThisFile = PageVariables::User()->shareThisFile;
    $allowToViewCreditScreening = PageVariables::User()->allowToViewCreditScreening;
    $acqualifyOptStatus = PageVariables::User()->acqualifyOptStatus;
    $avatar = PageVariables::User()->avatar;

    $externalBroker = PageVariables::User()->externalBroker; //Loan officer Flag
    if ($externalBroker == 1) {
        $secondaryBrokerNumber = $userNumber;// assigning Loan officer value as $secondary Broker number
    }

    $viewSubmitOfferTab = PageVariables::User()->viewSubmitOfferTab;
    $userPriceEngineStatus = PageVariables::User()->userPriceEngineStatus;
    $allowToAccessInternalLoanProgram = PageVariables::User()->allowToAccessInternalLoanProgram;
    $allowToCopyFile = PageVariables::User()->allowToCopyFile;
    $allowToSeeAllBrokers = PageVariables::User()->allowToSeeAllBrokers;
    $allowToMassUpdate = (int)PageVariables::User()->allowToMassUpdate;
    $allowToManageDraws = (int)PageVariables::User()->allowToManageDraws;
    $allowToViewContactsList = (int)PageVariables::User()->allowToViewContactsList;
    $allowToEditLoanStage = PageVariables::User()->allowToEditLoanStage;

    $agentBranchArray = PageVariables::User()->agentBranchArray;
} elseif ($userGroup == 'Branch' && $userNumber > 0) {
    $allowToSeeAlowareTab = PageVariables::User()->allowToSeeAlowareTab;
    $allowToEditMyFile = PageVariables::User()->allowToEditMyFile;
    $allowToUpdateFileAdminSection = PageVariables::User()->allowToUpdateFileAdminSection;
    $allowExcelDownload = PageVariables::User()->allowExcelDownload;
    $permissionToREST = PageVariables::User()->permissionToREST;
    $viewPrivateNotes = PageVariables::User()->viewPrivateNotes;
    $allowToEditOwnNotes = PageVariables::User()->allowToEditOwnNotes;
    $allowToEditCommission = PageVariables::User()->allowToEditCommission;
    $allowToSeeCommission = PageVariables::User()->allowToSeeCommission;
    $allowToAccessDocs = PageVariables::User()->allowToAccessDocs;
    $allowToDeleteUploadedDocs = PageVariables::User()->allowToDeleteUploadedDocs;
    $allowDashboard = PageVariables::User()->allowDashboard;
    $allowToCreateFiles = PageVariables::User()->allowToCreateFiles;
    $allowToCreateTasks = PageVariables::User()->allowToCreateTasks;
    $changeDIYPlan = PageVariables::User()->changeDIYPlan;
    $allowToOnOffAgentLogin = PageVariables::User()->allowToOnOffAgentLogin;
    $allowBranchToEditAgentProfile = PageVariables::User()->allowBranchToEditAgentProfile;
    $allowedToSendHomeownerLink = PageVariables::User()->allowedToSendHomeownerLink;
    $AEUserType = PageVariables::User()->AEUserType;
    $userTimeZone = PageVariables::User()->userTimeZone;
    $allowToLASubmission = PageVariables::User()->allowToLASubmission;
    $subscribeToHOME = PageVariables::User()->subscribeToHOME;
    $allowEmailCampaign = PageVariables::User()->allowEmailCampaign;
    $allowToSendFax = PageVariables::User()->allowToSendFax;
    $allowedToSendFileDesignation = PageVariables::User()->allowedToSendFileDesignation;
    $viewPublicNotes = PageVariables::User()->viewPublicNotes;
    $allowToCFPBSubmission = PageVariables::User()->allowToCFPBSubmission;

    $allowToViewCFPBPipeline = PageVariables::User()->allowToViewCFPBPipeline;
    $allowUserToAccessRAM = PageVariables::User()->allowToAccessRAM;
    $allowBranchManagerToLogin = PageVariables::User()->allowBranchManagerToLogin;
    $allowBranchWorkflowEdit = PageVariables::User()->allowBranchWorkflowEdit;
    $allowToLockLoanFile = PageVariables::User()->allowToLockLoanFile;
    $allowBranchToAddAgent = PageVariables::User()->allowBranchToAddAgent;
    $allowToViewMarketPlace = PageVariables::User()->allowToViewMarketPlace;
    $allowToupdateFileAndClient = PageVariables::User()->allowToupdateFileAndClient;
    $thirdPartyServices = PageVariables::User()->thirdPartyServices;
    $shareThisFile = PageVariables::User()->shareThisFile;
    $viewSubmitOfferTab = PageVariables::User()->viewSubmitOfferTab;
    $allowToViewCreditScreening = PageVariables::User()->allowToViewCreditScreening;
    $acqualifyOptStatus = PageVariables::User()->acqualifyOptStatus;
    $userPriceEngineStatus = PageVariables::User()->userPriceEngineStatus;
    $allowToAccessInternalLoanProgram = PageVariables::User()->allowToAccessInternalLoanProgram;
    $allowToCopyFile = PageVariables::User()->allowToCopyFile;
    $allowToMassUpdate = (int)PageVariables::User()->allowToMassUpdate;
    $allowToManageDraws = (int)PageVariables::User()->allowToManageDraws;
    $avatar = PageVariables::User()->avatar;
    $allowToEditLoanStage = PageVariables::User()->allowToEditLoanStage;

    // $allowEmailCampaign = 0;
    $userSeeBilling = 1;
    $myServicesArray = PageVariables::User()->myServicesArray;
    $myModulesArray = PageVariables::User()->myModulesArray;

} elseif (($userGroup == 'Employee' || $userGroup == 'Auditor' || $userGroup == 'CFPB Auditor' || $userGroup == 'Auditor Manager') && $userNumber > 0) {

    $allowToSeeAlowareTab = PageVariables::User()->allowToSeeAlowareTab;
    $allowToEditMyFile = PageVariables::User()->allowToEditMyFile;
    $allowExcelDownload = PageVariables::User()->allowExcelDownload;
    $allowToViewAllFiles = PageVariables::User()->allowToViewAllFiles;
    $allowToEditOwnNotes = PageVariables::User()->allowToEditOwnNotes;
    $userSeeBilling = PageVariables::User()->userSeeBilling;
    $allowEmailCampaign = PageVariables::User()->allowEmailCampaign;
    $allowToDeleteUploadedDocs = PageVariables::User()->allowToDeleteUploadedDocs;
    $allowDashboard = PageVariables::User()->allowDashboard;
    $allowToCreateFiles = PageVariables::User()->allowToCreateFiles;
    $allowToCreateTasks = PageVariables::User()->allowToCreateTasks;
    $permissionToREST = PageVariables::User()->permissionToREST;
    $viewPrivateNotes = PageVariables::User()->viewPrivateNotes;
    $changeDIYPlan = PageVariables::User()->changeDIYPlan;
    $allowedToSendHomeownerLink = PageVariables::User()->allowedToSendHomeownerLink;
    $allowToUpdateFileAdminSection = PageVariables::User()->allowToUpdateFileAdminSection;
    $userTimeZone = PageVariables::User()->userTimeZone;
    $allowToLASubmission = PageVariables::User()->allowToLASubmission;
    $subscribeToHOME = PageVariables::User()->subscribeToHOME;
    $allowToSendFax = PageVariables::User()->allowToSendFax;

    $allowToEditCommission = PageVariables::User()->allowToEditCommission;
    $allowToSeeCommission = PageVariables::User()->allowToSeeCommission;
    $allowedToSendFileDesignation = PageVariables::User()->allowedToSendFileDesignation;
    $viewPublicNotes = PageVariables::User()->viewPublicNotes;

    $allowToChangeOrAssignBranchForFile = PageVariables::User()->allowToChangeOrAssignBranchForFile;
    $allowToCFPBSubmission = PageVariables::User()->allowToCFPBSubmission;
    $allowToViewCFPBPipeline = PageVariables::User()->allowToViewCFPBPipeline;
    $allowUserToAccessRAM = PageVariables::User()->allowToAccessRAM;
    $allowEmpToCreateBranch = PageVariables::User()->allowEmpToCreateBranch;
    $convertNewBRIntoEmpOwnBR = PageVariables::User()->convertNewBRIntoEmpOwnBR;
    $allowToSeeBillingSectionForFile = PageVariables::User()->allowToSeeBillingSectionForFile;
    $allowEmpToCreateAgent = PageVariables::User()->allowEmpToCreateAgent;
    $allowEmpToSeeAgent = PageVariables::User()->allowEmpToSeeAgent;
    $allowToLockLoanFile = PageVariables::User()->allowToLockLoanFile;
    $allowToViewMarketPlace = PageVariables::User()->allowToViewMarketPlace;
    $allowToupdateFileAndClient = PageVariables::User()->allowToupdateFileAndClient ?? '';
    $thirdPartyServices = PageVariables::User()->thirdPartyServices;
    $shareThisFile = PageVariables::User()->shareThisFile;
    $allowToViewContactsList = PageVariables::User()->allowToViewContactsList;
    $viewSubmitOfferTab = PageVariables::User()->viewSubmitOfferTab;
    $allowToViewCreditScreening = PageVariables::User()->allowToViewCreditScreening;
    $acqualifyOptStatus = PageVariables::User()->acqualifyOptStatus;
    $userPriceEngineStatus = PageVariables::User()->userPriceEngineStatus;
    $allowUserToSendMsgToBorrower = PageVariables::User()->allowUserToSendMsgToBorrower;
    $allowEmpToCopyFile = PageVariables::User()->allowEmpToCopyFile;
    $allowToSelectFromEmail = PageVariables::User()->allowToSelectFromEmail;

    $allowToMassUpdate = (int)PageVariables::User()->allowToMassUpdate;
    $allowToManageDraws = (int)PageVariables::User()->allowToManageDraws;
    $avatar = PageVariables::User()->avatar;
    $allowToSeeFeesInDashboard = PageVariables::User()->allowToSeeFeesInDashboard;
    $allowToEditLoanStage = PageVariables::User()->allowToEditLoanStage;

    if ($userRole == 'Manager') {
        $allowToEditOwnNotes = PageVariables::User()->allowToEditOwnNotes;
        $allowToAccessDocs = PageVariables::User()->allowToAccessDocs;
        $allowToDeleteUploadedDocs = PageVariables::User()->allowToDeleteUploadedDocs;
    } else {
        $allowToAccessDocs = PageVariables::User()->allowToAccessDocs;
    }

} elseif ($publicUser == 1) {
    $allowToEditMyFile = PageVariables::User()->allowToEditMyFile;
    /** To Edit file for the users who are tried to submit from Iframe version **/
}

if (trim($PCTimeZone) == '') {
    $PCTimeZone = CONST_SERVER_TIME_ZONE;
}
if (trim($userTimeZone) == '') {
    $userTimeZone = CONST_SERVER_TIME_ZONE;
}
if ($userGroup == 'Auditor') {
    $allowToLASubmission = PageVariables::User()->allowToLASubmission;
    $allowToLASubmitForPC = PageVariables::User()->allowToLASubmitForPC;
}
if ($userGroup == 'CFPB Auditor' || $userGroup == 'Auditor Manager') {
    $allowToCFPBSubmission = PageVariables::User()->allowToCFPBSubmission;
    $allowToCFPBSubmitForPC = PageVariables::User()->allowToCFPBSubmitForPC;
    $allowToViewCFPBPipeline = PageVariables::User()->allowToViewCFPBPipeline;
    $allowPCUserToSubmitCFPB = PageVariables::User()->allowPCUserToSubmitCFPB;
    $allowToCreateFiles = PageVariables::User()->allowToCreateFiles;
}
if ((CONST_ENVIRONMENT == 'staging' && trim($userNumber) == '6431')
    || (CONST_ENVIRONMENT == 'production' && trim($userNumber) == '6569')) {  /* Show 'Cancelled' status for Loan Auditor 2 ONLY  */
    $loanAuditStatusArray[] = 'Cancelled';
}

/* TURN OFF excel download feature for NVA all <NAME_EMAIL>, <EMAIL> employee, <EMAIL>*/

$allowExcelDownload = PageVariables::User()->allowExcelDownload;

// Aloware pc profile.
if (!PageVariables::PC()->allowToCreateAloware > 0) {
    $allowToSeeAlowareTab = 0;
}

/*
 * Allow law office admin PC's employee as an auditing company
 * Aug 14, 2015
 *
 */
if (in_array($PCID, $CFPBAuditingPC) && $allowToViewCFPBPipeline == 1) {
    $allowCFPBAuditing = 1;
}
if (in_array($PCID, $glRAMAccessPC) && $allowUserToAccessRAM == 1) {
    /** Show RAM tab ONLY if the PC is allowed to access RAM **/
    $allowToAccessRAM = 1;
}
/**
 * New relic custom code added on April 30, 2019
 */
if (extension_loaded('newrelic')) { // Ensure PHP agent is available
    # Record custom data about this web transaction
    newrelic_add_custom_parameter('IP', $_SERVER['REMOTE_ADDR']);
    newrelic_add_custom_parameter('PCID', Strings::GetSess('PCID'));
    newrelic_add_custom_parameter('userEmail', PageVariables::User()->loggedInEmail);
    newrelic_add_custom_parameter('userRole', $userRole);
}

if ($PCID) {
    $subscriberID = PageVariables::PC()->subscriberID;
    $allowPropertyAddressAutoLookUp = PageVariables::PC()->allowPropertyAddressAutoLookUp;
    $nonInclusivePerDiem = PageVariables::PC()->nonInclusivePerDiem;
    $allowCustomFields = PageVariables::PC()->allowCustomFields;
    $allowNestedEntityMembers = PageVariables::PC()->allowNestedEntityMembers;
}

PageVariables::$isGhosted = $_SESSION['isGhosted'] ?? null;
PageVariables::$GhostDetails = $_SESSION['GhostDetails'] ?? null;

PageVariables::Init(get_defined_vars());

// Debug(PageVariables::staticToArray());
