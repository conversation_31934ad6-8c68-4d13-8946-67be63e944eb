<?php

use models\composite\oAcqualify\assignUserToAccount;
use models\composite\oAcqualify\createUser;
use models\composite\oAffiliate\checkAffiliateAEExist;
use models\composite\oAffiliate\checkPromoCodeExist;
use models\composite\oAffiliate\generatePromoCode;
use models\composite\oAffiliate\insertAffiliateInfo;
use models\composite\oBranch\getacqualifyBranchDetails;
use models\composite\oBranch\getReferralSiteInfo;
use models\composite\oBranch\savePreferredAgentForBranch;
use models\composite\oBroker\checkBrokerExistsForPC;
use models\composite\oBroker\createAgentEmployeeReln;
use models\composite\oBroker\deleteAgentEmployeeReln;
use models\composite\oBroker\insertbrokerLoanofficer;
use models\composite\oBroker\insertMultipleLMRAEIDForBroker;
use models\composite\oBroker\saveAgentInfo;
use models\composite\oBroker\saveAgentPCs;
use models\composite\oBroker\updateBrokerLogo;
use models\composite\oBroker\updateLMRBrokerAccessDocs;
use models\composite\oBroker\uploadBrokerDocs;
use models\composite\oFile\getFileInfo\PCInfo;
use models\composite\oFile\getFileInfoAgentWise;
use models\composite\oFileUpdate\updateFileInfoBasedOnAgentType;
use models\composite\oPC\getacqualifyPCDetails;
use models\composite\oPC\getacqualifyUserDetails;
use models\composite\oPC\postacQualifylog;
use models\composite\oUser\avatar;
use models\constants\accessSecondaryWFPC;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\constants\gl\glMimeTypes;
use models\constants\gl\globalWithUSTimeZones;
use models\Controllers\LMRequest\BrokerLoanOfferedDetails;
use models\Controllers\webForm\BrokerRegistration;
use models\Database2;
use models\FileStorage;
use models\lendingwise\tblBrokerAssociatedLoanOfficers;
use models\lendingwise\tblBrokerLoanStatesIn;
use models\lendingwise\tblCompanyLicenseDetails;
use models\lendingwise\tblPersonalLicenseDetails;
use models\cypher;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';

require 'initPageVariables.php';
require 'getPageVariables.php';
//global variables
global $userRole, $userName, $userGroup, $PCTimeZone;

$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
$globalWithUSTimeZones = globalWithUSTimeZones::$globalWithUSTimeZones;

$publicUser = 0;
if (isset($_POST['publicUser'])) {
    $publicUser = trim($_POST['publicUser']);
}

UserAccess::checkReferrerPgs(['url' => 'createAgent.php, brokerRegistration.php']);


if ($publicUser) {
} else {
    UserAccess::CheckAdminUse();
}

/* validate the captcha */
$branchDataArray = [];
$brCode = cypher::myDecryption(trim($_POST['branchReferralCode']));
if ($brCode > 1) {
    $branchDataArray = getReferralSiteInfo::getReport(['branchReferralCode' => $brCode]);
    for ($lr = 0; $lr < count($branchDataArray); $lr++) {
        $allowCaptcha = trim($branchDataArray[$lr]['allowcaptcha']);
    }
}

if (!RECAPTCHA_DISABLE) {
    if ($_POST['publicUser'] && $allowCaptcha) {
        if (isset($_POST['g-recaptcha-response'])) {
            $captcha = $_POST['g-recaptcha-response'];
        }
        if (!$captcha) {
            echo '<h2>Please check the captcha form.</h2>';
            Database2::saveLogQuery();
            exit();
        }
        $ip = $_SERVER['REMOTE_ADDR'];
        $CaptchaSecretKey = PCInfo::getCaptchaKey(trim($_POST['PCID']), 'secretKey');
        $url = 'https://www.google.com/recaptcha/api/siteverify?secret=' . urlencode($CaptchaSecretKey) . '&response=' . urlencode($captcha);
        $response = FileStorage::getFile($url);
        $responseKeys = json_decode($response, true);
        if (!$responseKeys['success']) {
            echo '<h2>Warning.... Invalid Captcha.</h2>';
            Database2::saveLogQuery();
            exit();
        }
    }
}
/* end validate the captcha*/

$email = '';
$firstName = '';
$lastName = '';
$phNo1 = '';
$phNo2 = '';
$phNo3 = '';
$ext = '';
$cellNo1 = '';
$cellNo2 = '';
$cellNo3 = '';
$fax1 = '';
$fax2 = '';
$fax3 = '';
$company = '';
$address = '';
$suiteNumber = '';
$city = '';
$state = '';
$zip = '';
$pwd = '';
$phoneNumber = '';
$cellNumber = '';
$fax = '';
$brokerArray = [];
$insertCount = 0;
$registerDate = date('Y-n-j');
$existsCnt = 0;
$brokerNumber = 0;
$AEID = 0;
$brokerNo = 0;
$updateCount = 0;
$referralCode = 1;
$affiliateInfoArray = [];
$executiveIds = '';
$executiveId = 0;
$userNumber = 0;
$existLMRAEIDs = 0;
$executiveIdArray = [];
$existLMRAEIDArray = [];
$LMRAEIDArray = [];
$allExecutiveIds = '';
$employeeIdAllowArray = [];
$employeeAllowCnt = 0;
$employeeIdNotAllowArray = [];
$employeeNotAllowCnt = 0;
$employeeId = 0;
$employeeIds = '';
$employeeCnt = 0;
$prefEmp = 0;
$allowAgentToAccessLMRDocs = 1;
$allowAgentToSendHomeownerLink = 1;
$allowAgentToEditLMRFile = 0;
$allowAgentToLogin = 0;
$PCID = 0;
$serviceProvider = '';
$allowedToUpdateFiles = 0;
$allowToAccessPrivateNotes = 0;
$sendNewDealAlert = 1;
$receiveUpdates = 1;
$allowToSendMassEmail = 1;
$allowAgentToCreateFiles = 1;
$allowAgentToCreateTasks = 1;
$allowAgentToSeeDashboard = 1;
$accessRestriction = 0;
$allowedToDeleteUplodedDocs = 1;
$allowedToEditOwnNotes = 1;
$agentNotes = '';
$seeBilling = 1;
$permissionToREST = 1;
$allowedToExcelReport = 0;
$adminEdit = 1;
$timeZone = '';
$changeDIYPlan = 0;
$isPrimary = 0;
$AEPartnerCode = 1;
$promoCode = 0;
$opt = false;
$resArray = [];
$allowToLASubmit = 0;
$allowToCFPBSubmit = $allowToEditCCInfo = 0;
$allowToLockLoanFileAgent = 1;
$subscribeToHOME = 0;
$allowToSendFax = 0;
$allowAgentToEditCommission = 1;
$allowAgentToSeeCommission = 1;
$allowEmailCampaign = 1;
$allowToSendFileDesignation = 1;
$allowAgentToSeePublicNotes = 1;
$allowToViewCFPBPipeline = 0;
$allowToSeeWebForms = 0;
$statusOpt = 0;
$allowEditToIR = $allowWorkflowEdit = 0;
$allowAgentToGetBorrowerUploadDocsNotification = 1;
$allowToViewMarketPlace = 1;
$WFIDsArray = [];
$allowToAccessRAM = 0;
$allowToSendMarketingEmailForBRBO = 0;
$TAC = '';
$license = '';
$prefCommunication = [];
$eniNumber = 0;
$brokerPartnerType = '';
$brokerNotes = '';
$brokerStatus = '';
$statusArray = [];
$website = '';
$NMLSLicense = '';
$DRE = '';
$allowToCreateAloware = 0;
$alowareErr = '';
$shareThisFile = 0;
$externalBroker = 0;
$allowToSubmitOffer = 0;
$allowToViewCreditScreening = 0;
$enable2FAAuthentication = 0;
$TwoFAType = 'email';
$userPriceEngineStatus = 0;
$loanpassLogin = '';
$loanpassPassword = '';
$notifyBODocUpload = 0;
$notifyBranchDocUpload = 0;
$notifyLODocUpload = 0;
$notifyBrokerDocUpload = 0;
$notifyDocUploadRequest = 0;
$allowToAccessInternalLoanProgram = 0;

if (isset($_POST['email'])) $email = trim($_POST['email']);
if (isset($_POST['firstName'])) $firstName = trim($_POST['firstName']);
if (isset($_POST['lastName'])) $lastName = trim($_POST['lastName']);
if (isset($_POST['phNo1'])) $phNo1 = trim($_POST['phNo1']);
if (isset($_POST['phNo2'])) $phNo2 = trim($_POST['phNo2']);
if (isset($_POST['phNo3'])) $phNo3 = trim($_POST['phNo3']);
if (isset($_POST['ext'])) $ext = trim($_POST['ext']);
if (isset($_POST['cellNo1'])) $cellNo1 = trim($_POST['cellNo1']);
if (isset($_POST['cellNo2'])) $cellNo2 = trim($_POST['cellNo2']);
if (isset($_POST['cellNo3'])) $cellNo3 = trim($_POST['cellNo3']);
if (isset($_POST['fax1'])) $fax1 = trim($_POST['fax1']);
if (isset($_POST['fax2'])) $fax2 = trim($_POST['fax2']);
if (isset($_POST['fax3'])) $fax3 = trim($_POST['fax3']);
if (isset($_POST['company'])) $company = trim($_POST['company']);
if (isset($_POST['address'])) $address = trim($_POST['address']);
if (isset($_POST['suiteNumber'])) $suiteNumber = trim($_POST['suiteNumber']);
if (isset($_POST['city'])) $city = trim($_POST['city']);
if (isset($_POST['state'])) $state = trim($_POST['state']);
if (isset($_POST['zip'])) $zip = trim($_POST['zip']);
if (isset($_POST['pwd'])) $pwd = trim($_POST['pwd']);
if (isset($_POST['brokerNumber'])) $brokerNumber = trim($_POST['brokerNumber']);
if (isset($_POST['employeeId'])) $employeeId = trim($_POST['employeeId']);
if (isset($_POST['bId'])) $brokerNo = trim($_POST['bId']);
if (isset($_POST['executiveId'])) $executiveIdArray = $_POST['executiveId'];
if (isset($_POST['employeeIds'])) $employeeIds = $_POST['employeeIds'];
if (isset($_POST['adminEdit'])) $adminEdit = $_POST['adminEdit'];
if (isset($_POST['allowAgentToAccessLMRDocs'])) $allowAgentToAccessLMRDocs = trim($_POST['allowAgentToAccessLMRDocs']);
if (isset($_POST['allowAgentToSendHomeownerLink'])) $allowAgentToSendHomeownerLink = trim($_POST['allowAgentToSendHomeownerLink']);
if (isset($_POST['allowAgentToEditLMRFile'])) $allowAgentToEditLMRFile = trim($_POST['allowAgentToEditLMRFile']);
if (isset($_POST['allowAgentToLogin'])) $allowAgentToLogin = trim($_POST['allowAgentToLogin']);
if (isset($_POST['PCID'])) $PCID = trim($_POST['PCID']);
if (isset($_POST['serviceProvider'])) $serviceProvider = trim($_POST['serviceProvider']);
if (isset($_POST['allowToCreateAloware'])) $allowToCreateAloware = trim($_POST['allowToCreateAloware']);
if (isset($_POST['allowedToUpdateFiles'])) $allowedToUpdateFiles = trim($_POST['allowedToUpdateFiles']);
if (isset($_POST['allowToAccessPrivateNotes'])) $allowToAccessPrivateNotes = trim($_POST['allowToAccessPrivateNotes']);
if (isset($_POST['sendNewDealAlert'])) $sendNewDealAlert = trim($_POST['sendNewDealAlert']);
if (isset($_POST['receiveUpdates'])) $receiveUpdates = trim($_POST['receiveUpdates']);
if (isset($_POST['allowToSendMassEmail'])) $allowToSendMassEmail = trim($_POST['allowToSendMassEmail']);
if (isset($_POST['allowAgentToCreateFiles'])) $allowAgentToCreateFiles = trim($_POST['allowAgentToCreateFiles']);
if (isset($_POST['allowAgentToCreateTasks'])) $allowAgentToCreateTasks = trim($_POST['allowAgentToCreateTasks']);
if (isset($_POST['allowAgentToSeeDashboard'])) $allowAgentToSeeDashboard = trim($_POST['allowAgentToSeeDashboard']);
if (isset($_POST['accessRestriction'])) $accessRestriction = trim($_POST['accessRestriction']);
if (isset($_POST['allowedToDeleteUplodedDocs'])) $allowedToDeleteUplodedDocs = trim($_POST['allowedToDeleteUplodedDocs']);
if (isset($_POST['allowEditToIR'])) $allowEditToIR = trim($_POST['allowEditToIR']);
if (isset($_POST['allowWorkflowEdit'])) $allowWorkflowEdit = trim($_POST['allowWorkflowEdit']);
if (isset($_POST['allowAgentToGetBorrowerUploadDocsNotification'])) $allowAgentToGetBorrowerUploadDocsNotification = trim($_POST['allowAgentToGetBorrowerUploadDocsNotification']);

if (isset($_POST['allowToViewMarketPlace'])) $allowToViewMarketPlace = trim($_POST['allowToViewMarketPlace']);

if (!$PCID) {
    $PCID = PageVariables::$PCID;
}

$allowServicing = 0;
if (isset($_POST['allowServicing'])) {
    $allowServicing = trim($_POST['allowServicing']);
}


if (isset($_POST['allowedToEditOwnNotes'])) $allowedToEditOwnNotes = trim($_POST['allowedToEditOwnNotes']);
if (isset($_POST['agentNotes'])) $agentNotes = trim($_POST['agentNotes']);
if (isset($_POST['seeBilling'])) $seeBilling = trim($_POST['seeBilling']);
if (isset($_POST['allowToEditCCInfo'])) $allowToEditCCInfo = trim($_POST['allowToEditCCInfo']);
if (isset($_POST['permissionToREST'])) $permissionToREST = trim($_POST['permissionToREST']);
if (isset($_POST['subscribeToHOME'])) $subscribeToHOME = trim($_POST['subscribeToHOME']);
if (isset($_POST['allowedToExcelReport'])) $allowedToExcelReport = trim($_POST['allowedToExcelReport']);
if (isset($_POST['changeDIYPlan'])) $changeDIYPlan = trim($_POST['changeDIYPlan']);
if (isset($_POST['isPrimary'])) $isPrimary = trim($_POST['isPrimary']);
if (isset($_POST['timeZone'])) $timeZone = trim($_POST['timeZone']);
if (isset($_POST['allowToSeeWebForms'])) $allowToSeeWebForms = trim($_POST['allowToSeeWebForms']);
if (isset($_POST['license'])) $license = trim($_POST['license']);
if (isset($_POST['phoneNumber'])) $phoneNumber = trim($_POST['phoneNumber']);
if (isset($_POST['publicUser'])) $publicUser = trim($_POST['publicUser']);
if (isset($_POST['branchReferralCode'])) $branchReferralCode = trim($_POST['branchReferralCode']);
if (isset($_POST['eniNumber'])) $eniNumber = trim($_POST['eniNumber']);
if (isset($_POST['brokerPartnerType'])) $brokerPartnerType = trim($_POST['brokerPartnerType']);

if (isset($_POST['brokerNotes'])) $brokerNotes = trim($_POST['brokerNotes']);
if (isset($_POST['brokerStatus'])) $brokerStatus = trim($_POST['brokerStatus']);
if (isset($_POST['statusOpt'])) $statusOpt = trim($_POST['statusOpt']);

if (isset($_POST['prefCommunication'])) $prefCommunication = $_POST['prefCommunication'];
if (isset($_POST['website'])) $website = trim($_POST['website']);
if (isset($_POST['shareThisFile'])) $shareThisFile = trim($_POST['shareThisFile']);
if (isset($_POST['NMLSLicense'])) $NMLSLicense = trim($_POST['NMLSLicense']);
if (isset($_POST['DRE'])) $DRE = trim($_POST['DRE']);
if (isset($_POST['allowToSubmitOffer'])) $allowToSubmitOffer = trim($_POST['allowToSubmitOffer']);
if (isset($_POST['allowToViewCreditScreening'])) $allowToViewCreditScreening = trim($_POST['allowToViewCreditScreening']);
if (isset($_POST['enable2FAAuthentication'])) $enable2FAAuthentication = trim($_POST['enable2FAAuthentication']);
if (isset($_POST['TwoFAType'])) $TwoFAType = trim($_POST['TwoFAType']);
if (isset($_POST['userPriceEngineStatus'])) $userPriceEngineStatus = trim($_POST['userPriceEngineStatus']);

if (isset($_POST['allowToLASubmit'])) {
    $allowToLASubmit = trim($_POST['allowToLASubmit']);
}
if (isset($_POST['allowToCFPBSubmit'])) {
    $allowToCFPBSubmit = trim($_POST['allowToCFPBSubmit']);
}
if (isset($_POST['allowEmailCampaign'])) {
    $allowEmailCampaign = trim($_POST['allowEmailCampaign']);
}
if (isset($_POST['allowToSendFax'])) $allowToSendFax = trim($_POST['allowToSendFax']);
if (isset($_POST['allowAgentToEditCommission'])) $allowAgentToEditCommission = trim($_POST['allowAgentToEditCommission']);
if (isset($_POST['allowAgentToSeeCommission'])) $allowAgentToSeeCommission = trim($_POST['allowAgentToSeeCommission']);
if (isset($_POST['allowToSendFileDesignation'])) {
    $allowToSendFileDesignation = trim($_POST['allowToSendFileDesignation']);
}
if (isset($_POST['allowAgentToSeePublicNotes'])) {
    $allowAgentToSeePublicNotes = trim($_POST['allowAgentToSeePublicNotes']);
}
if (isset($_POST['WFID'])) {
    $WFIDsArray = $_POST['WFID'];
}
if (isset($_POST['allowToViewCFPBPipeline'])) {
    $allowToViewCFPBPipeline = trim($_POST['allowToViewCFPBPipeline']);
}
if (isset($_POST['allowAgentToAccessRAM'])) {
    $allowToAccessRAM = trim($_POST['allowAgentToAccessRAM']);
}
if (isset($_POST['allowToSendMarketingEmailForBRBO'])) {
    $allowToSendMarketingEmailForBRBO = trim($_POST['allowToSendMarketingEmailForBRBO']);
}
if (isset($_POST['TAC'])) {
    $TAC = trim($_POST['TAC']);
}
if (isset($_POST['allowToLockLoanFileAgent'])) {
    $allowToLockLoanFileAgent = trim($_POST['allowToLockLoanFileAgent']);
}
if (isset($_POST['externalBroker'])) {
    $externalBroker = trim($_POST['externalBroker']);
}
$loanOfficerId = ($_POST['loanOfficerId']) ?? null;
$loanOfficersList = $_REQUEST['loanOfficersId'] ?? [];

//CRB Reset Permissions
if (!$brokerNumber && glCustomJobForProcessingCompany::isPC_CRB($PCID)) {
    $allowAgentToAccessLMRDocs = 0;
    $allowAgentToEditLMRFile = 0;
    $allowAgentToLogin = 0;
}
/* dummy Broker #12058*/
$overRideMort2Loan = '';
$expirationDate = isset($_POST['expirationDate']) ? $_POST['expirationDate'] : '';
$statusDate = isset($_POST['statusDate']) ? $_POST['statusDate'] : '';
if ($_REQUEST['externalBroker'] != $_REQUEST['existingAgentRole']) {
    $dummyBrokerId = 0;
    if (isset($_REQUEST['dummyBrokerId'])) $dummyBrokerId = $_REQUEST['dummyBrokerId'];
    if (isset($_POST['overRideMort2Loan'])) {
        if ($_POST['overRideMort2Loan'] != '') {
            $overRideMort2Loan = $_POST['overRideMort2Loan'];

            $inArrayOverride['PCID'] = $PCID;
            $inArrayOverride['brokerNumber'] = $brokerNumber;
            $inArrayOverride['agentType'] = $_REQUEST['existingAgentRole'];
            $inArrayOverrideFileData = getFileInfoAgentWise::getReport($inArrayOverride);
            $overRideArrays = [];
            $qryTxt = '';
            if (count($inArrayOverrideFileData) > 0) {
                foreach ($inArrayOverrideFileData as $eachOverRideFile) {
                    if ($_POST['overRideMort2Loan'] == 'Yes') {
                        $overRideArrays[] = $eachOverRideFile['LMRId'];

                    } else if ($_POST['overRideMort2Loan'] == 'No') {

                        if ($_REQUEST['externalBroker'] == '1') {
                            if ($eachOverRideFile['secondaryBrokerNumber'] == '' || $eachOverRideFile['secondaryBrokerNumber'] == '0' || $eachOverRideFile['secondaryBrokerNumber'] == null) {
                                $overRideArrays[] = $eachOverRideFile['LMRId'];
                            }
                        }
                        if ($_REQUEST['externalBroker'] == '0') {
                            if ($eachOverRideFile['brokerNumber'] == '' || $eachOverRideFile['brokerNumber'] == '0' || $eachOverRideFile['brokerNumber'] == null) {
                                $overRideArrays[] = $eachOverRideFile['LMRId'];
                            }
                        }

                    }
                }
                // Create Dummy Agent #12058
                if ($_REQUEST['dummyBrokerId'] == '0') {

                    $dummyBrokerArray = [];
                    $dummyEmailAgent = $PCID . '@dummyAgentemail.com';
                    $AEPartnerCode = 0;
                    $regDate = date('Y-m-d');
                    $dummyBrokerArray = [
                        'firstName'                                     => 'null',
                        'lastName'                                      => '',
                        'company'                                       => '',
                        'email'                                         => $dummyEmailAgent,
                        'phoneNumber'                                   => '',
                        'registerDate'                                  => '',
                        'promoCode'                                     => '',
                        'pwd'                                           => $PCID,
                        'city'                                          => '',
                        'cellNumber'                                    => '',
                        'fax'                                           => '',
                        'agreedTC'                                      => 0,
                        'allowAgentToAccessLMRDocs'                     => 0,
                        'listAllAgents'                                 => 0,
                        'allowAgentToCreateTasks'                       => 0,
                        'allowAgentToSeeDashboard'                      => 0,
                        'allowedToDeleteUplodedDocs'                    => 0,
                        'allowedToEditOwnNotes'                         => 0,
                        'seeBilling'                                    => 0,
                        'allowToSendMarketingEmailForBRBO'              => 0,
                        'allowToSeeWebForms'                            => 0,
                        'allowToViewMarketPlace'                        => 0,
                        'allowToLockLoanFileAgent'                      => 0,
                        'allowToupdateFileAndClient'                    => 0,
                        'allowToSendMassEmail'                          => 0,
                        'sendNewDealAlert'                              => 0,
                        'allowWorkflowEdit'                             => 0,
                        'allowAgentToGetBorrowerUploadDocsNotification' => 0,
                        'allowEmailCampaign'                            => 0,

                    ];

                    $ip = ['email' => $dummyEmailAgent];
                    $AEPartnerCode = checkAffiliateAEExist::getReport($ip);

                    if ($AEPartnerCode > 1) {
                        $promoCode = $AEPartnerCode;
                    } else {
                        do {
                            $promoCode = generatePromoCode::getReport();
                            $ip = ['promoCode' => $promoCode];
                            $opt = checkPromoCodeExist::getReport($ip);
                        } while ($opt);

                        $affiliateInfoArray = [
                            'firstName'  => 'null',
                            'lastName'   => '',
                            'company'    => '',
                            'email'      => $dummyEmailAgent,
                            'pwd'        => '',
                            'webAddress' => '',
                            'promoCode'  => $promoCode,
                            'siteName'   => 'TLP',

                        ];
                        insertAffiliateInfo::getReport($affiliateInfoArray);
                    }
                    $brokerArray = saveAgentInfo::getReport($dummyBrokerArray);

                    if (count($brokerArray) > 0) {
                        if (array_key_exists('brokerNumber', $brokerArray)) $dummyBrokerId = $brokerArray['brokerNumber'];
                        if ($dummyBrokerId > 0) {
                            $agentPCArray = [
                                'PCID'         => $PCID,
                                'brokerNumber' => $dummyBrokerId,

                            ];

                            if (count($executiveIdArray)) {
                                savePreferredAgentForBranch::getReport([
                                    'branchId'       => implode(', ', $executiveIdArray),
                                    'agentId'        => $dummyBrokerId,
                                    'PCID'           => $PCID,
                                    'expirationDate' => $expirationDate,
                                    'statusDate'     => $statusDate
                                ]);
                                saveAgentPCs::getReport($agentPCArray);
                            }

                        }
                    }

                }
                if (count($overRideArrays) > 0) {
                    $overRideFiles = implode(',', $overRideArrays);
                    if ($_REQUEST['externalBroker'] == '1') {
                        $processorCommentstxt = 'Changed <b>' . $firstName . ' ' . $lastName . '</b> From Broker To Loan Officer';
                        $qryTxt = 'secondaryBrokerNumber = ' . $brokerNumber . ',brokerNumber = ' . $dummyBrokerId;
                        $qryTxtWhr = ' where  brokerNumber = ' . $brokerNumber . ' and FPCID = ' . $PCID . ' and activeStatus =1 and LMRID in(' . $overRideFiles . ')';
                    }
                    if ($_REQUEST['externalBroker'] == '0') {
                        $processorCommentstxt = 'Changed <b>' . $firstName . ' ' . $lastName . '</b> From Loan Officer To Broker';
                        $qryTxt = 'brokerNumber = ' . $brokerNumber . ' ,secondaryBrokerNumber = 0 ';
                        $qryTxtWhr = ' where secondaryBrokerNumber = ' . $brokerNumber . ' and FPCID = ' . $PCID . ' and activeStatus =1 and LMRID in(' . $overRideFiles . ')';
                    }

                }

            }
            if ($qryTxt != '' && $qryTxtWhr != '') {
                $qryToUpdate = 'update tblFile set ' . $qryTxt . $qryTxtWhr;

                $ipArrayAgent = [];
                $ipArrayAgent['qry'] = $qryToUpdate;
                $ipArrayAgent['fileIds'] = $overRideFiles;
                $ipArrayAgent['processorNotes'] = $processorCommentstxt;
                $ipArrayAgent['isSysNotes'] = 1;
                $ipArrayAgent['employeeId'] = $_SESSION['userNumber'];
                updateFileInfoBasedOnAgentType::getReport($ipArrayAgent);
            }
        }
    }
}
/* end of dummy Broker #12058*/

if ($userRole == 'Branch') $redirectFile = CONST_URL_BR;
elseif ($userRole == 'Agent') $redirectFile = CONST_URL_AG;
else    $redirectFile = CONST_BO_URL;

$pcAcqualifyDetails = getacqualifyPCDetails::getReport(['pcid' => $PCID]);

if ($allowToViewCreditScreening == 1 && count($pcAcqualifyDetails) > 0) {

    $BrokerAcqualifyArray = [];
    $BrokerAcqualifyArray = ['lwUserId' => $brokerNumber, 'userRole' => 'Agent'];
    $BrokerAcqualifyDetails = getacqualifyUserDetails::getReport($BrokerAcqualifyArray);

    $acAccountId = $pcAcqualifyDetails[0]['accountId'];
    $acqualifyArray['apiToken'] = '4J2Qn9R0Kp6XenAOj5nUBed55Gsy9Ais';

    if (count($BrokerAcqualifyDetails) > 0) {
        $acqualifyArray['data'] = ['name' => Strings::escapeQuoteNew($firstName) . ' ' . Strings::escapeQuoteNew($lastName), 'notificationEmail' => $email];
        $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/update-user';
        $acUserId = $BrokerAcqualifyDetails[0]['userId'];
    } else {
        $acqualifyArray['data'] = ['name' => Strings::escapeQuoteNew($firstName) . ' ' . Strings::escapeQuoteNew($lastName), 'notificationEmail' => $email, 'username' => $brokerNumber . '+' . $email];
        $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/create-user';

        $acQualifyResponse = createUser::getReport($acqualifyArray);
        $acQualifyResponse = json_decode($acQualifyResponse);

        if ($acQualifyResponse->status == 'success') {
            if (isset($acQualifyResponse->user)) {

                $qry = "insert into acqualifyUsers (lwUserId,`name`,email,loginKey,userId,createdDate,userRole) values ('" . $brokerNumber . "','" . $acQualifyResponse->user->name . "','" . $acQualifyResponse->user->email . "','" . $acQualifyResponse->user->loginKey . "','" . $acQualifyResponse->user->id . "','" . Dates::Timestamp() . "','Agent');";

                $acqualifyInsertId = Database2::getInstance()->insert($qry);
            }
            $acUserId = $acQualifyResponse->user->id;
        }
    }
    $acqualifyPostArray['url'] = $acqualifyArray['url'];
    $acqualifyPostArray['request'] = $acqualifyArray;
    $acqualifyPostArray['response'] = $acQualifyResponse;
    $acqualifyPostArray['pcid'] = $PCID;
    postacQualifylog::getReport($acqualifyPostArray);


    if ($acUserId > 0) {
        $BRANCHAcqualifyUserId = [];
        $BRANCHAcqualifyArray = [];
        if (count($executiveIdArray) > 0) {
            $BRANCHAcqualifyArray = ['executiveId' => implode(',', $executiveIdArray)];
            $BRANCHAcqualifyArray = getacqualifyBranchDetails::getReport($BRANCHAcqualifyArray);
            foreach ($BRANCHAcqualifyArray as $BRANCHAcqualifyVal) {
                $BRANCHAcqualifyUserId[] = $BRANCHAcqualifyVal['userId'];
            }
            $acqualifyArray['data'] = ['userId' => $acUserId, 'accountId' => $acAccountId, 'role' => 'member', 'branchIds' => [implode(',', $BRANCHAcqualifyUserId)]];
        } else {
            $acqualifyArray['data'] = ['userId' => $acUserId, 'accountId' => $acAccountId, 'role' => 'member', 'branchIds' => []];
        }

        $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/assign-user-to-account';

        $acQualifyResponse = assignUserToAccount::getReport($acqualifyArray);
        $acQualifyResponse = json_decode($acQualifyResponse);

        if ($acQualifyResponse->status == 'success') {
            if (isset($acQualifyResponse->roleInfo)) {

                $qry = "update acqualifyUsers set accountId = '" . $acQualifyResponse->roleInfo->accountId . "', role = '" . $acQualifyResponse->roleInfo->role . "',branchIds = '" . implode(',', $acQualifyResponse->roleInfo->branchIds) . "'  where  userId ='" . $acUserId . "' and userRole ='Agent' ";

                $acqualifyInsertId = Database2::getInstance()->update($qry);
            }
            $acUserId = $acQualifyResponse->user->id;
        }

        $acqualifyPostArray['url'] = $acqualifyArray['url'];
        $acqualifyPostArray['request'] = $acqualifyArray;
        $acqualifyPostArray['response'] = $acQualifyResponse;
        $acqualifyPostArray['pcid'] = $PCID;
        postacQualifylog::getReport($acqualifyPostArray);

    }
}


if ($brokerNumber > 0 && $PCID > 0 && $statusOpt == 4) {
    $ucnt = saveAgentPCs::getReport([
        'brokerNumber'   => $brokerNumber,
        'PCID'           => $PCID,
        'brokerStatus'   => $brokerStatus,
        'brokerNotes'    => $brokerNotes,
        'userRole'       => $userRole,
        'userName'       => $userName,
        'expirationDate' => $expirationDate,
        'statusDate'     => $statusDate
    ]);
    $redirectFile .= 'createAgent.php?agentType=' . $externalBroker . '&bId=' . cypher::myEncryption($brokerNumber) . '&tabNumb=4';
    if ($ucnt > 0) {
        //Strings::SetSess("msg", "Successfully Updated.");
        $responseAjax = ['code' => '100', 'msg' => 'Successfully Updated.', 'redirecturl' => $redirectFile];
    } else {
        //Strings::SetSess("msg", "Error While Saving Data.");
        $responseAjax = ['code' => '101', 'msg' => 'Error While Saving Data.', 'redirecturl' => $redirectFile];
    }
    echo json_encode($responseAjax);
    exit();
}

$empIdArray = explode(',', $employeeIds);
for ($em = 0; $em < count($empIdArray); $em++) {
    $empId = 0;
    ${'employee_' . $empId} = '';
    $empId = $empIdArray[$em];
    if (isset($_POST['employee_' . $empId])) {
        ${'employee_' . $empId} = $_POST['employee_' . $empId];
        ${'employee_' . $empId} = trim(${'employee_' . $empId});
        if (${'employee_' . $empId} != '') {
            if (${'employee_' . $empId} == 0) {
                $employeeIdNotAllowArray[] = $empId;
            } else if (${'employee_' . $empId} == 1) {
                $employeeIdAllowArray[] = $empId;
            }
        }
    }

}

$employeeCnt = count($empIdArray);
$employeeAllowCnt = count($employeeIdAllowArray);
$employeeNotAllowCnt = count($employeeIdNotAllowArray);

if (isset($_POST['cellNumber'])) $cellNumber = trim($_POST['cellNumber']);
if (isset($_POST['fax'])) $fax = trim($_POST['fax']);

$phoneNumber = preg_replace('/[^0-9]/', '', $phoneNumber);                   // | Phone Number Formatting
$cellNumber = preg_replace('/[^0-9]/', '', $cellNumber);                    // | Phone Number Formatting
$fax = preg_replace('/[^0-9]/', '', $fax);                           // | Phone Number Formatting

if ($brokerNumber > 0 || $publicUser == 1) {
    /** Check Agent already exists while create new agent **/
} else {
    $ip = ['email' => $email, 'pcid' => $PCID];
    //$resArray = $oBroker->checkBrokerExists($ip);
    $resArray = checkBrokerExistsForPC::getReport($ip);
    if (count($resArray) > 0) {
        $redirectFile = '/backoffice/brokers';
        header('Location: ' . $redirectFile);
        exit();
    }
}
$infoArray = [
    'PCID'                      => $PCID,
    'email'                     => $email,
    'firstName'                 => $firstName,
    'lastName'                  => $lastName,
    'phoneNumber'               => $phoneNumber,
    'cellNumber'                => $cellNumber,
    'fax'                       => $fax,
    'company'                   => $company,
    'address'                   => $address,
    'suiteNumber'               => $suiteNumber,
    'city'                      => $city,
    'state'                     => $state,
    'zipCode'                   => $zip,
    'pwd'                       => $pwd,
    'agreedTC'                  => 1,
    'promoCode'                 => '',
    'registerDate'              => $registerDate,
    'referralCode'              => $referralCode,
    'strPosition'               => '',
    'receiveNews'               => 1,
    'hearAboutUs'               => '',
    'FHAApproved'               => '',
    'principalBroker'           => 0,
    'receiveLMInfo'             => 0,
    'privateBroker'             => '1',
    'regFrom'                   => 'LM',
    'serviceProvider'           => $serviceProvider,
    //                        "allowedToUpdateFiles" => $allowedToUpdateFiles,
    'allowToAccessPrivateNotes' => $allowToAccessPrivateNotes,
    'sendNewDealAlert'          => $sendNewDealAlert,
    //                        "receiveUpdates" => $receiveUpdates,
    //                        "allowAgentToCreateFiles" => $allowAgentToCreateFiles,
    'allowAgentToCreateTasks'   => $allowAgentToCreateTasks,
    //                        "allowAgentToSeeDashboard" => $allowAgentToSeeDashboard,
    //                        "allowedToDeleteUplodedDocs" => $allowedToDeleteUplodedDocs,
    //                        "allowedToEditOwnNotes" => $allowedToEditOwnNotes,
    //                        "agentNotes" => $agentNotes,
    'seeBilling'                => $seeBilling,
    'allowToEditCCInfo'         => $allowToEditCCInfo,
    //                      "isPrimary" => $isPrimary,
    'permissionToREST'          => $permissionToREST,
    //                        "subscribeToHOME" => $subscribeToHOME,
    'allowedToExcelReport'      => $allowedToExcelReport,
    //                        "changeDIYPlan"          => $changeDIYPlan,
    'timeZone'                  => $timeZone,

    'brokerNumber'               => $brokerNumber,
    'allowToSendFax'             => $allowToSendFax,
    //                        'allowAgentToEditCommission' => $allowAgentToEditCommission,
    //                        'allowAgentToSeeCommission' => $allowAgentToSeeCommission,
    'userNumber'                 => $userNumber,
    'userGroup'                  => $userGroup,
    'license'                    => $license,
    'eniNumber'                  => $eniNumber,
    'brokerPartnerType'          => $brokerPartnerType,
    'prefCommunication'          => $prefCommunication,
    'allowToLockLoanFileAgent'   => $allowToLockLoanFileAgent,
    'website'                    => $website,
    'NMLSLicense'                => $NMLSLicense,
    'DRE'                        => $DRE,
    'shareThisFile'              => $shareThisFile,
    'externalBroker'             => $externalBroker,
    'allowToSubmitOffer'         => $allowToSubmitOffer,
    'allowToViewCreditScreening' => $allowToViewCreditScreening,
    'enable2FAAuthentication'    => $enable2FAAuthentication,
    'TwoFAType'                  => $TwoFAType,
    'userPriceEngineStatus'      => $userPriceEngineStatus,
];

if (isset($_POST['allowAgentToLogin'])) $infoArray['allowAgentToLogin'] = $allowAgentToLogin;
if (isset($_POST['allowToLASubmit'])) {
    $infoArray['allowToLASubmit'] = $allowToLASubmit;
}
if (isset($_POST['allowToCFPBSubmit'])) {
    $infoArray['allowToCFPBSubmit'] = $allowToCFPBSubmit;
}
if (isset($_POST['allowEmailCampaign'])) {
    $infoArray['allowEmailCampaign'] = $allowEmailCampaign;
}
if (isset($_POST['allowToSendFileDesignation'])) {
    $infoArray['allowToSendFileDesignation'] = $allowToSendFileDesignation;
}
if (isset($_POST['allowAgentToSeePublicNotes'])) {
    $infoArray['allowAgentToSeePublicNotes'] = $allowAgentToSeePublicNotes;
}
if (isset($_POST['allowToViewCFPBPipeline'])) {
    $infoArray['allowToViewCFPBPipeline'] = $allowToViewCFPBPipeline;
}
if (isset($_POST['allowToAccessRAM'])) {
    $infoArray['allowToAccessRAM'] = $allowToAccessRAM;
}
if (isset($_POST['allowedToEditOwnNotes'])) {
    $infoArray['allowedToEditOwnNotes'] = $allowedToEditOwnNotes;
}
if (isset($_POST['allowAgentToEditCommission'])) {
    $infoArray['allowAgentToEditCommission'] = $allowAgentToEditCommission;
}
if (isset($_POST['isPrimary'])) {
    $infoArray['isPrimary'] = $isPrimary;
}
if (isset($_POST['allowAgentToSeeCommission'])) {
    $infoArray['allowAgentToSeeCommission'] = $allowAgentToSeeCommission;
}
if (isset($_POST['allowToCreateAloware'])) {
    $infoArray['allowToCreateAloware'] = $allowToCreateAloware;
}
if (isset($_POST['allowedToUpdateFiles'])) {
    $infoArray['allowedToUpdateFiles'] = $allowedToUpdateFiles;
}
if (isset($_POST['allowAgentToCreateFiles'])) {
    $infoArray['allowAgentToCreateFiles'] = $allowAgentToCreateFiles;
}
if (isset($_POST['allowAgentToSeeDashboard'])) {
    $infoArray['allowAgentToSeeDashboard'] = $allowAgentToSeeDashboard;
}
if (isset($_POST['subscribeToHOME'])) {
    $infoArray['subscribeToHOME'] = $subscribeToHOME;
}
if (isset($_POST['receiveUpdates'])) {
    $infoArray['receiveUpdates'] = $receiveUpdates;
}
if (isset($_POST['changeDIYPlan'])) {
    $infoArray['changeDIYPlan'] = $changeDIYPlan;
}
if (isset($_POST['allowToSendMarketingEmailForBRBO'])) {
    $infoArray['allowToSendMarketingEmailForBRBO'] = $allowToSendMarketingEmailForBRBO;
}
if (isset($_POST['allowedToDeleteUplodedDocs'])) {
    $infoArray['allowedToDeleteUplodedDocs'] = $allowedToDeleteUplodedDocs;
}
if (isset($_POST['allowEditToIR'])) {
    $infoArray['allowEditToIR'] = $allowEditToIR;
}
if (isset($_POST['allowWorkflowEdit'])) {
    $infoArray['allowWorkflowEdit'] = $allowWorkflowEdit;
}
if (isset($_POST['allowAgentToGetBorrowerUploadDocsNotification'])) {
    $infoArray['allowAgentToGetBorrowerUploadDocsNotification'] = $allowAgentToGetBorrowerUploadDocsNotification;
}

if (isset($_POST['allowToViewMarketPlace'])) {
    $infoArray['allowToViewMarketPlace'] = $allowToViewMarketPlace;
}

if (isset($_POST['allowServicing'])) {
    $infoArray['allowServicing'] = $allowServicing;
}

if (isset($_POST['allowToSeeWebForms'])) $infoArray['allowToSeeWebForms'] = $allowToSeeWebForms;

if (isset($_POST['TAC'])) {
    $infoArray['TAC'] = HTTP::escapeQuoteForPOST($TAC);
}

/*
* Allow Secondary WF feature for the PCs =
* Dave PC, Enrollment Advisory, Law offices
*/
if (($userRole == 'Manager' || $userRole == 'Administrator' || $userRole == 'Super') && (in_array($PCID, $accessSecondaryWFPC))) {
    $infoArray['WFIDs'] = $WFIDsArray;
}

if (isset($_POST['notifyBODocUpload'])) {
    $notifyBODocUpload = $_POST['notifyBODocUpload'];
    $infoArray['notifyBODocUpload'] = $notifyBODocUpload;
}

if (isset($_POST['notifyBranchDocUpload'])) {
    $notifyBranchDocUpload = $_POST['notifyBranchDocUpload'];
    $infoArray['notifyBranchDocUpload'] = $notifyBranchDocUpload;
}
if ($externalBroker == '1') { //LO
    if (isset($_POST['notifyBrokerDocUpload'])) {
        $notifyBrokerDocUpload = $_POST['notifyBrokerDocUpload'];
        $infoArray['notifyBrokerDocUpload'] = $notifyBrokerDocUpload;
    }
} else { //Broker
    if (isset($_POST['notifyLODocUpload'])) {
        $notifyLODocUpload = $_POST['notifyLODocUpload'];
        $infoArray['notifyLODocUpload'] = $notifyLODocUpload;
    }
}

if (isset($_POST['notifyDocUploadRequest'])) {
    $notifyDocUploadRequest = $_POST['notifyDocUploadRequest'];
    $infoArray['notifyDocUploadRequest'] = $notifyDocUploadRequest;
}

if (isset($_POST['loanpassLogin'])) {
    $loanpassLogin = $_POST['loanpassLogin'];
    $infoArray['loanpassLogin'] = $loanpassLogin;
}
if (isset($_POST['loanpassPassword'])) {
    $loanpassPassword = $_POST['loanpassPassword'];
    $infoArray['loanpassPassword'] = $loanpassPassword;
}
if (isset($_POST['allowToAccessInternalLoanProgram'])) {
    $infoArray['allowToAccessInternalLoanProgram'] = $_POST['allowToAccessInternalLoanProgram'];
}
if (isset($_POST['allowToViewAutomationPopup'])) {
    $infoArray['allowToViewAutomationPopup'] = $_POST['allowToViewAutomationPopup'];
}
if (isset($_POST['allowToCopyFile'])) {
    $infoArray['allowToCopyFile'] = $_POST['allowToCopyFile'];
}
if (isset($_POST['ssnNumber'])) {
    $infoArray['ssnNumber'] = $_POST['ssnNumber'];
}
if (isset($_POST['linkedInURL'])) {
    $infoArray['linkedInURL'] = $_POST['linkedInURL'];
}
if (isset($_POST['ofEmployees'])) {
    $infoArray['ofEmployees'] = $_POST['ofEmployees'];
}
if (isset($_POST['bestServeYourNeeds'])) {
    $infoArray['bestServeYourNeeds'] = $_POST['bestServeYourNeeds'];
}
if (isset($_POST['useSameContactInfo'])) {
    $infoArray['useSameContactInfo'] = $_POST['useSameContactInfo'];
} else {
    $infoArray['useSameContactInfo'] = 0;
}
if (isset($_POST['primaryContactFName'])) {
    $infoArray['primaryContactFName'] = $_POST['primaryContactFName'];
}
if (isset($_POST['primaryContactLName'])) {
    $infoArray['primaryContactLName'] = $_POST['primaryContactLName'];
}
if (isset($_POST['primaryContactPhone'])) {
    $infoArray['primaryContactPhone'] = $_POST['primaryContactPhone'];
}
if (isset($_POST['primaryContactEmail'])) {
    $infoArray['primaryContactEmail'] = $_POST['primaryContactEmail'];
}
if (isset($_POST['allowToAssignBOEmployee'])) {
    $infoArray['allowToAssignBOEmployee'] = $_POST['allowToAssignBOEmployee'];
}
if (isset($_POST['allowToSeeAllBrokers'])) {
    $infoArray['allowToSeeAllBrokers'] = $_POST['allowToSeeAllBrokers'];
}
if (isset($_POST['allowToEditLoanStage'])) {
    $infoArray['allowToEditLoanStage'] = $_POST['allowToEditLoanStage'];
}

$infoArray['personalNMLSLicense'] = $_POST['personalNMLSLicense'] ?? '';
$infoArray['haveCompanyNMLS'] = $_POST['haveCompanyNMLS'] ?? '';
$infoArray['havePersonalNMLS'] = $_POST['havePersonalNMLS'] ?? '';
$infoArray['haveCompanyStateLicense'] = $_POST['haveCompanyStateLicense'] ?? '';
$infoArray['havePersonalStateLicense'] = $_POST['havePersonalStateLicense'] ?? '';
$infoArray['statesAuthorizedToOriginate'] = $_POST['statesAuthorizedToOriginate'] ?? [];
$infoArray['approvedReferralFee'] = Request::GetClean('approvedReferralFee') ?? null;
$infoArray['allowToMassUpdate'] = Request::GetClean('allowToMassUpdate') ?? null;
$infoArray['allowToManageDraws'] = Request::GetClean('allowToManageDraws') ?? null;

$typeOfLoansOfferedOnLoadArray = [];
$deletedTypesOfLoansoffered = [];
$typeOfLoansOffered = $_REQUEST['typeOfLoansOffered'] ?? [];
$typeOfLoansOfferedOnLoad = $_REQUEST['typeOfLoansOfferedOnLoad'] ?? [];
if ($typeOfLoansOfferedOnLoad) {
    $typeOfLoansOfferedOnLoadArray = explode(',', $typeOfLoansOfferedOnLoad);
}
$deletedTypesOfLoansoffered = array_values(array_diff($typeOfLoansOfferedOnLoadArray, $typeOfLoansOffered));


// if ($brokerNumber > 0) {
//     $resultArray = $oBroker->saveAgentInfo($infoArray);
// } else {
/**
 * Agent affiliate not created issue.
 * April 4, 2019
 */
$AEPartnerCode = checkAffiliateAEExist::getReport(['email' => $email]);
if ($AEPartnerCode > 1) {
    $promoCode = $AEPartnerCode;
} else {
    do {
        $promoCode = generatePromoCode::getReport();
        $opt = checkPromoCodeExist::getReport(['promoCode' => $promoCode]);
    } while ($opt);

    insertAffiliateInfo::getReport([
        'firstName'  => $firstName,
        'lastName'   => $lastName,
        'company'    => $company,
        'email'      => $email,
        'pwd'        => $pwd,
        'webAddress' => '',
        'promoCode'  => $promoCode,
        'siteName'   => 'TLP',
    ]);
}

$resultArray = saveAgentInfo::getReport($infoArray);
//}
//update the Broker Logo

if (count($resultArray) > 0) {
    if (array_key_exists('insertCount', $resultArray)) $insertCount = trim($resultArray['insertCount']);
    if (array_key_exists('updateCount', $resultArray)) $updateCount = trim($resultArray['updateCount']);
    if (array_key_exists('brokerNumber', $resultArray)) $brokerNumber = trim($resultArray['brokerNumber']);
    if ($brokerNumber > 0) {

        if ($PCID > 0) {
            $inputArray['brokerNumber'] = $brokerNumber;
            $inputArray['PCID'] = $PCID;
            $inputArray['expirationDate'] = $expirationDate;
            $inputArray['statusDate'] = $statusDate;
            saveAgentPCs::getReport($inputArray);
        }     // | Agent PC Relation...

        if ($userRole == 'Agent' && $brokerNumber > 0) {
            insertbrokerLoanofficer::getReport(['loanofficerId' => $_SESSION['userNumber'], 'brokerId' => $brokerNumber]);
        }
        if ($publicUser == 1 && $loanOfficerId) {
            insertbrokerLoanofficer::getReport(['loanofficerId' => cypher::myDecryption($loanOfficerId), 'brokerId' => $brokerNumber]);
        }

        $ip = ['executiveIdArray' => $executiveIdArray, 'brokerNumber' => $brokerNumber, 'PCID' => $PCID];
        insertMultipleLMRAEIDForBroker::getReport($ip);

        $ip = ['allowAgentToAccessLMRDocs' => $allowAgentToAccessLMRDocs, 'allowAgentToEditLMRFile' => $allowAgentToEditLMRFile, 'allowAgentToLogin' => $allowAgentToLogin, 'brokerNumber' => $brokerNumber];
        if (isset($_POST['allowAgentToSendHomeownerLink'])) {
            $ip['allowAgentToSendHomeownerLink'] = $allowAgentToSendHomeownerLink;
        }
        updateLMRBrokerAccessDocs::getReport($ip);
        /**
         * New Broker Registration Email
         */

        if ($insertCount) {
            BrokerRegistration::emailNotification(
                $PCID,
                $executiveIdArray,
                intval($brokerNumber),
                ($publicUser ? ($loanOfficerId ? [intval(cypher::myDecryption($loanOfficerId))] : []) : $loanOfficersList),
            );
        }

        $updateCount += uploadBrokerDocs::getReport(['brokerNumber' => $brokerNumber, 'userGroup' => $userGroup]);

        /**
         * Card # :530
         * Description      : Aloware integration
         * Functionality    : Agent creation process.
         */
        if ($allowToCreateAloware == 1) {
            $aloware = new alowareApi();
            $resuser = $aloware->getUserAlowareInfo(['bid' => $brokerNumber]);
            if (empty($resuser)) {
                $request['internal_company_id'] = $PCID;
                $request['company_name'] = $company;
                $request['company_country'] = 'US';
                $request['internal_user_id'] = $brokerNumber;
                $request['user_name'] = $firstName;
                $request['user_phone_number'] = $cellNumber;
                $request['user_email'] = $email;
                $request['user_timezone'] = Arrays::getArrayValue($PCTimeZone, $globalWithUSTimeZones);
                $creteUserJson = $aloware->creteUser($request);
                $creteUserJsonDecrypt = json_decode($creteUserJson, true);
                if (isset($creteUserJsonDecrypt['api_token'])) {
                    $alowareInfo['bid'] = $brokerNumber;
                    $alowareInfo['api_token'] = $creteUserJsonDecrypt['api_token'];
                    $alowareinfo = $aloware->saveUserAloware($alowareInfo);
                } else {
                    $qryUp = ' UPDATE tblAgent SET allowToCreateAloware = 0 WHERE userNumber =' . $brokerNumber;
                    Database2::getInstance()->executeQuery($qryUp);
                }
                if (isset($creteUserJsonDecrypt['errors'])) {
                    foreach ($creteUserJsonDecrypt['errors'] as $errKey => $errValue) {
                        $alowareErr .= $errValue[0] . '<br>';
                    }
                }
            }
        }
        /* Aloware end. */
    }

    if (($brokerNumber > 0) && (($employeeAllowCnt > 0) || ($employeeNotAllowCnt > 0) || ($employeeCnt > 0))) {
        $ip = ['brokerNumber' => $brokerNumber];
        deleteAgentEmployeeReln::getReport($ip);

        $ip['employeeIdArray'] = $employeeIdAllowArray;
        $ip['allowToCommunicate'] = '1';
        createAgentEmployeeReln::getReport($ip);
        $ip['employeeIdArray'] = $employeeIdNotAllowArray;
        $ip['allowToCommunicate'] = '0';
        createAgentEmployeeReln::getReport($ip);
    }


    $file_type = $_FILES['logo']['type'];
    if ($file_type != '') {
        if (in_array($file_type, glMimeTypes::$imageTypes)) {
            if ($_FILES['logo']['name'] != '') {
                if (!(is_dir(CONST_BROKER_LOGO_PATH))) {
                    mkdir(CONST_BROKER_LOGO_PATH);
                }
                $specialCharacters = ['%', '+', '&', '@', '?', '#', '*', '$', '^', '!'];
                $spaces = [' ', '  ', '   ', '    ', '     '];
                $file_name = str_replace($spaces, '_', $_FILES['logo']['name']);
                $file_name = str_replace($specialCharacters, '', $file_name);
                $logo = 'logo_LMR_' . $brokerNumber . '_' . $file_name;
                $fileTempName = $_FILES['logo']['tmp_name'];

                $logoExit = false;
                $logoExit = file_exists(CONST_BROKER_LOGO_PATH . $logo);
                if ($logoExit) unlink(CONST_BROKER_LOGO_PATH . basename($logo));
                if ($file_type == 'image/gif') {
                    $pos = strrpos($logo, '.');
                    $replaceString = substr($logo, $pos + 1);
                    $logo = str_replace($replaceString, 'jpg', $logo);
                    $fileName = CONST_BROKER_LOGO_PATH . $logo;
                    convertGIF2JPG($fileTempName, $fileName, 'logo');
                } else if (($file_type == 'image/png') || ($file_type == 'image/x-png')) {
                    $pos = strrpos($logo, '.');
                    $replaceString = substr($logo, $pos + 1);
                    $logo = str_replace($replaceString, 'jpg', $logo);
                    $fileName = CONST_BROKER_LOGO_PATH . $logo;
                    convertpng2JPG($fileTempName, $fileName, 'logo');
                } else {
                    $pos = strrpos($logo, '.');
                    $replaceString = substr($logo, $pos + 1);
                    $logo = str_replace($replaceString, 'jpg', $logo);
                    $fileName = CONST_BROKER_LOGO_PATH . $logo;
                    resizeJPGImg($fileTempName, $fileName, 'logo');
                }
                $upCnt = 0;
                $upCnt = updateBrokerLogo::getReport($brokerNumber, $logo);
                if ($upCnt > 0 && trim($logo) != '' && $userRole == 'Agent') {
                    Strings::SetSess('userLogo', $logo);
                }
                if ($upCnt > 0 && trim($logo) != '') {
                    Strings::SetSess('brokerLogo', $logo);
                }
            }
        } else {
            Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');

        }
    }

}
if ($brokerNumber && $_FILES['agentAvatar']['type']
    && in_array($_FILES['agentAvatar']['type'], glMimeTypes::$imageTypes)
    && $_FILES['agentAvatar']['size']) {
    $agentAvatar = '/avatar/broker/' . $brokerNumber . '.' . pathinfo($_FILES['agentAvatar']['name'], PATHINFO_EXTENSION);
    FileStorage::moveFile($_FILES['agentAvatar']['tmp_name'], $agentAvatar);
    avatar::updateAgent($brokerNumber, $agentAvatar);
}


$tblBrokerLoanStatesIn = new tblBrokerLoanStatesIn();
$tblBrokerAssociatedLoanOfficers = new tblBrokerAssociatedLoanOfficers();
$tblCompanyLicenseDetails = new tblCompanyLicenseDetails();
$tblPersonalLicenseDetails = new tblPersonalLicenseDetails();

$updateCount += BrokerLoanOfferedDetails::saveData($_REQUEST['broker'], $brokerNumber, $typeOfLoansOffered);
$updateCount += BrokerLoanOfferedDetails::deleteData($deletedTypesOfLoansoffered, $brokerNumber);
$updateCount += $tblBrokerLoanStatesIn->saveData($_REQUEST['states'], $brokerNumber);
if (sizeof($loanOfficersList)) {
    $updateCount += $tblBrokerAssociatedLoanOfficers->saveData($loanOfficersList, $brokerNumber);
}
$updateCount += $tblCompanyLicenseDetails->saveData(Request::GetClean('companyLicense'), $brokerNumber);
$updateCount += $tblPersonalLicenseDetails->saveData(Request::GetClean('personalLicense'), $brokerNumber);


if ($publicUser == 1) {
    $redirectUrl = 'backoffice/brokerRegThankPage.php?bRc=' . cypher::myEncryption($branchReferralCode) . '&aId=' . cypher::myEncryption($brokerNumber) . '&branchId=' . cypher::myEncryption($executiveIdArray[0]);
    //header("Location: " . $redirectUrl);
    $responseAjax = ['code' => '100', 'msg' => 'Successfully Inserted.', 'redirecturl' => $redirectUrl];
    echo json_encode($responseAjax);
    exit();
}


if ($alowareErr != '') {
    Strings::SetSess('msg', $alowareErr);
} // Aloware Error messages.
$redirectFile .= 'createAgent.php?agentType=' . $externalBroker . '&bId=' . cypher::myEncryption($brokerNumber) . '&tabNumb=1';

if ($insertCount > 0) {
    //Strings::SetSess("msg", "Successfully Inserted.");
    $responseAjax = ['code' => '100', 'msg' => 'Successfully Inserted.', 'redirecturl' => $redirectFile];
} elseif ($updateCount > 0) {
    //Strings::SetSess("msg", "Successfully Updated.");
    $responseAjax = ['code' => '100', 'msg' => 'Successfully Updated!', 'redirecturl' => $redirectFile];
} else {
    //Strings::SetSess("msg", "Sorry Error while Insertion.");
    $responseAjax = ['code' => '101', 'msg' => 'No Changes to Record'];
}
echo json_encode($responseAjax);
exit();
