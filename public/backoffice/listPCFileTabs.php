<?php
global $fileStatusModules, $modulesArray, $assignedPCID, $allowPCUsersToMarketPlace, $subscribePCToHOME, $isPCActiveAloware,
       $allowPeerstreet, $allowServicing, $tabOrderFor, $searchFileTabModules;

use models\composite\oFileTabs\getPCFileTabsDispOrder;
use models\constants\gl\glCustomJobForProcessingCompany;
use models\cypher;
use models\PageVariables;
use models\standard\Strings;

$PSId = 0;
$inputArray = [];
$primaryStatus = '';
$dispOrder = '';
$allowOthersToUpdate = 1;
$rowCnt = 0;
$filesCntArray = [];
$buttonDisp = 'display:none';
$countDisp = 'display:none';
$fileStatusArray = [];
$statusArrayKeys = [];
$fileTabsResArray = $fileTabsArray = [];
$PCFileTabInfo = [];
$fileTabModuleInfoArray = [];
$PCSelectedFileTabArray = [];
$PCSelectedModulesArray = [];
if ($tabOrderFor == 'Client') {
    $fileStatusModules = $searchFileTabModules;
}
if ($fileStatusModules != '') {
    for ($j = 0; $j < count($modulesArray); $j++) {
        if (trim($fileStatusModules) == trim($modulesArray[$j]['moduleCode'])) {
            $PCSelectedModulesArray[] = $modulesArray[$j];
            break;
        }
    }
} else {
    $PCSelectedModulesArray = $modulesArray;
}

$PCFileTabModuleCode = [];
$PCSelectedFileTabModuleCode = '';
foreach ($PCSelectedModulesArray as $eachPCModule) {
    $PCFileTabModuleCode[] = $eachPCModule['moduleCode'];
}
$PCSelectedFileTabModuleCode = implode(',', $PCFileTabModuleCode);

$fileTabsResArray = getPCFileTabsDispOrder::getReport([
    'PCID' => $assignedPCID,
    'moduleCode' => $PCSelectedFileTabModuleCode,
    'tabOrderFor' => $tabOrderFor,
]);
$PCFileTabsDispOrderArray = $fileTabsResArray['PCFileTabsDispOrder'] ?? [];
$enabledDrawManagementV2 = PageVariables::PC()->drawManagement && PageVariables::PC()->enableDrawManagementV2 ?? 0;
?>
<div class="row ">
    <div class="col-md-12">
        <div class="table-responsive">
            <table class="table table-hover table-bordered table-condensed table-sm table-vertical-center"
                   id="<?php if ($tabOrderFor == 'PC') {
                       echo 'PCFileTabsTable';
                   } else {
                       echo 'clientFileTabsTable';
                   } ?>">
                <thead class="thead-light">
                <tr>
                    <th>Tab Name</th>
                    <th>File Type(s)</th>
                    <th>Action</th>
                </tr>
                </thead>
                <tbody id="<?php if ($tabOrderFor == 'PC') {
                    echo 'tabledivbody1';
                } else {
                    echo 'clientTabledivbody';
                } ?>" class="ui-sortable-helper">
                <?php
                foreach ($PCFileTabsDispOrderArray as $eachPCFileTabs) {
                    $tabName = '';
                    $PCFOID = '';
                    $tabAbbr = '';
                    $moduleName = '';
                    $showTabs = 1;
                    $displayRow = '';
                    $tabName = trim($eachPCFileTabs['tabName']);

                    if ($tabName == 'MP') {
                        if ($allowPCUsersToMarketPlace == 0) {
                            $displayRow = " style='display:none;'";
                        }
                    }
                    if ($tabName == 'HR') {
                        if ($subscribePCToHOME == 0) {
                            $displayRow = " style='display:none;'";
                        }
                    }
                    if ($tabName == 'AW') {
                        if ($isPCActiveAloware == 0) {
                            $displayRow = " style='display:none;'";
                        }
                    }
                    if ($tabName == 'PE') {
                        if ($allowPeerstreet == 0) {
                            $displayRow = " style='display:none;'";
                        }
                    }
                    if ($tabName == 'SER2') {
                        if ($allowServicing == 0) {
                            $displayRow = " style='display:none;'";
                        }
                    }
                    $PCFOID = trim($eachPCFileTabs['PFTID']);
                    $tabAbbr = trim($eachPCFileTabs['tabAbbr']);
                    $moduleName = trim($eachPCFileTabs['moduleName']);
                    $showTabs = trim($eachPCFileTabs['showTabs']);

                    /* if($tabAbbr == 'Loan Terms' && $assignedPCID != '2057'){
                         $moduleName = 'Biz Funding';
                     }*/
                    if ($tabName == 'CI') {
                        if (in_array('LM', array_column($modulesArray, 'moduleCode')) ||
                            in_array('SS', array_column($modulesArray, 'moduleCode'))) {
                            $tabAbbr = trim($eachPCFileTabs['tabAbbr']);
                        } else {
                            $tabAbbr = 'Borrower Info';
                        }
                    }
                    if ($tabName == 'DS' && !in_array($assignedPCID, glCustomJobForProcessingCompany::$glCustomTabDealSizerCommercial)) {
                        $displayRow = " style='display:none;' ";
                    }
                    if ($tabName == 'LT') {
                        if (!glCustomJobForProcessingCompany::showLoanTermsToBusinessFunding($assignedPCID)) {
                            $displayRow = " style='display:none;' ";
                        }
                    }
                    if (!glCustomJobForProcessingCompany::showLoanInfoV2($assignedPCID) && $tabName == 'LIV2') {
                        $displayRow = " style='display:none;' ";
                    }
                    if ($enabledDrawManagementV2 && $tabName == 'BD') {
                        $tabAbbr = "Draw Management";
                    }
                    ?>
                    <tr id="<?php echo $PCFOID ?>_<?php echo $tabName ?>" <?php echo $displayRow; ?>>
                        <td class="font-weight-bold py-3 font-size-h6">
                            <?php echo $tabAbbr ?>
                        </td>
                        <td><?php echo str_replace(',', ', ', $moduleName) ?></td>
                        <td class="text-center" id="<?php if ($tabOrderFor == 'PC') {
                            echo 'showTab_'.$rowCnt;
                        } else {
                            echo 'showTab_Client_'.$rowCnt;
                        } ?>">
                            <?php
                            if ($showTabs > 0) { ?>
                                <span class="fa fa-check text-success tooltipClass"
                                      onclick="listPCFileTabs.showPCFileTabs('<?php echo cypher::myEncryption($tabName) ?>', 0, '<?php echo $rowCnt ?>',
                                              '<?php echo cypher::myEncryption($assignedPCID); ?>','<?php echo cypher::myEncryption($tabOrderFor); ?>');"
                                      title="Click to hide Tab">
                                </span>
                                <?php
                            } else { ?>
                                <span class="fa fa-times text-danger tooltipClass"
                                      onclick="listPCFileTabs.showPCFileTabs('<?php echo cypher::myEncryption($tabName) ?>', 1, '<?php echo $rowCnt ?>',
                                              '<?php echo cypher::myEncryption($assignedPCID); ?>','<?php echo cypher::myEncryption($tabOrderFor); ?>');"
                                      title="Click to show Tab">
                                </span>
                                <?php
                            }
                            ?>
                        </td>
                    </tr>
                    <?php
                    $rowCnt++;
                }
                ?>
                </tbody>
            </table>
        </div> <!-- Module Div END -->
    </div>
</div>
<input type="hidden" name="totalRowCount" id="totalRowCount" value="<?php echo $rowCnt ?>">

