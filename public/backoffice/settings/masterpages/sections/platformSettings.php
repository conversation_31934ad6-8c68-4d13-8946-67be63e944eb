<?php

use models\composite\oPC\getPCModules;
use models\constants\accessSecondaryWFPC;
use models\constants\gl\glPackageCategoryArray;
use models\cypher;
use models\PageVariables;
use models\portals\BackofficeSettingsPage;

$isPCActive = PageVariables::$isPCActive;

$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;

$fileModules = [];

if (PageVariables::$PCID > 0) {
    $modulesArray = getPCModules::getReportParams(PageVariables::$PCID);

    foreach($modulesArray as $item) {
        $fileModules[] = $item->moduleCode;
    }

}

foreach (glPackageCategoryArray::$glPackageCategoryArray as $item) {
    $fileModules[] = $item;
}


$encPCID = cypher::myEncryption(PageVariables::$PCID);
$links = [
    1 => [
        'name' => 'Company Info',
        'onclick' => 'showTabUrl(1, \'' . $encPCID . '\')',
        'show' => true,
    ],
    2 => [
        'name' => 'Doc Library',
        'onclick' => 'showTabUrl(2, \'' . $encPCID . '\')',
        'show' => PageVariables::$PCID > 0,
    ],
    3 => [
        'name' => 'Activate',
        'onclick' => 'showTabUrl(3, \'' . $encPCID . '\')',
        'show' => (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Sales') && PageVariables::$PCID > 0,
    ],
    4 => [
        'name' => 'Required Docs',
        'onclick' => 'showTabUrl(4, \'' . $encPCID . '\')',
        'show' => (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Sales' || (PageVariables::$userRole == 'Manager' && $isPCActive)) && PageVariables::$PCID > 0,
    ],
    5 => [
        'name' => 'Workflow',
        'onclick' => 'showTabUrl(5, \'' . $encPCID . '\')',
        'show' => (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Sales' || (PageVariables::$userRole == 'Manager' && $isPCActive)) && PageVariables::$PCID > 0,
    ],
    9 => [
        'name' => 'Workflow',
        'onclick' => 'showTabUrl(9, \'' . $encPCID . '\')',
        'show' => in_array(PageVariables::$PCID, $accessSecondaryWFPC) && (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Sales' || (PageVariables::$userRole == 'Manager' && $isPCActive)) && PageVariables::$PCID > 0,
    ],
    6 => [
        'name' => 'File Status',
        'onclick' => 'window.location=\'/backoffice/settings/company/file_status\'',
        'show' => (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Sales' || (PageVariables::$userRole == 'Manager' && $isPCActive)) && PageVariables::$PCID > 0,
    ],
    7 => [
        'name' => 'File Sub-status',
        'onclick' => 'showTabUrl(7, \'' . $encPCID . '\')',
        'show' => (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Sales' || (PageVariables::$userRole == 'Manager' && $isPCActive)) && PageVariables::$PCID > 0,
    ],
    11 => [
        'name' => 'Web Forms/ Logins',
        'onclick' => 'showTabUrl(11, \'' . $encPCID . '\')',
        'show' => PageVariables::$PCID > 0,
    ],
    13 => [
        'name' => 'Custom Loan Guidelines',
        'onclick' => 'showTabUrl(13, \'' . $encPCID . '\')',
        'show' => (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Sales' || (PageVariables::$userRole == 'Manager' && $isPCActive)) && PageVariables::$PCID > 0 && (in_array('HMLO', $fileModules) || in_array('loc', $fileModules)),
    ],
    14 => [
        'name' => 'File Tab Display Order',
        'onclick' => 'showTabUrl(14, \'' . $encPCID . '\')',
        'show' => PageVariables::$PCID > 0,
    ],
    16 => [
        'name' => 'Form Fields',
        'onclick' => 'showTabUrl(16, \'' . $encPCID . '\')',
        'show' => PageVariables::$PCID > 0 && (PageVariables::$userRole == 'Super' || (PageVariables::$userRole == 'Manager' && $isPCActive)),
    ],
    17 => [
        'name' => 'Form Fields V2',
        'onclick' => 'showTabUrl(17, \'' . $encPCID . '\')',
        'show' => PageVariables::$PCID > 0 && (stristr(PageVariables::$userEmail, 'lendingwise.com') !== false || PageVariables::$userRole == 'Super'),
    ],
    18 => [
        'name' => 'Custom Form Fields',
        'onclick' => 'window.location=\'/backoffice/settings/custom_fields\'',
        'show' => (PageVariables::$PCID > 0 && (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Manager' && $isPCActive)  ),
    ],
    19 => [
        'name' => 'Draw Management',
        'onclick' => 'showTabUrl(22, \'' . $encPCID . '\')',
        'show' => (PageVariables::$PCID > 0 && (PageVariables::$userRole == 'Super' || PageVariables::$userRole == 'Manager' && $isPCActive)  ),
    ],
];
?>


<ul class="nav nav-pills">

    <?php foreach($links as $tab => $settings) { if(!$settings['show']) { continue; } ?>
        <li class="nav-item ml-1 mb-1 mr-0 bg-gray-100 tooltipClass">
            <a class="nav-link px-4 py-2 <?php if (BackofficeSettingsPage::$tabNumber == $tab) {
                echo 'active';
            } else {
                echo '';
            } ?> " href="#" onclick="<?php echo $settings['onclick'] ?>"><?php echo $settings['name'] ?></a>
        </li>
    <?php } ?>
</ul>

<script>
    function showTabUrl(tabNumb, PCID) {
        <?php if (PageVariables::$userRole == 'Super') { ?>
        window.location.href = "/backoffice/createProcessingCompany.php?pcId=" + PCID + "&tabNumb=" + tabNumb;
        <?php } else { ?>
        window.location.href = "/backoffice/createProcessingCompany.php?tabNumb=" + tabNumb;
        <?php } ?>
    }
</script>
