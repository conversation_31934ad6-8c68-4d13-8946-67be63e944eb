<?php

use models\composite\oAcqualify\assignUserToAccount;
use models\composite\oAcqualify\assignUserToBranch;
use models\composite\oAcqualify\createBranch;
use models\composite\oAcqualify\createUser;
use models\composite\oAffiliate\checkAffiliateAEExist;
use models\composite\oAffiliate\checkPromoCodeExist;
use models\composite\oAffiliate\generatePromoCode;
use models\composite\oAffiliate\insertAffiliateInfo;
use models\composite\oBranch\getacqualifyBranchDetails;
use models\composite\oBranch\getPromoCodeForBranch;
use models\composite\oBranch\saveBranchInfo;
use models\composite\oBranch\saveBranchModules;
use models\composite\oBranch\savePCAssociationWithBranch;
use models\composite\oBranch\updateBranchLogo;
use models\composite\oBranch\updateBranchSubscriberID;
use models\composite\oBroker\getAgentIdAssignedToBranch;
use models\composite\oEmployee\getEmployeeIdAssignedToBranch;
use models\composite\oEmployee\saveEmployeeBranch;
use models\composite\oPC\deleteUserServerInfo;
use models\composite\oPC\getacqualifyPCDetails;
use models\composite\oPC\getacqualifyUserDetails;
use models\composite\oPC\postacQualifylog;
use models\composite\oPC\saveUserServerInfo;
use models\composite\oUser\avatar;
use models\constants\accessSecondaryWFPC;
use models\constants\gl\glMimeTypes;
use models\constants\gl\globalWithUSTimeZones;
use models\Database2;
use models\cypher;
use models\FileStorage;
use models\PageVariables;
use models\Request;
use models\standard\Arrays;
use models\standard\Dates;
use models\standard\HTTP;
use models\standard\Strings;
use models\standard\UserAccess;

session_start();
require '../includes/util.php';

UserAccess::checkReferrerPgs(['url' => 'createBranch.php, branchRegistration.php']);

require 'initPageVariables.php';
require 'getPageVariables.php';

global $isPLO, $glBranchAELoginUrl, $userGroup, $PCID, $allowEmpToCreateBranch,
       $convertNewBRIntoEmpOwnBR, $PCTimeZone;


$isJSON = HTTP::isJSONRequest();

$accessSecondaryWFPC = accessSecondaryWFPC::$accessSecondaryWFPC;
$globalWithUSTimeZones = globalWithUSTimeZones::$globalWithUSTimeZones;

$isPublicWebform = 0;
if (isset($_POST['isPublicWebform'])) $isPublicWebform = trim($_POST['isPublicWebform']);

if ($isPublicWebform == 0) {
    UserAccess::CheckAdminUse();
}

$AcUserRoles = [];
$lwUserRolesAgent = [];
$AcUserRolesAgent = [];
$acUserId = null;
$executiveId = 0;
$LMRExecutive = $LMRLastName = '';
$LMRFirstName = '';
$company = '';
$pwd = '';
$email = '';
$AEPartnerCode = 1;
$promoCode = 0;
$opt = false;
$LMRBccEmail = '';
$LMRCcEmail = '';
$tollFree1 = '';
$tollFree2 = '';
$tollFree3 = '';
$tollFreeExt = '';
$fax1 = '';
$fax2 = '';
$fax3 = '';
$website = '';
$tollFree = '';
$fax = '';
$signTollFree1 = '';
$signTollFree2 = '';
$signTollFree3 = '';
$signFax1 = '';
$signFax2 = '';
$signFax3 = '';
$signCompany = '';
$signTollFree = '';
$signFax = '';
$signWebsite = '';
$file_type = '';
$logo = '';
$cellNo1 = '';
$cellNo2 = '';
$cellNo3 = '';
$cellNumber = '';
$signCellNo1 = '';
$signCellNo2 = '';
$signCellNo3 = '';
$userType = '';
$subscribedOption = 1;
$signCellNumber = '';
$state = '';
$address1 = '';
$city = '';
$appStates = '';
$zipCode = '';
$paymentToCompany = '';
$rescissionPhNo1 = '';
$rescissionPhNo2 = '';
$rescissionPhNo3 = '';
$rescissionPhNo = '';
$processingCompanyId = 0;
$adminAllow = false;
$packageIds = '';
$tcUrl = '';
$paymentGateway = 'Authorize.net';
$pkgIdArray = [];
$allowToCreateBranch = 0;
$creditCardTypeArray = [];
$selectedCreditCardType = '';
$PLOHearAbout = 0;
$allowLMRAEToEditFile = 0;
$askReferralAgent = 0;
$branchAELoginUrl = '';
$allowLMRAEToAccessDocs = 1;
$allowLMRToOnOffAgentLogin = 0;
$askPaymentBeforeLMR = 1;
$allowLMRAEToEditCommission = 0;
$PBID = 0;
$showPoweredByTMPLink = 1;
$serviceProvider = '';
$allowAddOn = 0;
$timeZone = '';
$subscriberID = '';
$allowToUpdateFileAdminSection = 0;
$sendMarketingEmail = 1;
$branchModuleTypeArray = [];
$allowClientToUploadDocs = 0;
$displayLinkToFICO = 1;
$allowBranchToLogin = 0;
$allowLMRToEditAgentProfile = 0;
$phone1 = '';
$phone2 = '';
$phone3 = '';
$phoneExt = '';
$phone = '';
$allowToAddAgent = 0;
$useMyServerSetting = 0;
$hostName = '';
$userName = '';
$SMTPPwd = '';
$portNo = '';
$replyTo = '';
$bounceMail = '';
$ESID = 0;
$myServerInfoArray = [];
$allowBranchToCreateFiles = 1;
$allowBranchToCreateTasks = 1;
$changeDIYPlan = 0;
$allowBranchToSeeDashboard = 1;
$AllowToCreateAloware = 0;
$seePrivate = 0;
$accessRestriction = 0;
$allowedToDeleteUplodedDocs = 1;
$allowedToEditOwnNotes = 1;
$permissionToREST = 1;
$allowedToExcelReport = 0;
$userRole = '';
$resultArray = [];
$globalAcctNo = '';
$sponsorName = '';
$isPrimary = 0;
$resArray = [];
$allowToLASubmit = $allowWorkflowEdit = 0;
$allowToCFPBSubmit = 0;
$allowToViewCFPBPipeline = 0;
$allowAgentToSeeFile = '';
$subscribeToHOME = 0;
$allowBranchToGetBorrowerUploadDocsNotification = 1;
$allowToViewMarketPlace = 1;
$allowEmailCampaign = 1;
$allowToSendFax = 0;
if ($isPLO == 1) $allowToSendHomeownerLink = 1; else $allowToSendHomeownerLink = 0;
$allowBranchToSeeCommission = 1;
$allowClientToAccessDocs = 0;
$btnValue = '';
$allowEditToIR = 0;
$allowToSendFileDesignation = 1;
$allowBranchToSeePublicNotes = 1;
$WFIDsArray = [];
$mailingAddress = '';
$mailingCity = '';
$mailingState = '';
$mailingZipCode = '';
$bankName = '';
$routingNumber = '';
$accountNumber = '';
$county = '';
$allowToAccessRAM = 0;
$allowBranchManagerToLogin = $allowtoEditCCInfo = 0;
$allowToLockLoanFileBranch = 1;
$alowareErr = '';
$allowcaptcha = 0; //captcha feature disabled by default
$shareThisFile = 0;
$allowToSubmitOffer = 0;
$allowToViewCreditScreening = 0;
$enable2FAAuthentication = 0;
$TwoFAType = 'email';
$userPriceEngineStatus = 0;
$loanpassLogin = '';
$loanpassPassword = '';
$notifyBODocUpload = 0;
$notifyLODocUpload = 0;
$notifyBrokerDocUpload = 0;
$notifyDocUploadRequest = 0;
$notifyNewFileCreated = 0;
$allowToAccessInternalLoanProgram = 0;
$allowToEditLoanStage = 0;

$DocUploadBcc = '';
$DocUploadCc = '';

if (isset($_POST['subscribed'])) $subscribedOption = trim($_POST['subscribed']);
if (isset($_POST['eId'])) $executiveId = trim($_POST['eId']);
if (isset($_POST['email'])) $email = trim($_POST['email']);
if (isset($_POST['LMRName'])) $LMRExecutive = trim($_POST['LMRName']);
if (isset($_POST['LMRFirstName'])) $LMRFirstName = trim($_POST['LMRFirstName']);
if (isset($_POST['LMRLastName'])) $LMRLastName = trim($_POST['LMRLastName']);
if (isset($_POST['company'])) $company = trim($_POST['company']);
if (isset($_POST['pwd'])) $pwd = trim($_POST['pwd']);
if (isset($_POST['LMRBccEmail'])) $LMRBccEmail = trim($_POST['LMRBccEmail']);
if (isset($_POST['LMRCcEmail'])) $LMRCcEmail = trim($_POST['LMRCcEmail']);
if (isset($_POST['tollFree'])) $tollFree = trim($_POST['tollFree']);
//if (isset($_POST['tollFree1'])) $tollFree1 = trim($_POST['tollFree1']);
//if (isset($_POST['tollFree2'])) $tollFree2 = trim($_POST['tollFree2']);
//if (isset($_POST['tollFree3'])) $tollFree3 = trim($_POST['tollFree3']);
//if (isset($_POST['tollFreeExt'])) $tollFreeExt = trim($_POST['tollFreeExt']);
if (isset($_POST['fax'])) $fax = trim($_POST['fax']);
//if (isset($_POST['fax2'])) $fax2 = trim($_POST['fax2']);
//if (isset($_POST['fax3'])) $fax3 = trim($_POST['fax3']);
if (isset($_POST['website'])) $website = trim($_POST['website']);
if (isset($_POST['userType'])) $userType = trim($_POST['userType']);
if (isset($_POST['signCompany'])) $signCompany = trim($_POST['signCompany']);
if (isset($_POST['signTollFree1'])) $signTollFree1 = trim($_POST['signTollFree1']);
if (isset($_POST['signTollFree2'])) $signTollFree2 = trim($_POST['signTollFree2']);
if (isset($_POST['signTollFree3'])) $signTollFree3 = trim($_POST['signTollFree3']);
if (isset($_POST['signFax1'])) $signFax1 = trim($_POST['signFax1']);
if (isset($_POST['signFax2'])) $signFax2 = trim($_POST['signFax2']);
if (isset($_POST['signFax3'])) $signFax3 = trim($_POST['signFax3']);
if (isset($_POST['signWebsite'])) $signWebsite = trim($_POST['signWebsite']);
if (isset($_POST['branchAELoginUrl'])) $branchAELoginUrl = trim($_POST['branchAELoginUrl']);
if (isset($_POST['globalAcctNo'])) $globalAcctNo = trim($_POST['globalAcctNo']);
if (isset($_POST['sponsorName'])) $sponsorName = trim($_POST['sponsorName']);

if ($branchAELoginUrl == '') {
    $branchAELoginUrl = $glBranchAELoginUrl;
}

if (isset($_REQUEST['saveLMR'])) {
    $btnValue = trim($_REQUEST['saveLMR']);
}

if (isset($_POST['cellNumber'])) $cellNumber = trim($_POST['cellNumber']);
//if (isset($_POST['cellNo2'])) $cellNo2 = trim($_POST['cellNo2']);
//if (isset($_POST['cellNo3'])) $cellNo3 = trim($_POST['cellNo3']);
if (isset($_POST['signCellNo1'])) $signCellNo1 = trim($_POST['signCellNo1']);
if (isset($_POST['signCellNo2'])) $signCellNo2 = trim($_POST['signCellNo2']);
if (isset($_POST['signCellNo3'])) $signCellNo3 = trim($_POST['signCellNo3']);
if (isset($_POST['address1'])) $address1 = trim($_POST['address1']);
if (isset($_POST['city'])) $city = trim($_POST['city']);
if (isset($_POST['state'])) $state = trim($_POST['state']);
if (isset($_POST['zipCode'])) $zipCode = trim($_POST['zipCode']);
if (isset($_POST['paymentToCompany'])) $paymentToCompany = trim($_POST['paymentToCompany']);
if (isset($_POST['rescissionPhNo1'])) $rescissionPhNo1 = trim($_POST['rescissionPhNo1']);
if (isset($_POST['rescissionPhNo2'])) $rescissionPhNo2 = trim($_POST['rescissionPhNo2']);
if (isset($_POST['rescissionPhNo3'])) $rescissionPhNo3 = trim($_POST['rescissionPhNo3']);
if (isset($_POST['assignedToProcessingCompany'])) $processingCompanyId = trim($_POST['assignedToProcessingCompany']);
if (isset($_POST['allowToCreateBranch'])) $allowToCreateBranch = trim($_POST['allowToCreateBranch']);
if (isset($_POST['allowLMRAEToEditFile'])) $allowLMRAEToEditFile = trim($_POST['allowLMRAEToEditFile']);
if (isset($_POST['allowAgentToSeeFile'])) $allowAgentToSeeFile = trim($_POST['allowAgentToSeeFile']);

if (isset($_POST['allowLMRAEToAccessDocs'])) $allowLMRAEToAccessDocs = trim($_POST['allowLMRAEToAccessDocs']);
if (isset($_POST['allowLMRToOnOffAgentLogin'])) $allowLMRToOnOffAgentLogin = trim($_POST['allowLMRToOnOffAgentLogin']);
if (isset($_POST['allowLMRAEToEditCommission'])) $allowLMRAEToEditCommission = trim($_POST['allowLMRAEToEditCommission']);
if (isset($_POST['PBID'])) $PBID = trim($_POST['PBID']);
if (isset($_POST['serviceProvider'])) $serviceProvider = trim($_POST['serviceProvider']);
if (isset($_POST['allowAddOn'])) $allowAddOn = trim($_POST['allowAddOn']);
if (isset($_POST['timeZone'])) $timeZone = trim($_POST['timeZone']);
if (isset($_POST['subscriberID'])) $subscriberID = trim($_POST['subscriberID']);
if (isset($_POST['allowToUpdateFileAdminSection'])) $allowToUpdateFileAdminSection = trim($_POST['allowToUpdateFileAdminSection']);
if (isset($_POST['sendMarketingEmail'])) $sendMarketingEmail = trim($_POST['sendMarketingEmail']);
if (isset($_POST['branchModuleType'])) {
    if (is_array($_POST['branchModuleType'])) {
        $branchModuleTypeArray = $_POST['branchModuleType'];
    }
}
if (isset($_POST['displayLinkToFICO'])) $displayLinkToFICO = trim($_POST['displayLinkToFICO']);
if (isset($_POST['allowBranchToLogin'])) $allowBranchToLogin = trim($_POST['allowBranchToLogin']);
if (isset($_POST['allowLMRToEditAgentProfile'])) $allowLMRToEditAgentProfile = trim($_POST['allowLMRToEditAgentProfile']);
if (isset($_POST['phone'])) $phone = trim($_POST['phone']);
//if (isset($_POST['phone2'])) $phone2 = trim($_POST['phone2']);
//if (isset($_POST['phone3'])) $phone3 = trim($_POST['phone3']);
//if (isset($_POST['phoneExt'])) $phoneExt = trim($_POST['phoneExt']);
if (isset($_POST['allowToAddAgent'])) $allowToAddAgent = trim($_POST['allowToAddAgent']);
if (isset($_POST['useMyServerSetting'])) $useMyServerSetting = trim($_POST['useMyServerSetting']);
if (isset($_POST['hostName'])) $hostName = trim($_POST['hostName']);
if (isset($_POST['userName'])) $userName = trim($_POST['userName']);
if (isset($_POST['SMTPPwd'])) $SMTPPwd = trim($_POST['SMTPPwd']);
if (isset($_POST['portNo'])) $portNo = trim($_POST['portNo']);
if (isset($_POST['replyTo'])) $replyTo = trim($_POST['replyTo']);
if (isset($_POST['bounceMail'])) $bounceMail = trim($_POST['bounceMail']);
if (isset($_POST['ESID'])) $ESID = trim($_POST['ESID']);
if (isset($_POST['allowBranchToCreateFiles'])) $allowBranchToCreateFiles = trim($_POST['allowBranchToCreateFiles']);
if (isset($_POST['allowBranchToCreateTasks'])) $allowBranchToCreateTasks = trim($_POST['allowBranchToCreateTasks']);
if (isset($_POST['allowBranchToSeeDashboard'])) $allowBranchToSeeDashboard = trim($_POST['allowBranchToSeeDashboard']);
if (isset($_POST['accessRestriction'])) $accessRestriction = trim($_POST['accessRestriction']);
if (isset($_POST['AllowToCreateAloware'])) $AllowToCreateAloware = trim($_POST['AllowToCreateAloware']);
if (isset($_POST['seePrivate'])) $seePrivate = trim($_POST['seePrivate']);
if (isset($_POST['allowedToDeleteUplodedDocs'])) $allowedToDeleteUplodedDocs = trim($_POST['allowedToDeleteUplodedDocs']);
if (isset($_POST['allowedToEditOwnNotes'])) $allowedToEditOwnNotes = trim($_POST['allowedToEditOwnNotes']);
if (isset($_POST['allowtoEditCCInfo'])) $allowtoEditCCInfo = trim($_POST['allowtoEditCCInfo']);
if (isset($_POST['allowEmailCampaign'])) $allowEmailCampaign = trim($_POST['allowEmailCampaign']);

if (isset($_POST['permissionToREST'])) $permissionToREST = trim($_POST['permissionToREST']);
if (isset($_POST['subscribeToHOME'])) $subscribeToHOME = trim($_POST['subscribeToHOME']);
if (isset($_POST['allowToSendFax'])) $allowToSendFax = trim($_POST['allowToSendFax']);

if (isset($_POST['allowedToExcelReport'])) $allowedToExcelReport = trim($_POST['allowedToExcelReport']);
if (isset($_POST['changeDIYPlan'])) $changeDIYPlan = trim($_POST['changeDIYPlan']);
if (isset($_POST['userRole'])) $userRole = trim($_POST['userRole']);
if (isset($_POST['allowToSendHomeownerLink'])) $allowToSendHomeownerLink = trim($_POST['allowToSendHomeownerLink']);
if (isset($_POST['isPrimary'])) $isPrimary = trim($_POST['isPrimary']);
if (isset($_POST['allowToLASubmit'])) {
    $allowToLASubmit = trim($_POST['allowToLASubmit']);
}
if (isset($_POST['allowToCFPBSubmit'])) {
    $allowToCFPBSubmit = trim($_POST['allowToCFPBSubmit']);
}
if (isset($_POST['allowBranchToSeeCommission'])) {
    $allowBranchToSeeCommission = trim($_POST['allowBranchToSeeCommission']);
}
if (isset($_POST['allowClientToAccessDocs'])) {
    $allowClientToAccessDocs = trim($_POST['allowClientToAccessDocs']);
}
if (isset($_POST['allowEditToIR'])) {
    $allowEditToIR = trim($_POST['allowEditToIR']);
}
if (isset($_POST['allowToSendFileDesignation'])) {
    $allowToSendFileDesignation = trim($_POST['allowToSendFileDesignation']);
}
if (isset($_POST['allowBranchToSeePublicNotes'])) {
    $allowBranchToSeePublicNotes = trim($_POST['allowBranchToSeePublicNotes']);
}
if (isset($_POST['WFID'])) {
    $WFIDsArray = $_POST['WFID'];
}
if (isset($_POST['mailingAddress'])) {
    $mailingAddress = $_POST['mailingAddress'];
}
if (isset($_POST['mailingCity'])) {
    $mailingCity = $_POST['mailingCity'];
}
if (isset($_POST['mailingState'])) {
    $mailingState = $_POST['mailingState'];
}
if (isset($_POST['mailingZipCode'])) {
    $mailingZipCode = $_POST['mailingZipCode'];
}

if (isset($_POST['bankName'])) {
    $bankName = $_POST['bankName'];
}
if (isset($_POST['routingNumber'])) {
    $routingNumber = $_POST['routingNumber'];
}
if (isset($_POST['accountNumber'])) {
    $accountNumber = $_POST['accountNumber'];
}
if (isset($_POST['county'])) {
    $county = $_POST['county'];
}
if (isset($_POST['allowToViewCFPBPipeline'])) {
    $allowToViewCFPBPipeline = $_POST['allowToViewCFPBPipeline'];
}
if (isset($_POST['allowBranchToAccessRAM'])) {
    $allowToAccessRAM = $_POST['allowBranchToAccessRAM'];
}
if (isset($_POST['allowBranchManagerToLogin'])) {
    $allowBranchManagerToLogin = $_POST['allowBranchManagerToLogin'];
}
if (isset($_POST['allowWorkflowEdit'])) {
    $allowWorkflowEdit = $_POST['allowWorkflowEdit'];
}
if (isset($_POST['allowToLockLoanFileBranch'])) {
    $allowToLockLoanFileBranch = $_POST['allowToLockLoanFileBranch'];
}
if (isset($_POST['allowBranchToGetBorrowerUploadDocsNotification'])) {
    $allowBranchToGetBorrowerUploadDocsNotification = $_POST['allowBranchToGetBorrowerUploadDocsNotification'];
}
if (isset($_POST['allowToViewMarketPlace'])) {
    $allowToViewMarketPlace = $_POST['allowToViewMarketPlace'];
}
if (isset($_POST['allowcaptcha'])) {
    $allowcaptcha = $_POST['allowcaptcha']; // get value from form(UI).
}
if (isset($_POST['shareThisFile'])) {
    $shareThisFile = $_POST['shareThisFile']; // get value from form(UI).
}
if (isset($_POST['allowToSubmitOffer'])) {
    $allowToSubmitOffer = $_POST['allowToSubmitOffer']; // get value from form(UI).
}
if (isset($_POST['allowToViewCreditScreening'])) {
    $allowToViewCreditScreening = $_POST['allowToViewCreditScreening']; // get value from form(UI).
}
if (isset($_POST['enable2FAAuthentication'])) {
    $enable2FAAuthentication = $_POST['enable2FAAuthentication']; // get value from form(UI).
}
if (isset($_POST['userPriceEngineStatus'])) {
    $userPriceEngineStatus = $_POST['userPriceEngineStatus']; // get value from form(UI).
}
if (isset($_POST['TwoFAType'])) {
    $TwoFAType = $_POST['TwoFAType']; // get value from form(UI).
}
if (isset($_POST['allowToEditLoanStage'])) {
    $allowToEditLoanStage = $_POST['allowToEditLoanStage'];
}

$processingCompanyId = trim($processingCompanyId);
if ($processingCompanyId == '') $processingCompanyId = 0;
if (trim($allowLMRAEToEditFile) == '') $allowLMRAEToEditFile = 0;
if (trim($allowToUpdateFileAdminSection) == '') $allowToUpdateFileAdminSection = 0;

//$tollFree = $tollFree1 . $tollFree2 . $tollFree3 . $tollFreeExt;
//$fax = $fax1 . $fax2 . $fax3;
//$cellNumber = $cellNo1 . $cellNo2 . $cellNo3;
$signTollFree = $signTollFree1 . $signTollFree2 . $signTollFree3;
$signFax = $signFax1 . $signFax2 . $signFax3;
$signCellNumber = $signCellNo1 . $signCellNo2 . $signCellNo3;
$rescissionPhNo = $rescissionPhNo1 . $rescissionPhNo2 . $rescissionPhNo3;

//$phone = $phone1 . $phone2 . $phone3 . $phoneExt;
$phone = str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], $phone);
$tollFree = str_replace(['(', ')', '-', '_', ' ', 'Ext'], [''], $tollFree);

if (!$pwd) {
    $pwd = $LMRExecutive;
}


$infoArray = [
    'executiveId'                                    => $executiveId,
    'email'                                          => $email,
    'LMRExecutive'                                   => $LMRExecutive,
    'LMRFirstName'                                   => $LMRFirstName,
    'LMRLastName'                                    => $LMRLastName,
    'company'                                        => $company,
    'pwd'                                            => $pwd,
    'LMRBccEmail'                                    => $LMRBccEmail,
    'LMRCcEmail'                                     => $LMRCcEmail,
    'tollFree'                                       => $tollFree,
    'fax'                                            => $fax,
    'website'                                        => $website,
    'userType'                                       => $userType,
    'signCompany'                                    => $signCompany,
    'signTollFree'                                   => $signTollFree,
    'signFax'                                        => $signFax,
    'signWebsite'                                    => $signWebsite,
    'branchAELoginUrl'                               => $branchAELoginUrl,
    'cellNumber'                                     => $cellNumber,
    'signCellNumber'                                 => $signCellNumber,
    'address1'                                       => $address1,
    'city'                                           => $city,
    'state'                                          => $state,
    'zipCode'                                        => $zipCode,
    'paymentToCompany'                               => $paymentToCompany,
    'rescissionPhNo'                                 => $rescissionPhNo,
    'processingCompanyId'                            => $processingCompanyId,
    'userRole'                                       => $userRole,
    'allowToCreateBranch'                            => $allowToCreateBranch,
    'allowLMRAEToEditFile'                           => $allowLMRAEToEditFile,
    'allowAgentToSeeFile'                            => $allowAgentToSeeFile == '' ? 1 : $allowAgentToSeeFile,
    'allowLMRAEToAccessDocs'                         => $allowLMRAEToAccessDocs,
    'allowLMRToOnOffAgentLogin'                      => $allowLMRToOnOffAgentLogin,
    'subscribedOption'                               => $subscribedOption,
    'allowLMRAEToEditCommission'                     => $allowLMRAEToEditCommission,
    'serviceProvider'                                => $serviceProvider,
    'allowAddOn'                                     => $allowAddOn,
    'timeZone'                                       => $timeZone,
    'allowToUpdateFileAdminSection'                  => $allowToUpdateFileAdminSection,
    'displayLinkToFICO'                              => $displayLinkToFICO,
    'allowLMRToEditAgentProfile'                     => $allowLMRToEditAgentProfile,
    'phone'                                          => $phone,
    'allowToAddAgent'                                => $allowToAddAgent,
    'useMyServerSetting'                             => $useMyServerSetting,
    'allowBranchToCreateFiles'                       => $allowBranchToCreateFiles,
    'allowBranchToCreateTasks'                       => $allowBranchToCreateTasks,
    'allowBranchToSeeDashboard'                      => $allowBranchToSeeDashboard,
    'AllowToCreateAloware'                           => $AllowToCreateAloware,
    'seePrivate'                                     => $seePrivate,
    'allowedToDeleteUplodedDocs'                     => $allowedToDeleteUplodedDocs,
    'allowedToEditOwnNotes'                          => $allowedToEditOwnNotes,
    'allowtoEditCCInfo'                              => $allowtoEditCCInfo,
    'allowEmailCampaign'                             => $allowEmailCampaign,
    'permissionToREST'                               => $permissionToREST,
    'subscribeToHOME'                                => $subscribeToHOME,
    'allowedToExcelReport'                           => $allowedToExcelReport,
    'changeDIYPlan'                                  => $changeDIYPlan,
    'allowToSendHomeownerLink'                       => $allowToSendHomeownerLink,
    'isPrimary'                                      => $isPrimary,
    'allowToSendFax'                                 => $allowToSendFax,
    'userNumber'                                     => PageVariables::$userNumber,
    'userGroup'                                      => $userGroup, 'allowWorkflowEdit' => $allowWorkflowEdit,
    'allowBranchToGetBorrowerUploadDocsNotification' => $allowBranchToGetBorrowerUploadDocsNotification,
    'allowToViewMarketPlace'                         => $allowToViewMarketPlace,
    'allowcaptcha'                                   => $allowcaptcha,
    'shareThisFile'                                  => $shareThisFile,
    'allowToSubmitOffer'                             => $allowToSubmitOffer,
    'allowToViewCreditScreening'                     => $allowToViewCreditScreening,
    'enable2FAAuthentication'                        => $enable2FAAuthentication,
    'TwoFAType'                                      => $TwoFAType,
    'userPriceEngineStatus'                          => $userPriceEngineStatus,
    'county'                                         => $county,
    'allowEditToIR'                                  => $allowEditToIR,
    'allowToLockLoanFileBranch'                      => $allowToLockLoanFileBranch,
    'allowToEditLoanStage'                           => $allowToEditLoanStage
];
if ($userType == 'PLO') {
    if (isset($_POST['PLOHearAbout'])) $PLOHearAbout = trim($_POST['PLOHearAbout']);
    if (isset($_POST['askReferralAgent'])) $askReferralAgent = trim($_POST['askReferralAgent']);
    if (isset($_POST['askPaymentBeforeLMR'])) $askPaymentBeforeLMR = trim($_POST['askPaymentBeforeLMR']);
    if (isset($_POST['allowClientToUploadDocs'])) $allowClientToUploadDocs = trim($_POST['allowClientToUploadDocs']);

    if ($PLOHearAbout == '') $PLOHearAbout = 0;
    if ($askReferralAgent == '') $askReferralAgent = 0;
    if ($askPaymentBeforeLMR == '') $askPaymentBeforeLMR = 0;
    if ($allowClientToUploadDocs == '') $allowClientToUploadDocs = 0;
    $infoArray['PLOHearAbout'] = $PLOHearAbout;
    $infoArray['askReferralAgent'] = $askReferralAgent;
    $infoArray['askPaymentBeforeLMR'] = $askPaymentBeforeLMR;
    $infoArray['allowClientToUploadDocs'] = $allowClientToUploadDocs;
}
if (isset($_POST['allowBranchToLogin'])) $infoArray['allowBranchToLogin'] = $allowBranchToLogin;
if (isset($_POST['allowBranchToSeeCommission'])) $infoArray['allowBranchToSeeCommission'] = $allowBranchToSeeCommission;
if (isset($_POST['allowToLASubmit'])) {
    $infoArray['allowToLASubmit'] = $allowToLASubmit;
}
if (isset($_POST['allowToCFPBSubmit'])) {
    $infoArray['allowToCFPBSubmit'] = $allowToCFPBSubmit;
}
if (isset($_POST['allowClientToAccessDocs'])) {
    $infoArray['allowClientToAccessDocs'] = $allowClientToAccessDocs;
}
if (isset($_POST['allowToSendFileDesignation'])) {
    $infoArray['allowToSendFileDesignation'] = $allowToSendFileDesignation;
}
if (isset($_POST['allowBranchToSeePublicNotes'])) {
    $infoArray['allowBranchToSeePublicNotes'] = $allowBranchToSeePublicNotes;
}
if (isset($_POST['allowToViewCFPBPipeline'])) {
    $infoArray['allowToViewCFPBPipeline'] = $allowToViewCFPBPipeline;
}
if (isset($_POST['allowBranchToAccessRAM'])) {
    $infoArray['allowToAccessRAM'] = $allowToAccessRAM;
}
if (isset($_POST['allowBranchManagerToLogin'])) {
    $infoArray['allowBranchManagerToLogin'] = $allowBranchManagerToLogin;
}

if (isset($_POST['notifyBODocUpload'])) {
    $notifyBODocUpload = $_POST['notifyBODocUpload'];
    $infoArray['notifyBODocUpload'] = $notifyBODocUpload;
}
if (isset($_POST['notifyLODocUpload'])) {
    $notifyLODocUpload = $_POST['notifyLODocUpload'];
    $infoArray['notifyLODocUpload'] = $notifyLODocUpload;
}
if (isset($_POST['notifyBrokerDocUpload'])) {
    $notifyBrokerDocUpload = $_POST['notifyBrokerDocUpload'];
    $infoArray['notifyBrokerDocUpload'] = $notifyBrokerDocUpload;
}
if (isset($_POST['notifyDocUploadRequest'])) {
    $notifyDocUploadRequest = $_POST['notifyDocUploadRequest'];
    $infoArray['notifyDocUploadRequest'] = $notifyDocUploadRequest;
}
if (isset($_POST['notifyNewFileCreated'])) {
    $notifyNewFileCreated = $_POST['notifyNewFileCreated'];
    $infoArray['notifyNewFileCreated'] = $notifyNewFileCreated;
}
if (isset($_POST['DocUploadBcc'])) {
    $DocUploadBcc = $_POST['DocUploadBcc'];
    $infoArray['DocUploadBcc'] = $DocUploadBcc;
}
if (isset($_POST['DocUploadCc'])) {
    $DocUploadCc = $_POST['DocUploadCc'];
    $infoArray['DocUploadCc'] = $DocUploadCc;
}
if (isset($_POST['loanpassLogin'])) {
    $loanpassLogin = $_POST['loanpassLogin'];
    $infoArray['loanpassLogin'] = $loanpassLogin;
}
if (isset($_POST['loanpassPassword'])) {
    $loanpassPassword = $_POST['loanpassPassword'];
    $infoArray['loanpassPassword'] = $loanpassPassword;
}
if (isset($_POST['allowToAccessInternalLoanProgram'])) {
    $infoArray['allowToAccessInternalLoanProgram'] = $_POST['allowToAccessInternalLoanProgram'];
}
if (isset($_POST['allowToViewAutomationPopup'])) {
    $infoArray['allowToViewAutomationPopup'] = $_POST['allowToViewAutomationPopup'];
}
if (isset($_POST['allowToCopyFile'])) {
    $infoArray['allowToCopyFile'] = $_POST['allowToCopyFile'];
}
$infoArray['allowToMassUpdate'] = Request::GetClean('allowToMassUpdate') ?? null;

if (Request::isset('allowToManageDraws')) {
    $infoArray['allowToManageDraws'] = Request::GetClean('allowToManageDraws') ?? null;
}

/*
* Allow Secondary WF feature for the PCs =
* Dave PC, Enrollment Advisory, Law offices
*/
if (($userRole == 'Manager' || $userRole == 'Administrator' || $userRole == 'Super') && (in_array($processingCompanyId, $accessSecondaryWFPC))) {
    $infoArray['WFIDs'] = $WFIDsArray;
}
if (isset($_GET['sponsor'])) {
//------------------------- 1126=abrams law, 854=mader, 820=daves, 792=resolution law group
    if ($PCID == '1126' || $PCID == '854' || $PCID == '820' || $PCID == '792' || ($_GET['sponsor'] == '1')) {
        $infoArray['globalAcctNo'] = $globalAcctNo;
        $infoArray['sponsorName'] = $sponsorName;
    }
}


if ($executiveId > 0) {
    saveBranchInfo::getReport($infoArray);
} else {
    $ip = ['email' => $email];
    $AEPartnerCode = checkAffiliateAEExist::getReport($ip);
    if ($AEPartnerCode > 1) {
        $promoCode = $AEPartnerCode;
        $referralCode = $AEPartnerCode;
    } else {
        $referralCode = '';
        do {
            $promoCode = generatePromoCode::getReport();
            $ip = ['promoCode' => $promoCode];
            $opt = checkPromoCodeExist::getReport($ip);
        } while ($opt);
        $affiliateInfoArray = [
            'firstName'  => $LMRExecutive,
            'lastName'   => '',
            'company'    => $company,
            'email'      => $email,
            'pwd'        => $pwd,
            'webAddress' => '',
            'promoCode'  => $promoCode,
            'siteName'   => 'TLP',

        ];
        insertAffiliateInfo::getReport($affiliateInfoArray);
    }
    $resultArray = saveBranchInfo::getReport($infoArray);
    if (count($resultArray)) $executiveId = trim($resultArray['executiveId']);

    if ($executiveId > 0 && $userGroup == 'Employee') {
        if ($userRole != 'Manager' && $allowEmpToCreateBranch == 1) {

            saveEmployeeBranch::getReport([
                'BID'             => $executiveId,
                'EID'             => PageVariables::$userNumber,
                'assignBR'        => $convertNewBRIntoEmpOwnBR,
                'isPublicWebform' => $isPublicWebform
            ]);
        }
    }
}


/**
 * Card # :530
 * Description      : Aloware integration
 * Functionality    : User creation process.
 */
if ($AllowToCreateAloware == 1 && $cellNumber > 0) {
    $aloware = new alowareApi();
    $resuser = $aloware->getUserAlowareInfo(['eid' => $executiveId]);
    if (empty($resuser)) {
        $request['internal_company_id'] = $processingCompanyId;
        $request['company_name'] = $company;
        $request['company_country'] = 'US';
        $request['internal_user_id'] = $executiveId;
        $request['user_name'] = $LMRExecutive;
        $request['user_phone_number'] = $cellNumber;
        $request['user_email'] = $email;
        $request['user_timezone'] = Arrays::getArrayValue($PCTimeZone, $globalWithUSTimeZones);
        $creteUserJson = $aloware->creteUser($request);

        $creteUserJsonDecrypt = json_decode($creteUserJson, true);
        if (isset($creteUserJsonDecrypt['api_token'])) {
            $alowareInfo['eid'] = $executiveId;
            $alowareInfo['api_token'] = $creteUserJsonDecrypt['api_token'];
            $alowareinfo = $aloware->saveUserAloware($alowareInfo);
        } else {
            $qryUp = ' UPDATE tblBranch SET AllowToCreateAloware = 0 WHERE executiveId =' . $executiveId;
            Database2::getInstance()->executeQuery($qryUp);
        }
        if (isset($creteUserJsonDecrypt['errors'])) {
            foreach ($creteUserJsonDecrypt['errors'] as $errKey => $errValue) {
                $alowareErr .= $errValue[0] . '<br>';
            }
        }
    }
}
/* Aloware end. */


if ($allowToViewCreditScreening == 1) {
    $pcAcqualifyArray = [];
    $pcAcqualifyArray = ['executiveId' => $executiveId];
    $pcAcqualifyDetails = getacqualifyBranchDetails::getReport($pcAcqualifyArray);

    $acqualifyArray['apiToken'] = '4J2Qn9R0Kp6XenAOj5nUBed55Gsy9Ais';
    $accountIdDetails = getacqualifyPCDetails::getReport(['pcid' => $processingCompanyId]);
    $branchIdArrayAcqualify = [];
    $branchAcqualiftBranchId = 0;


    if (count($accountIdDetails) > 0) {
        $acAccountId = $accountIdDetails[0]['accountId'];

        $acqualifyArray['data'] = ['accountId' => $accountIdDetails[0]['accountId'], 'branchName' => Strings::escapeQuoteNew($LMRExecutive)];
        if (count($pcAcqualifyDetails) > 0) {
            $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/update-branch';
            $branchAcqualiftBranchId = $pcAcqualifyDetails[0]['userId'];

        } else {

            $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/create-branch';

            $acQualifyResponse = createBranch::getReport($acqualifyArray);
            $acQualifyResponse = json_decode($acQualifyResponse);

            if ($acQualifyResponse->status == 'success') {
                if (isset($acQualifyResponse->branch)) {
                    $qry = "
insert into acqualifyBranch (
                             executiveId,accountId,`name`,userId,createdDate, email
                             ) values (
                                       :executiveId
                                       , :accountId
                                       , :name
                                       , :userId
                                       , :createdDate
                                       , ''
                                       );";
                    $acqualifyInsertId = Database2::getInstance()->executeQuery($qry, [
                        'executiveId' => $executiveId,
                        'accountId'   => $acQualifyResponse->branch->accountId,
                        'name'        => $acQualifyResponse->branch->branchName,
                        'userId'      => $acQualifyResponse->branch->id,
                        'createdDate' => Dates::Timestamp(),
                    ]);
                    $branchIdArrayAcqualify = [$acQualifyResponse->branch->id];
                    $branchAcqualiftBranchId = $acQualifyResponse->branch->id;
                }
            }

            $acqualifyPostArray['url'] = $acqualifyArray['url'];
            $acqualifyPostArray['request'] = $acqualifyArray;
            $acqualifyPostArray['response'] = $acQualifyResponse;
            $acqualifyPostArray['pcid'] = $processingCompanyId;
            postacQualifylog::getReport($acqualifyPostArray);


            /*****************/

            $acqualifyArray = [];
            $acqualifyArray['data'] = ['name' => Strings::escapeQuoteNew($LMRExecutive), 'notificationEmail' => $email, 'username' => $executiveId . '+' . $email];
            $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/create-user';

            $acQualifyResponse = createUser::getReport($acqualifyArray);
            $acQualifyResponse = json_decode($acQualifyResponse);

            if ($acQualifyResponse->status == 'success') {
                if (isset($acQualifyResponse->user)) {

                    $qry = "insert into acqualifyUsers (lwUserId,`name`,email,loginKey,userId,createdDate,userRole) values ('" . $executiveId . "','" . $acQualifyResponse->user->name . "','" . $acQualifyResponse->user->email . "','" . $acQualifyResponse->user->loginKey . "','" . $acQualifyResponse->user->id . "','" . Dates::Timestamp() . "','Branch');";

                    $acqualifyInsertId = Database2::getInstance()->insert($qry);
                }
                $acUserId = $acQualifyResponse->user->id;
            }

            $acqualifyPostArray['url'] = $acqualifyArray['url'];
            $acqualifyPostArray['request'] = $acqualifyArray;
            $acqualifyPostArray['response'] = $acQualifyResponse;
            $acqualifyPostArray['pcid'] = $processingCompanyId;
            postacQualifylog::getReport($acqualifyPostArray);

            if ($acUserId > 0) {
                $BRANCHAcqualifyUserId = [];
                $BRANCHAcqualifyArray = [];

                // $acAccountId = $accountIdDetails[0]['accountId'];
                $acqualifyArray['data'] = [
                    'userId'    => $acUserId,
                    'accountId' => $acAccountId,
                    'role'      => 'manager',
                    'branchIds' => [implode(',', $branchIdArrayAcqualify)]
                ];
                $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/assign-user-to-account';

                $acQualifyResponse = assignUserToAccount::getReport($acqualifyArray);
                $acQualifyResponse = json_decode($acQualifyResponse);

                if ($acQualifyResponse->status == 'success') {
                    if (isset($acQualifyResponse->roleInfo)) {

                        $qry = "
update acqualifyUsers set
                          accountId = :accountId
                          , role = :role
                          ,branchIds = :branchIds
                          where  userId = :userId
                          and userRole ='Branch'
                          ";

                        $acqualifyInsertId = Database2::getInstance()->insert($qry, [
                            'accountId' => $acQualifyResponse->roleInfo->accountId,
                            'role'      => $acQualifyResponse->roleInfo->role,
                            'branchIds' => implode(',', $acQualifyResponse->roleInfo->branchIds),
                            'userId'    => $acUserId,
                        ]);
                    }
                    $acUserId = $acQualifyResponse->user->id;
                }
                $acqualifyPostArray['url'] = $acqualifyArray['url'];
                $acqualifyPostArray['request'] = $acqualifyArray;
                $acqualifyPostArray['response'] = $acQualifyResponse;
                $acqualifyPostArray['pcid'] = $processingCompanyId;
                postacQualifylog::getReport($acqualifyPostArray);
            }

        }

        if ($branchAcqualiftBranchId > 0) {
            $branchAssignUsers = [];
            /*$oEmployee = new oEmployee();
            pr($oEmployee->getEmployeeInfo(array("PCID" => $processingCompanyId)));*/
            $AcqualifyUsersArray = [];
            $lwUserRoles = [];
            $AcqualifyUsersArray = ['accountId' => $acAccountId];
            $AcqualifyUsersArray = getacqualifyUserDetails::getReport($AcqualifyUsersArray);
            $AcqualifyUserRoleDetails = Arrays::buildKeyByValue($AcqualifyUsersArray, 'userRole');
            if (isset($AcqualifyUserRoleDetails['Employee'])) {
                $EmpAcqualifyUserRoleDetails = $AcqualifyUserRoleDetails['Employee'];
                if (is_array($EmpAcqualifyUserRoleDetails)) {
                    foreach ($EmpAcqualifyUserRoleDetails as $empAcqualifyUserRoleD) {
                        $lwUserRoles[] = $empAcqualifyUserRoleD['lwUserId'];
                        $AcUserRoles[$empAcqualifyUserRoleD['lwUserId']] = $empAcqualifyUserRoleD['userId'];
                    }

                    $emplBranchesList = getEmployeeIdAssignedToBranch::getReport(['EID' => implode(',', $lwUserRoles)]);

                    foreach ($lwUserRoles as $eachEmpUser) {
                        if (array_key_exists($eachEmpUser, $emplBranchesList)) {
                            $empbranchList = (explode(',', $emplBranchesList[$eachEmpUser][0]['branchList']));
                            if (in_array($eachEmpUser, $empbranchList)) {
                                $branchAssignUsers['userValues'][] = $AcUserRoles[$eachEmpUser];
                            }
                        } else {
                            $branchAssignUsers['userValues'][] = $AcUserRoles[$eachEmpUser];
                        }
                    }
                }
            }
            if (isset($AcqualifyUserRoleDetails['Agent'])) {
                $AgentAcqualifyUserRoleDetails = $AcqualifyUserRoleDetails['Agent'];
                if (is_array($AgentAcqualifyUserRoleDetails)) {
                    foreach ($AgentAcqualifyUserRoleDetails as $agentAcqualifyUserRoleD) {
                        $lwUserRolesAgent[] = $agentAcqualifyUserRoleD['lwUserId'];
                        $AcUserRolesAgent[$agentAcqualifyUserRoleD['lwUserId']] = $agentAcqualifyUserRoleD['userId'];
                    }
                    $agentBranchesList = getAgentIdAssignedToBranch::getReport(['brokerNumbers' => implode(',', $lwUserRolesAgent)]);

                    foreach ($lwUserRolesAgent as $eachAgentUser) {
                        if (array_key_exists($eachAgentUser, $agentBranchesList)) {

                            $empbranchList = (explode(',', $agentBranchesList[$eachAgentUser][0]['branchList']));
                            if (in_array($eachAgentUser, $empbranchList)) {
                                $branchAssignUsers['userValues'][] = $AcUserRolesAgent[$eachAgentUser];
                            }
                        } else {
                            $branchAssignUsers['userValues'][] = $AcUserRolesAgent[$eachAgentUser];
                        }
                    }

                }
            }


            if (count($branchAssignUsers) > 0) {

                $acqualifyArray['data'] = ['branchId' => $branchAcqualiftBranchId, 'userIds' => [implode(',', $branchAssignUsers['userValues'])]];
                $acqualifyArray['url'] = 'https://www.acqualify.com/api/v1/add-users-to-branch';

                $acQualifyResponse = assignUserToBranch::getReport($acqualifyArray);
                $acQualifyResponse = json_decode($acQualifyResponse);

                if ($acQualifyResponse->status == 'success') {
                    if (count($acQualifyResponse->extraMessages) == 0) {
                        $qry = "update acqualifyUsers set branchIds = concat(branchIds,'," . $branchAcqualiftBranchId . "')  where  userId in (" . implode(',', $branchAssignUsers['userValues']) . ') ';
                        $acqualifyInsertId = Database2::getInstance()->update($qry);
                    }
                }
                $acqualifyPostArray['url'] = $acqualifyArray['url'];
                $acqualifyPostArray['request'] = $acqualifyArray;
                $acqualifyPostArray['response'] = $acQualifyResponse;
                $acqualifyPostArray['pcid'] = $processingCompanyId;
                postacQualifylog::getReport($acqualifyPostArray);

            }
        }

    }

}


//    if($userType == "PLO") {


$brokerArray = [];
$LMRInfoArray = [];
$myBrokerNumber = 0;
$referralCode = 1;
$ip = ['executiveEmails' => "'" . $email . "'"];
$LMRInfoArray = getPromoCodeForBranch::getReport($ip);

if (array_key_exists($email, $LMRInfoArray)) $referralCode = $LMRInfoArray[$email];

if ($executiveId > 0) {
    if ($userRole == 'Super') {
        updateBranchSubscriberID::getReport(['branchId' => $executiveId, 'subscriberID' => $subscriberID]);
    }


    $file_type = $_FILES['logoFile']['type'];
    if ($file_type != '') {
        if (in_array($file_type, glMimeTypes::$imageTypes)) {
            if ($_FILES['logoFile']['name'] != '') {
                if (!(is_dir(CONST_BRANCH_LOGO_PATH))) {
                    mkdir(CONST_BRANCH_LOGO_PATH);
                }
                $specialCharacters = ['%', '+', '&', '@', '?', '#', '*', '$', '^', '!'];
                $spaces = [' ', '  ', '   ', '    ', '     '];
                $file_name = str_replace($spaces, '_', $_FILES['logoFile']['name']);
                $file_name = str_replace($specialCharacters, '', $file_name);
                $logo = 'logo_LMR_' . $executiveId . '_' . $file_name;
                $fileTempName = $_FILES['logoFile']['tmp_name'];

                $logoExit = false;
                $logoExit = file_exists(CONST_BRANCH_LOGO_PATH . $logo);
                if ($logoExit) unlink(CONST_BRANCH_LOGO_PATH . basename($logo));
                if ($file_type == 'image/gif') {
                    $pos = strrpos($logo, '.');
                    $replaceString = substr($logo, $pos + 1);
                    $logo = str_replace($replaceString, 'jpg', $logo);
                    $fileName = CONST_BRANCH_LOGO_PATH . $logo;
                    convertGIF2JPG($fileTempName, $fileName, 'logo');
                } else if (($file_type == 'image/png') || ($file_type == 'image/x-png')) {
                    $pos = strrpos($logo, '.');
                    $replaceString = substr($logo, $pos + 1);
                    $logo = str_replace($replaceString, 'jpg', $logo);
                    $fileName = CONST_BRANCH_LOGO_PATH . $logo;
                    convertpng2JPG($fileTempName, $fileName, 'logo');
                } else {
                    $pos = strrpos($logo, '.');
                    $replaceString = substr($logo, $pos + 1);
                    $logo = str_replace($replaceString, 'jpg', $logo);
                    $fileName = CONST_BRANCH_LOGO_PATH . $logo;
                    resizeJPGImg($fileTempName, $fileName, 'logo');
                }
                $infoArray['LMRLogo'] = $logo;
                $infoArray['branchId'] = $executiveId;

                $upCnt = 0;
                $upCnt = updateBranchLogo::getReport($infoArray);
                if ($upCnt > 0 && trim($logo) != '' && $userRole == 'Branch') {
                    Strings::SetSess('userLogo', $logo);
                }
                if ($upCnt > 0 && trim($logo) != '') {
                    Strings::SetSess('branchLogo', $logo);
                }
            }
        } else {
            Strings::SetSess('msg', 'Unsupported File Format/File Size is too large.');
        }
    }

    if ($executiveId && $_FILES['branchAvatar']['type']
        && in_array($_FILES['branchAvatar']['type'], glMimeTypes::$imageTypes)
        && $_FILES['branchAvatar']['size']) {
        $branchAvatar = '/avatar/branch/' . $executiveId . '.' . pathinfo($_FILES['branchAvatar']['name'], PATHINFO_EXTENSION);
        FileStorage::moveFile($_FILES['branchAvatar']['tmp_name'], $branchAvatar);
        avatar::updateBranch($executiveId, $branchAvatar);
    }

// PC Association with branch Start

    if ($userRole == 'Super' || $allowToCreateBranch == 1) {

        $inputArray['branchId'] = $executiveId;
        $inputArray['PCID'] = $processingCompanyId;
        $inputArray['PBID'] = $PBID;

        if ($processingCompanyId > 0) {
            $inputArray['activeStatus'] = '1';
        } else {
            $inputArray['activeStatus'] = '0';
        }
        savePCAssociationWithBranch::getReport($inputArray);
    }

//PC Association with branch End /
    $clientTypeArray['branchID'] = $executiveId;
    $clientTypeArray['branchModuleTypeArray'] = $branchModuleTypeArray;
    $clientTypeArray['activeStatus'] = 1;
    saveBranchModules::getReport($clientTypeArray);

    require 'branchServiceFormSave.php';

}


if ($userRole == 'Super') {
    if ($useMyServerSetting == 1) {
        $myServerInfoArray['hostName'] = $hostName;
        $myServerInfoArray['userName'] = $userName;
        $myServerInfoArray['pwd'] = $SMTPPwd;
        $myServerInfoArray['portNo'] = $portNo;
        $myServerInfoArray['senderId'] = $executiveId;
        $myServerInfoArray['senderUserType'] = 'Branch';
        $myServerInfoArray['replyTo'] = $replyTo;
        $myServerInfoArray['bounceMail'] = $bounceMail;
        $myServerInfoArray['ESID'] = $ESID;

        saveUserServerInfo::getReport($myServerInfoArray);
    } else if ($ESID > 0) {
        deleteUserServerInfo::getReport($ESID);
    }
}

if ($isPublicWebform == 1) {
    $redirectUrl = '/backoffice/branchRegThankPage.php?bRc=' . cypher::myEncryption($promoCode) . '&p=' . cypher::myEncryption($processingCompanyId) . '&e=' . cypher::myEncryption($executiveId);
    if ($isJSON) {
        HTTP::ExitJSON([
            'code'        => 100,
            'msg'         => 'Successfully Inserted',
            'redirecturl' => $redirectUrl,
        ]);
    } else {
        HTTP::Redirect($redirectUrl);
    }
}


if ($alowareErr != '') Strings::SetSess('msg', $alowareErr); // Aloware Error messages.
if ($userRole == 'Branch') $redirect = CONST_URL_BR;
elseif ($userRole == 'Agent') $redirect = CONST_URL_AG;
else    $redirect = CONST_BO_URL;

$tabNumb = '';
if ($btnValue == 'Save & Next') {
    $tabNumb = '2';
} else {
    $tabNumb = '1';
}
if ($isPublicWebform == 0) {
    $redirect .= 'createBranch.php?eId=' . cypher::myEncryption($executiveId) . '&tabNumb=' . $tabNumb;
}

if (!($_SESSION['assignedPCID'] ?? 0) && isset($_REQUEST['assignedToProcessingCompany'])) {
    $redirect .= '&processorId=' . cypher::myEncryption($_REQUEST['assignedToProcessingCompany']);
}
if ($executiveId == 0) {
    $msg = 'Successfully Inserted';
} else {
    $msg = 'Successfully Updated!';
}
if ($isJSON) {
    HTTP::ExitJSON([
        'code'        => 100,
        'msg'         => $msg,
        'redirecturl' => $redirect,
    ]);
} else {
    HTTP::Redirect($redirect);
}

