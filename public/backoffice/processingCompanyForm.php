<?php
global $userRole, $modulesResultArray, $assignedPCID, $publicUser,
       $isUserActive, $tabNumb,
       $fileModules,
       $libModulesArray, $tempLibModulesArray, $glSelectedHMLOLMRFeeIdArray;

use models\composite\oEmployee\getPCEmployeeList;
use models\composite\oFile\getFileInfo\propertyCountyInfo;
use models\composite\oPC\getAllowAccessIP;
use models\composite\oPC\getDetailedPCList;
use models\composite\oPC\getDetailedPCList\processingCompany;
use models\composite\oPC\getEmailReportEvent;
use models\composite\oPC\getEMailServerInfo;
use models\composite\oPC\getFaxServerInfo;
use models\composite\oPC\getPCAuditor;
use models\composite\oPC\getPCInternalServiceType;
use models\composite\oPC\getPCModules;
use models\composite\oPC\getPCServiceType;
use models\composite\oPC\getProcessingCompanyFee;
use models\constants\eFaxServiceProviderArray;
use models\constants\gl\glCaptcha;
use models\constants\gl\glCSMRepresentativeArray;
use models\constants\gl\glFUModulesNotesTypeArray;
use models\constants\gl\glModuleArray;
use models\constants\gl\glNotesTypeArray;
use models\constants\gl\glSalesRepresentativeArray;
use models\constants\gl\glSelectedFUModulesNotesTypeIdArray;
use models\constants\gl\glSelectedLMRFeeIdArray;
use models\constants\gl\glSelectedNotesTypeIdArray;
use models\constants\gl\glUserGroup;
use models\constants\gl\glUserRegPCUsersArray;
use models\constants\gl\glUserRole;
use models\constants\gl\planTypes;
use models\constants\LMRFeeArray;
use models\constants\stateTimeZone;
use models\constants\timeZoneArray;
use models\Controllers\backoffice\LoanStagesController;
use models\Controllers\loanForm;
use models\cypher;
use models\PageVariables;
use models\standard\Arrays;
use models\standard\Strings;

$glUserRegPCUsersArray = glUserRegPCUsersArray::$glUserRegPCUsersArray;
$eFaxServiceProviderArray = eFaxServiceProviderArray::$eFaxServiceProviderArray;
$glFUModulesNotesTypeArray = glFUModulesNotesTypeArray::$glFUModulesNotesTypeArray;
$glSelectedNotesTypeIdArray = glSelectedNotesTypeIdArray::$glSelectedNotesTypeIdArray;
$glSalesRepresentativeArray = glSalesRepresentativeArray::$glSalesRepresentativeArray;
$LMRFeeArray = LMRFeeArray::$LMRFeeArray;
$stateTimeZone = stateTimeZone::$stateTimeZone;
$timeZoneArray = timeZoneArray::$timeZoneArray;
$glSelectedLMRFeeIdArray = glSelectedLMRFeeIdArray::$glSelectedLMRFeeIdArray;
$glSelectedFUModulesNotesTypeIdArray = glSelectedFUModulesNotesTypeIdArray::$glSelectedFUModulesNotesTypeIdArray;
$glNotesTypeArray = glNotesTypeArray::$glNotesTypeArray;
$glCSMRepresentativeArray = glCSMRepresentativeArray::$glCSMRepresentativeArray;

asort($libModulesArray);
asort($glNotesTypeArray);

$processingCompanyName = '';
$attorneyFName = '';
$attorneyMName = '';
$attorneyLName = '';
$appStates = '';
$phone1 = '';
$phone2 = '';
$phone3 = '';
$fax1 = '';
$fax2 = '';
$fax3 = '';
$address1 = '';
$city = '';
$state = '';
$zipCode = '';
$phoneArray = [];
$phoneNumber = '';
$fax = '';
$attorneyEmail = '';
$processingCompanyInfoArray = [];
$processingCompanyLogo = '';
$processingCompanyWebsite = '';
$LMRFeeKeyArray = [];
$LMRFeeId = '';
$processingFeeInfoArray = [];
$LMRFeeIdArray = [];
$processingPkgInfoArray = [];
$pkgName = '';
$pkgNameArray = [];
$allowToCreateBranch = 0;
$timeZone = '';
$LMRFileSubStatusArray = [];
$LMRFileSubStatus = '';
$processingFileSubStatusInfoArray = [];
$allowToCreateAloware = 0;
$allowToCreateEmployee = 0;
$myTimeZone = '';
$PCPLMStatusInfoArray = [];
$primaryLoanModStatusArray = [];
$primaryLoanModStatus = '';
$allowToSendUpdateToClient = 1;
$allowToOnOffAgentLogin = 1;
$myPKGIDArray = [];
$cellNumber = '';
$doingLM = '';
$PCEmailReportEvent = [];
$PCEmailReportEventArray = [];
$countyInfo = [];
$emailEventStatus = 0;

$companyGoals = '';
$regnFor = '';
$regnForArray = [];
$cell1 = '';
$cell2 = '';
$cell3 = '';
$cellNumberArray = [];
$nUsers = 0;
$nUsersAgent = 0;
$glUserRegPCUsersKeyArray = [];
$userRegEmail = '';
$PCStatus = 0;
$mySalesRep = '';
$myServerInfoUserSetting = 0;
$subscriberID = '';
$allowToEditLenderList = 0;
$moduleTypeArray = [];
$moduleType = '';
$modulesInfoArray = [];
$useMyNameAndEmail = 0;
$allowPCToViewAddOn = 1;
$allowESignService = 1;
$sendMassEmailToClient = 0;
$allowUBAToUpAndDowngradeClient = 1;
$EINNumber = '';
$sendFax = 1;
$emailCampaign = 1;
$isPLO = 1;
$subscribeToREST = 1;
$allow3WayCall = 1;
$procCompFooterLogo = '';
$sentUpDate = '';
$allowToSend = 0;
$PCSentUpdateArray = [];
$SID = 0;
$allowIPAccess = '';
$showFSCntInDash = 1;
$faxInfoUserSetting = 0;
$eFaxHName = '';
$eFaxUserName = '';
$eFaxPwd = '';
$faxDisp = '';
$allowIPAccessArray = [];
$processingCompanyArray = [];
$myServerInfoArray = [];
$ESID = 0;
$hostName = '';
$userName = '';
$pwd = '';
$PCSentUpdateArray = [];
$SID = 0;
$allowIPAccess = '';
$faxInfoUserSetting = 0;
$eFaxHName = '';
$eFaxUserName = '';
$eFaxPwd = '';
$faxDisp = '';
$allowIPAccessArray = [];
$processingCompanyArray = [];
$myServerInfoArray = [];
$ESID = 0;
$hostName = '';
$userName = '';
$pwd = '';
$portNo = '';
$replyTo = '';
$bounceMail = '';
$stateArray = [];
$allowToViewFSSF = 0;
$allowPCToMarketPlace = 0;
$allowPCToMarketPlacePublic = 0;
$allowPCToAccessPublicMarketPlaceLoanPrograms = 0;
$allowToLASubmitForPC = 0;
$allowToCFPBSubmitForPC = 0;
$allowPCUserToSubmitCFPB = 1;
$ext = '';
$subscribeToHOME = 0;
$showStateMapInDash = 1;
$showSysGenNote = 1;
$hideBorrowerInfo = 0;
$homeReportFee = 0;
$FSID = 0;
$AID = 0;
$auditors = '';
$loanNumberPrefix = 0;
$showStartLoanNumber = 0;
$adminUserTitle = '';
$adminUserName = '';

$auditorListArray = [];
$notesTypeInfoArray = ['GE'];
$theme = 1;
$allowEditToIR = $allowEditToIRAgent = 0;
$isSysNotesPrivate = 1;
$eFaxCompany = '';
$eFaxServiceProvider = 'Vitelity';
$PCServiceTypeInfoArray = [];
$PCServiceType = '';
$PCServiceTypeArray = [];

$allowPCToSendCustomText = 1;
$lenderPayableInfo = '';
$pcGlobalEmails = '';
$stateOfIncorporation = '';
$serviceExt = '';
$serviceExt = '';
$servicerName = '';
$servicerAddress = '';
$servicerEmail = '';
$servicePhone1 = '';
$servicePhone2 = '';
$servicePhone3 = '';
$isHMLOPC = 0;
$payoffPhoneNo = '';
$payoffPhoneNo1 = '';
$payoffPhoneNo2 = '';
$payoffPhoneNo3 = '';
$payoffPhoneExt = '';
$payOffRequestEmail = '';
$borrowerLoginURL = '';
$userSignEmail = 0;
$notesType = '';
$selectAllHideShow = 'display : none;';
$leadPostingAPI = 1;
$client_id = $client_secret = '';
$allowPeerstreet = '';
$thirdPartyServices = 1;
$thirdPartyServiceCSR = '';
$thirdPartyServicesProducts = '';
$loadMenu = 'v';
$isWebsite = 0;
$allowAutomation = 0; //default 0
$allowServicing = 0;
$allowWebformToPDF = 1;
$creditCardType = $creditCardNumber = $expirationMonth = $expirationYear = $cardNumberOnBack = $billingAddress1 = $billingAddress2 = $billingCity = $billingState = $billingZip = $cardHolderName = '';
$planType = '';
$stateArray = Arrays::fetchStates();
if ($userRole == 'Super') {
    $auditorListArray = getPCEmployeeList::getReport(['role' => 'Auditor', 'activeStatus' => '1']);
}
$PCModuleServiceKeyArray = [];
$PCModuleServiceKeyArray = array_keys($modulesResultArray);

/* Create Company for Default PC - ********* (billing fees & notes types) */
$tempprocessingFeeInfoArray = $defaultSelBillingfeeIDArr = $defaultPcBillingFeeTempArr = $defaultNotesTypeArr = $tempProcessingCompanyArray = $defaultNotesTypeArray = $tempPCServiceTypeInfoArray = $defaultPcLoanPgm = $defaultPcLoanPgmArr = [];
$defaultSelBillingfeeID = $defaultNotesType = $defaultLoanProg = '';
$inp['PCID'] = CONST_DEFAULT_PC;

$tempProcessingFeeInfoArray = getProcessingCompanyFee::getReport(CONST_DEFAULT_PC);

$defaultPcBillingFeeTempArr = $tempProcessingFeeInfoArray[CONST_DEFAULT_PC];

foreach ($defaultPcBillingFeeTempArr as $i => $item) {
    $defaultSelBillingfeeIDArr[$i] = $item->feeCode;
}
$defaultSelBillingfeeID = json_encode($defaultSelBillingfeeIDArr);

$tempProcessingCompanyArray = getDetailedPCList::getReport($inp);


foreach ($tempProcessingCompanyArray->processingCompanyArray as $i => $item) {
    $defaultNotesType = $item->notesType;
}

$defaultNotesTypeArray = explode(',', $defaultNotesType);
$defaultNotesTypeID = json_encode($defaultNotesTypeArray);

$allowNestedEntityMembers = 0;
$drawManagement = 1;
$enableDrawManagementV2 = 1;

/**
 * In super admin, when you add HMLO file type or any other file type... the default loan programs for HMLO always turn ON... please disable that function
 * Story# 1492 ( https://app.clubhouse.io/lendingwise/story/1492/super-admin-disable-function-that-defaults-loan-programs-as-on )
 */

$pcItem =  new processingCompany();

if ($assignedPCID > 0) {
    $ip['PCID'] = $assignedPCID;
    if (($userRole == 'Sale') || ($userRole == 'Super') || ($publicUser) || (!$isUserActive)) {
        $ip['allPCtatus'] = 'all';
    }

    $ip['marketPlaceStatus'] = 'all';
    $ip['marketPlacePublicStatus'] = 'all';
    $processingCompanyArray = getDetailedPCList::getReport($ip);


    $PCEmailReportEvent = getEmailReportEvent::getReport(['PCID' => $assignedPCID]);
    if (count($PCEmailReportEvent) > 0) {
        $PCEmailReportEventArray = explode(',', $PCEmailReportEvent[0]['eventType']);
        $emailEventStatus = $PCEmailReportEvent[0]['status'];
    }

    if ($tabNumb == 1 && $userRole == 'Super') $ip['subscribedOpt'] = 'ALL';

    $modulesInfoArray = getPCModules::getReport($ip);
    $processingFeeInfoArray = getProcessingCompanyFee::getReport($assignedPCID);

    $PCServiceTypeInfoArray = getPCServiceType::getReport($ip);

    $PCServiceTypeInternalLoanInfoArray = getPCInternalServiceType::getReport($ip);

    $allowIPAccessArray = getAllowAccessIP::getReport($ip);
    $PCAuditorArray = getPCAuditor::getReport($ip);
    if (count($PCAuditorArray) > 0) {
        $AID = $PCAuditorArray[$assignedPCID]['AID'];
        $auditors = $PCAuditorArray[$assignedPCID]['auditorID'];

    }

    if (count($allowIPAccessArray) > 0) $allowIPAccess = $allowIPAccessArray['IP'];
    if ($processingCompanyArray) {
        $processingCompanyInfoArray = $processingCompanyArray->processingCompanyArray;
    }

    if (count($PCSentUpdateArray) > 0) {
        $SID = $PCSentUpdateArray['SID'];
        $sentUpDate = $PCSentUpdateArray['weekDay'];
        $allowToSend = $PCSentUpdateArray['allowToSend'];
    }

    $pcItem = $processingCompanyArray->processingCompanyArray[0] ?? new processingCompany();

    $processingCompanyName = trim($pcItem->processingCompanyName);
    $attorneyFName = trim($pcItem->attorneyFName);
    $attorneyMName = trim($pcItem->attorneyMName);
    $attorneyLName = trim($pcItem->attorneyLName);
    $phoneNumber = trim($pcItem->attorneyTelephone);
    $cellNumber = trim($pcItem->attorneyCell);
    $fax = trim($pcItem->attorneyFascimile);
    $address1 = trim($pcItem->attorneyAddress);
    $city = trim($pcItem->attorneyCity);
    $appStates = trim($pcItem->attorneyState);
    $county = trim($pcItem->county) ?? '';
    $countyInfo = propertyCountyInfo::getReport('ALL', $appStates);
    $zipCode = trim($pcItem->attorneyZipCode);
    $attorneyEmail = trim($pcItem->attorneyEmail);
    $processingCompanyLogo = trim($pcItem->procCompLogo);
    $processingCompanyWebsite = trim($pcItem->processingCompanyWebsite);
    $allowToCreateBranch = trim($pcItem->allowToCreateBranch);
    $allowToViewFSSF = trim($pcItem->allowToViewFSSF);

    $allowPCToMarketPlace = trim($pcItem->allowPCToMarketPlace);
    $allowPCToMarketPlacePublic = trim($pcItem->allowPCToMarketPlacePublic);
    $allowPCToAccessPublicMarketPlaceLoanPrograms = trim($pcItem->allowPCToAccessPublicMarketPlaceLoanPrograms);

    $allowToCreateAloware = trim($pcItem->allowToCreateAloware);

    $allowToCreateEmployee = trim($pcItem->allowToCreateEmployee);
    $myTimeZone = trim($pcItem->timeZone);
    $allowToOnOffAgentLogin = trim($pcItem->allowToOnOffAgentLogin);
    $companyGoals = trim($pcItem->companyGoals);
    $regnFor = trim($pcItem->regnFor);
    $nUsers = trim($pcItem->nUsers);
    $nUsersAgent = trim($pcItem->nUsersAgent);
    $userRegEmail = trim($pcItem->userRegEmail);
    $PCStatus = trim($pcItem->activeStatus);
    $mySalesRep = trim($pcItem->salesRep);
    $myServerInfoUserSetting = trim($pcItem->serverInfoUserSetting);
    $subscriberID = trim($pcItem->subscriberID) ?? null;
    $customerID = trim($pcItem->customerID);
    $allowToEditLenderList = trim($pcItem->allowToEditLenderList);
    $useMyNameAndEmail = trim($pcItem->useMyNameAndEmail);
    $allowPCToViewAddOn = trim($pcItem->allowPCToViewAddOn);
    $allowESignService = trim($pcItem->allowESignService);
    $sendMassEmailToClient = trim($pcItem->sendMassEmailToClient);
    $allowUBAToUpAndDowngradeClient = trim($pcItem->allowUBAToUpAndDowngradeClient);
    $EINNumber = trim($pcItem->EINNumber);
    $sendFax = trim($pcItem->sendFax);
    $emailCampaign = trim($pcItem->allowEmailCampaign);
    $isPLO = trim($pcItem->isPLO);
    $isWebsite = trim($pcItem->isWebsite);
    $pcAcqualifyStatus = trim($pcItem->pcAcqualifyStatus);
    $subscribeToREST = trim($pcItem->subscribeToREST);
    $allow3WayCall = trim($pcItem->allow3WayCall);
    $procCompFooterLogo = trim($pcItem->procCompFooterLogo);
    $faxInfoUserSetting = trim($pcItem->faxInfoUserSetting);
    $homeReportFee = trim($pcItem->HRFee);
    $theme = trim($pcItem->theme);
    $isSysNotesPrivate = trim($pcItem->isSysNotesPrivate);
    $lenderPayableInfo = trim(rawurldecode($pcItem->lenderPayableInfo));
    $pcGlobalEmails = trim($pcItem->pcGlobalEmails);
    $thirdPartyServices = trim($pcItem->thirdPartyServices);
    $thirdPartyServiceCSR = trim($pcItem->thirdPartyServiceCSR);
    $thirdPartyServicesProducts = trim($pcItem->thirdPartyServicesProducts);

    $stateOfIncorporation = trim(rawurldecode($pcItem->stateOfIncorporation));

    $servicerName = trim(rawurldecode($pcItem->servicerName));
    $servicerAddress = trim(rawurldecode($pcItem->servicerAddress));
    $servicerEmail = trim(rawurldecode($pcItem->servicerEmail));
    $servicerPhone = trim(rawurldecode($pcItem->servicerPhone));

    if ($myTimeZone == '') {
        $myTimeZone = Arrays::recursive_array_search($appStates, $stateTimeZone);
    }
    $allowToLASubmitForPC = trim($pcItem->allowToLASubmitForPC);
    $subscribeToHOME = trim($pcItem->subscribeToHOME);
    $allowToCFPBSubmitForPC = trim($pcItem->allowToCFPBSubmitForPC);
    $allowPCUserToSubmitCFPB = trim($pcItem->allowPCUserToSubmitCFPB);
    $allowPCToSendCustomText = trim($pcItem->allowPCToSendCustomText);

    $showFSCntInDash = trim($pcItem->showFSCntInDash);
    $showStateMapInDash = trim($pcItem->showStateMapInDash);
    $showStartLoanNumber = trim($pcItem->showStartLoanNumber);
    $loanNumberPrefix = $pcItem->loanNumberPrefix;
    $payoffPhoneNo = trim($pcItem->payoffPhoneNo);
    $payOffRequestEmail = trim($pcItem->payOffRequestEmail);
    $notesType = trim($pcItem->notesType);
    $showSysGenNote = trim($pcItem->showSysGenNote);
    $hideBorrowerInfo = trim($pcItem->hideBorrowerInfo);
    $allowEditToIR = trim($pcItem->allowEditToIR);
    $allowEditToIRAgent = trim($pcItem->allowEditToIRAgent);
    $adminUserName = trim($pcItem->adminUserName);
    $adminUserTitle = trim($pcItem->adminUserTitle);
    $userSignEmail = trim($pcItem->userSignEmail);
    $leadPostingAPI = trim($pcItem->leadPostingAPI);
    # allow to Peerstreet.
    $allowPeerstreet = $pcItem->allowPeerstreet;
    # Peerstreet client secreat.
    $client_id = trim($pcItem->client_id);
    # Peerstreet client secreat.
    $client_secret = trim($pcItem->client_secret);

    $borrowerLoginURL = trim($pcItem->borrowerLoginURL);
    $loadMenu = trim($pcItem->loadMenu);
    $zohoLink = trim($pcItem->zohoLink);
    $drive = trim($pcItem->drive);
    $planType = trim($pcItem->planType);
    $captcha = trim($pcItem->captcha);

    $allowAutomation = trim($pcItem->allowAutomation);
    $pcPriceEngineStatus = trim($pcItem->pcPriceEngineStatus);
    $allowWebformToPDF = trim($pcItem->allowWebformToPDF);
    $allowServicing = trim($pcItem->allowServicing);
    $allowPropertyAddressAutoLookUp = trim($pcItem->allowPropertyAddressAutoLookUp);
    $nonInclusivePerDiem = trim($pcItem->nonInclusivePerDiem);
    $allowNestedEntityMembers = trim($pcItem->allowNestedEntityMembers);
    $allowCustomFields = trim($pcItem->allowCustomFields);
    $docWizard = $pcItem->docWizard ?? 0;
    $VIPSupport = $pcItem->VIPSupport ?? 0;
    $drawManagement = $pcItem->drawManagement ?? 0;
    $enableDrawManagementV2 = $pcItem->enableDrawManagementV2 ?? 0;
    $myCSMRep = $pcItem->CSMRep ?? null;
    $GHL = $pcItem->GHL ?? null;
    $identifierForMinAutoGeneration = $pcItem->identifierForMinAutoGeneration ?? null;
    //default vhost
    if (!$pcItem->vhost) {
        $pcItem->vhost = Strings::validateVhost(CONST_SITE_URL);
    }


    if (($phoneNumber == '') || ($phoneNumber == NULL) || ($phoneNumber == 'NULL')) {
    } else {
        $phoneArray = Strings::splitPhoneNumber($phoneNumber);
        if (count($phoneArray) > 0) {
            $phone1 = $phoneArray['No1'];
            $phone2 = $phoneArray['No2'];
            $phone3 = $phoneArray['No3'];
        }
        $ext = $phoneArray['Ext'];
    }
    if (($cellNumber == '') || ($cellNumber == NULL) || ($cellNumber == 'NULL')) {
    } else {
        $cellNumberArray = Strings::splitPhoneNumber($cellNumber);
        if (count($cellNumberArray) > 0) {
            $cell1 = $cellNumberArray['No1'];
            $cell2 = $cellNumberArray['No2'];
            $cell3 = $cellNumberArray['No3'];
        }
    }
    if (($fax == '') || ($fax == NULL) || ($fax == 'NULL')) {
    } else {
        $faxArray = Strings::splitPhoneNumber($fax);
        if (count($faxArray) > 0) {
            $fax1 = $faxArray['No1'];
            $fax2 = $faxArray['No2'];
            $fax3 = $faxArray['No3'];
        }
    }
    $phoneNumber = Strings::formatPhoneNumber($phoneNumber);
    $fax = Strings::formatPhoneNumber($fax);
    $cellNumber = Strings::formatPhoneNumber($cellNumber);


    if (($servicerPhone == '') || ($servicerPhone == NULL) || ($servicerPhone == 'NULL')) {
    } else {
        $servicerPhoneArray = Strings::splitPhoneNumber($servicerPhone);
        if (count($servicerPhoneArray) > 0) {
            $servicePhone1 = $servicerPhoneArray['No1'];
            $servicePhone2 = $servicerPhoneArray['No2'];
            $servicePhone3 = $servicerPhoneArray['No3'];
        }
        $serviceExt = $servicerPhoneArray['Ext'];
    }

    if (($payoffPhoneNo == '') || ($payoffPhoneNo == NULL) || ($payoffPhoneNo == 'NULL')) {
    } else {
        $payoffPhoneNoArray = Strings::splitPhoneNumber($payoffPhoneNo);
        if (count($payoffPhoneNoArray) > 0) {
            $payoffPhoneNo1 = $payoffPhoneNoArray['No1'];
            $payoffPhoneNo2 = $payoffPhoneNoArray['No2'];
            $payoffPhoneNo3 = $payoffPhoneNoArray['No3'];
        }
        $payoffPhoneExt = $payoffPhoneNoArray['Ext'];
    }
    if (count($processingFeeInfoArray) > 0) {
        $feeInfoArray = [];
        if (array_key_exists($assignedPCID, $processingFeeInfoArray)) {
            $feeInfoArray = $processingFeeInfoArray[$assignedPCID];
        }
        foreach ($feeInfoArray as $p => $item) {
            $feeCode = trim($item->feeCode);
            $LMRFeeIdArray[] = $feeCode;
        }
    }

    for ($c = 0; $c < count($PCServiceTypeInfoArray); $c++) {
        $temArray = [];
        $serviceType = '';
        $temArray = $PCServiceTypeInfoArray[$c];
        $serviceType = trim($temArray['LMRClientType']);
        if ($c > 0) {
            $PCServiceType .= ',';
        }
        $PCServiceType .= $serviceType;
    }

    if (count($PCServiceTypeInfoArray) > 0) $PCServiceTypeArray = explode(',', $PCServiceType);
    for ($c = 0; $c < count($modulesInfoArray); $c++) {
        $tempArray = [];
        $moduleCodes = '';
        $tempArray = $modulesInfoArray[$c];
        $moduleCodes = trim($tempArray['moduleCode']);
        if ($c > 0) {
            $moduleType .= ',';
        }
        $moduleType .= $moduleCodes;

        $libModulesStatusArray[trim($tempArray['moduleCode'])] = trim($tempArray['activeStatus']);
    }
    if (count($modulesInfoArray) > 0) $moduleTypeArray = explode(',', $moduleType);

    if (count($moduleTypeArray) > 0) { // Check is HMLO PC..
        for ($m = 0; $m < count($moduleTypeArray); $m++) {
            $tempMCode = '';
            $tempMCode = trim($moduleTypeArray[$m]);
            if ($tempMCode == 'HMLO') {
                $isHMLOPC = 1;
            }
        }
    }

}
if (trim($myTimeZone) == '') {
    $myTimeZone = CONST_SERVER_TIME_ZONE;
}

if (count($glUserRegPCUsersArray) > 0) {
    $glUserRegPCUsersKeyArray = array_keys($glUserRegPCUsersArray);
}
if ($userRole == 'Super') {
} else {
    if ($PCStatus == 1) {
        Strings::SetSess('isUserActive', true);
    }
}

if ($assignedPCID > 0) {
    $myInpArray['senderIds'] = $assignedPCID;
    $myInpArray['senderUserType'] = 'Employee';
    $myServerInfoArray = getEMailServerInfo::getReport($myInpArray);
    if (count($myServerInfoArray) > 0) {
        $hostName = $myServerInfoArray['Employee'][$assignedPCID]['hostName'];
        $userName = $myServerInfoArray['Employee'][$assignedPCID]['userName'];
        $pwd = $myServerInfoArray['Employee'][$assignedPCID]['pwd'];
        $portNo = $myServerInfoArray['Employee'][$assignedPCID]['portNo'];
        $replyTo = $myServerInfoArray['Employee'][$assignedPCID]['replyTo'];
        $bounceMail = $myServerInfoArray['Employee'][$assignedPCID]['bounceMail'];
        $ESID = $myServerInfoArray['Employee'][$assignedPCID]['ESID'];
    }
    $myFaxInfoArray = [];
    $InpArray['PCID'] = $assignedPCID;


    $myFaxInfoArray = getFaxServerInfo::getReport($InpArray);
    if (count($myFaxInfoArray) > 0) {
        $eFaxHName = trim($myFaxInfoArray['eFaxHName']);
        $eFaxCompany = trim($myFaxInfoArray['eFaxCompany']);
        $eFaxUserName = trim($myFaxInfoArray['eFaxUserName']);
        $eFaxPwd = trim($myFaxInfoArray['eFaxPwd']);
        $FSID = trim($myFaxInfoArray['FSID']);
        $eFaxServiceProvider = trim($myFaxInfoArray['eFaxServiceProvider']);
    }
}
if ($myServerInfoUserSetting == 1) $smtpDisp = 'block';
else $smtpDisp = 'none';
if ($faxInfoUserSetting == 1) $faxDisp = 'block';
else $faxDisp = 'none';
if ($assignedPCID > 0) {
//    $allowToCreateBranch = $allowToCreateBranch;
//    $allowToCreateEmployee = $allowToCreateEmployee;
//    $allowToCreateAloware = $allowToCreateAloware;
//    $allowToSendUpdateToClient = $allowToSendUpdateToClient;
//    $useMyNameAndEmail = $useMyNameAndEmail;
//    $allowToOnOffAgentLogin = $allowToOnOffAgentLogin;
} else {
    $allowToCreateBranch = CONST_ALLOW_TO_CREATE_BRANCH;
    $allowToCreateEmployee = CONST_ALLOW_TO_CREATE_EMPLOYEE;
    $allowToSendUpdateToClient = CONST_ALLOW_TO_SEND_UPDATE_TO_CLIENT;
    $useMyNameAndEmail = CONST_USE_MY_NAME_AND_EMAIL;
    $allowToOnOffAgentLogin = CONST_ALLOW_TO_ON_OFF_AGENT_LOGIN;
}

if (in_array('MF', $moduleTypeArray)) {
    $glNotesTypeArray = $glFUModulesNotesTypeArray;
}
if ($notesType != '') {
    $notesTypeInfoArray = explode(',', $notesType);
} else {
    if (in_array('MF', $moduleTypeArray)) {
        $notesTypeInfoArray = $glSelectedFUModulesNotesTypeIdArray;
    } else {
        $notesTypeInfoArray = $glSelectedNotesTypeIdArray;
    }
}
if (count($moduleTypeArray) > 1) $selectAllHideShow = 'display : block;';
if ($showStartLoanNumber == '') $showStartLoanNumber = 0;
$singleUserPlans = planTypes::$singleUserPlans;
$trackerEnabled = (int)(bool)LoanStagesController::getPcTrackerSettings($assignedPCID) ?? false;

?>
<style>
    .chzn-container-multi {
        width: 100% !important;
    }
</style>
<form class="form-horizontal form" autocomplete="off"
      name="processingCompanyForm" id="processingCompanyForm"
      ENCTYPE="multipart/form-data" method="post"
      action="processingCompanySave.php"
      onsubmit="return validateProcessingCompanyForm();">

    <input type="hidden" name="pcId" id="pcId" value="<?php echo cypher::myEncryption($assignedPCID); ?>">
    <input type="hidden" name="LMRFeeId" id="LMRFeeId" value="<?php echo $LMRFeeId; ?>">
    <input type="hidden" name="newCount" id="newCount" value="0">
    <input type="hidden" name="totalCount" id="totalCount" value="0">
    <input type="hidden" name="selFileSubStatus" id="selFileSubStatus" value="<?php echo $LMRFileSubStatus; ?>">
    <input type="hidden" name="selLMRClientType" id="selLMRClientType" value="<?php echo $moduleType; ?>">
    <input type="hidden" name="SID" id="SID" value="<?php echo $SID; ?>">
    <input type="hidden" name="userRole" id="userRole" value="<?php echo $userRole; ?>">
    <input type="hidden" name="publicUser" id="publicUser" value="<?php echo $publicUser; ?>">
    <input type="hidden" name="PCEmailChk" id="PCEmailChk" value="<?php echo $attorneyEmail; ?>">


    <?php if ($userRole == 'Super' || $userRole == 'Sales') { ?>
        <div class="row py-6  bg-gray-100 mb-4">
            <label class="col-md-4 py-2 font-weight-bold"> <?php if(in_array($planType,$singleUserPlans)){?>
                    # of Users Allowed:
            <?php } else {?>
                    # of Back Office or Branch Users Allowed:
            <?php }?></label>
            <div class="col-md-2 py-2"><input class="form-control input-sm" type="text" name="nUsers"
                                              value="<?php echo $nUsers ?>"></div>
            <div class="col-md-2 py-2"><a class="tooltipClass"
                                          href="mailto:<EMAIL>?subject=Add more users&body=I would like to add the following number of users to our account:%0D%0A%0D%0A%0D%0AThank you"
                                          alt="Click to add more users" title="Click to add more users"><b>Add
                        Users</b></a></div>
            <?php if ($userRole == 'Super') { ?>
                <div class="col-md-4 py-2 font-weight-bold">Documents Size: <i class="fas fa-paperclip tooltipClass"
                                                                               title="Documents Size"></i></div>
            <?php } ?>
            <label class="col-md-4 py-2 font-weight-bold <?php if(in_array($planType,$singleUserPlans)){ echo 'd-none'; }?>"># of Loan Officers or Brokers Allowed:</label>
            <div class="col-md-2 py-2 <?php if(in_array($planType,$singleUserPlans)){ echo 'd-none'; }?>"><input class="form-control input-sm" type="text" name="nUsersAgent"
                                              value="<?php echo $nUsersAgent ?>"></div>
            <label class="col-md-2 py-2 font-weight-bold"> Plan Type:</label>
            <div class="col-md-3 py-2">
                <?php
                if ($userRole == 'Super') {
                    ?>
                    <select class="form-control input-sm" name="planType" id="planType">
                        <option value=""> - Select Plan -</option>
                        <?php
                        for ($s = 0; $s < count(planTypes::$plansArray); $s++) {
                            ?>
                            <option
                                    value="<?php echo trim(planTypes::$plansArray[$s]) ?>" <?php echo Arrays::isSelected(trim(planTypes::$plansArray[$s]), $planType) ?>> <?php echo trim(planTypes::$plansArray[$s]) ?> </option>
                            <?php
                        }
                        ?>
                    </select>
                <?php } else { ?>
                    <?php echo $planType ?>
                <?php } ?>
            </div>
        </div>
    <?php } else { ?>
        <div class="row py-6  bg-gray-100 mb-4">
            <label class="col-md-4 py-2 font-weight-bold"> # of Back Office or Branch Users Allowed:</label>
            <div class="col-md-2 py-2"><?php echo $nUsers; ?></div>
            <div class="col-md-4 py-2"><a class="tooltipClass"
                                          href="mailto:<EMAIL>?subject=Add more users&body=I would like to add the following number of users to our account:%0D%0A%0D%0A%0D%0AThank you"
                                          alt="Click to add more users" title="Click to add more users"><b>Add
                        Users</b></a></div>

            <label class="col-md-4 py-2 font-weight-bold"># of Loan Officers or Brokers Allowed:</label>
            <div class="col-md-2 py-2"><?php echo $nUsersAgent; ?></div>
        </div>
    <?php } ?>
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card card-custom">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label">
                            Company Info
                        </h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                           data-card-tool="toggle"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                           data-card-tool="reload"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </a>
                        <a href="javascript:void(0);"
                           class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                           data-card-tool="toggle"
                           data-section="companyinfoCard"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body companyinfoCard" style="display: none">
                    <div class="row">
                        <div class="form-group row  row col-md-12">
                            <label class="font-weight-bold label-control col-md-4">Company Name</label>
                            <div class="col-md-8">
                                <?php if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') { ?>
                                    <input type="text" name="processingCompanyName" id="processingCompanyName"
                                           value="<?php echo htmlspecialchars($processingCompanyName) ?>"
                                           class="mandatory form-control input-sm" autocomplete="off">
                                <?php } else {
                                    echo '<h5>' . $processingCompanyName . '</h5>';
                                } ?>
                            </div>
                        </div>

                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Owner Name</label>
                            <?php
                            if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                ?>
                                <div class="col-md-4 col-sm-12">
                                    <input type="text" name="attorneyFName" id="attorneyFName"
                                           value="<?php echo $attorneyFName ?>" maxlength="25"
                                           class="form-control input-sm mandatory" autocomplete="off">
                                </div>
                                <div class="col-md-4 col-sm-12">
                                    <input type="text" name="attorneyLName" id="attorneyLName"
                                           value="<?php echo $attorneyLName ?>" maxlength="25"
                                           class="form-control input-sm mandatory" autocomplete="off">
                                </div>
                                <?php
                            } else {
                                echo "<div class=\"col-md-7\"><h5>" . $attorneyFName . ' ' . $attorneyLName . '</h5></div>';
                            }
                            ?>
                        </div>
                        <?php
                        if ($publicUser) {
                            ?>
                            <div class="form-group row col-md-12">
                                <label class="font-weight-bold col-md-4">User Reg Email</label>
                                <div class="col-md-8">
                                    <?php
                                    if ($assignedPCID > 0) {
                                        echo $userRegEmail;
                                        ?>
                                        <input type="hidden" name="userRegEmail" id="userRegEmail"
                                               value="<?php echo $userRegEmail ?>" maxlength="75">
                                    <?php } else { ?>
                                        <input type="text" name="userRegEmail" id="userRegEmail"
                                               value="<?php echo $userRegEmail ?>" maxlength="75"
                                               onblur="checkPCEmailExists();"
                                               class="form-control input-sm mandatory">
                                    <?php } ?>
                                </div>
                            </div>
                        <?php } else { ?>
                            <input type="hidden" name="userRegEmail" id="userRegEmail"
                                   value="<?php echo $userRegEmail ?>"
                                   size="40" maxlength="75" onblur="checkPCEmailExists();" class="mandatory">
                        <?php } ?>
                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Owner Email</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <input type="text" name="attorneyEmail" id="attorneyEmail"
                                           value="<?php echo $attorneyEmail ?>" maxlength="75" autocomplete="off"
                                           onblur="checkPCEmailExists();"
                                           class="form-control input-sm mandatory">
                                    <?php
                                } else {
                                    echo '<h5>' . $attorneyEmail . '</h5>';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Admin User Name</label>
                            <div class="col-md-8">
                                <input type="text" name="adminUserName" id="adminUserName" class="form-control input-sm"
                                       value="<?php echo $adminUserName ?>" maxlength="25">
                            </div>
                        </div>
                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Admin User Title</label>
                            <div class="col-md-8">
                                <input type="text" name="adminUserTitle" id="adminUserTitle"
                                       class="form-control input-sm"
                                       value="<?php echo $adminUserTitle ?>" maxlength="25">
                            </div>
                        </div>


                        <?php
                        if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                            ?>
                            <div class="form-group row col-md-12">
                                <label class="font-weight-bold col-md-4 with-children-tip">Logo <a
                                            class="fa fa-info-circle  tip-right"
                                            style="text-decoration:none;"
                                            title="The Image size should be 250px wide by 150px tall"></a></label>
                                <div class="col-md-8">
                                    <div class="left pad5"><input type="FILE" name="logoName" id="logoName" value="">
                                        <span> <i>(The file types allowed are JPEG,PNG,GIF.)</i></span>
                                    </div>
                                    <div class="left pad5">
                                        <?php
                                        $logoSize = false;
                                        $errorMsg = '';
                                        if ($processingCompanyLogo != '') {
                                            $logoExit = file_exists(CONST_PATH_PC_LOGO . $processingCompanyLogo);
                                            if ($logoExit) {
                                                $src = CONST_PC_LOGO_URL . $processingCompanyLogo;
                                                $logoSize = @getimagesize($src);
                                                if (!$logoSize) {
                                                    $errorMsg = 'Please check the logo you have uploaded';
                                                }
                                                if (trim($errorMsg) != '') {
                                                    echo "<font class=\"alertUsers\">" . $errorMsg . '</font><br>';
                                                }
                                                ?>
                                                <div class="col-md-4">
                                                <img src="<?php echo $src ?>">

                                                <?php
                                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                                    ?>

                                                    <a class="btn btn-xs btn-danger btn-text-primary  btn-icon m-1 tooltipClass"
                                                       style="text-decoration:none;"
                                                       href="javascript:procCompLogoDelete('<?php echo cypher::myEncryption($assignedPCID) ?>')"
                                                       title="Click to Delete Logo">
                                                        <i class="flaticon2-trash"></i>
                                                    </a>
                                                    </div>
                                                    <?php
                                                }
                                            }
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                        ?>
                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Telephone</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <input type="hidden" name="phone" value="">
                                    <input type="text" name="phoneNumber" id="phoneNumber" maxlength="31" size="4"
                                           value="<?php echo $phoneNumber ?>"
                                           class="mandatory form-control mask_phone">
                                    <?php
                                } else {
                                    echo '<h5>' . $phoneNumber . '</h5>';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Cell Number</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <input type="text" name="cellNumber" id="cellNumber" maxlength="25" size="4"
                                           value="<?php echo $cellNumber ?>" class="form-control mask_cellnew">
                                    <?php
                                } else {
                                    echo '<h5>' . $cellNumber . '</h5>';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Fascimile</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <input type="text" name="fax" id="fax" maxlength="25" size="4"
                                           value="<?php echo $fax ?>" class="form-control mask_cellnew">
                                    <?php
                                } else {
                                    echo '<h5>' . $fax . '</h5>';
                                }
                                ?>
                            </div>
                        </div>

                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Address</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <input class="form-control input-sm" type="text" name="address1" id="address1"
                                           value="<?php echo $address1 ?>" size="40" maxlength="75" autocomplete="off">
                                    <?php
                                } else {
                                    echo '<h5>' . $address1 . '</h5>';
                                }
                                ?>
                            </div>
                        </div>

                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">City</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <input class="form-control input-sm" type="text" name="city" id="city"
                                           value="<?php echo $city ?>" size="20" maxlength="30">
                                    <?php
                                } else {
                                    echo '<h5>' . $city . '</h5>';
                                }
                                ?>
                            </div>
                        </div>
                        <div class="clearfix"></div>

                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">State</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <select class="form-control input-sm" name="state" id="state"
                                            onchange="populateStateTimeZone('processingCompanyForm', 'state', 'timeZone');
                                            populateStateCounty('processingCompanyForm', 'state', 'county');">
                                        <option value=""> - Select -</option>
                                        <?php
                                        for ($s = 0; $s < count($stateArray); $s++) {
                                            ?>
                                            <option
                                                    value="<?php echo trim($stateArray[$s]['stateCode']) ?>" <?php echo Arrays::isSelected(trim($stateArray[$s]['stateCode']), $appStates) ?>><?php echo $stateArray[$s]['stateName'] ?></option>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                    <?php
                                } else {
                                    echo '<h5>' . $appStates . '</h5>';
                                }
                                ?>
                            </div>
                        </div>

                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Zip Code</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <input class="form-control input-sm" type="text" name="zipCode" id="zipCode"
                                           value="<?php echo $zipCode ?>" size="10" maxlength="10">
                                    <?php
                                } else {
                                    echo '<h5>' . $zipCode . '</h5>';
                                }
                                ?>
                            </div>
                        </div>


                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">County</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <select class="form-control input-sm"
                                            name="county" id="county">
                                        <option value=''> - Select County -</option>
                                        <?php
                                        for ($c = 0; $c < count($countyInfo); $c++) {
                                            $sOpt = '';
                                            $sOpt = Arrays::isSelected(trim($countyInfo[$c]['countyName']), $county);
                                            echo "<option value=\"" . trim($countyInfo[$c]['countyName']) . "\" " . $sOpt . '>' . trim($countyInfo[$c]['countyName']) . '</option>';
                                        }
                                        ?>
                                    </select>
                                    <?php
                                } else {
                                    echo '<h5>' . $appStates . '</h5>';
                                }
                                ?>
                            </div>
                        </div>

                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Website</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <input class="form-control input-sm" type="text" name="processingCompanyWebsite"
                                           id="processingCompanyWebsite" value="<?php echo $processingCompanyWebsite ?>"
                                           maxlength="150" autocomplete="off">
                                    <?php
                                } else {
                                    echo '<h5>' . $processingCompanyWebsite . '</h5>';
                                }
                                ?>
                            </div>
                        </div>


                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4 with-children-tip">Borrower Login URL<a
                                        class="fa fa-info-circle  tip-right" style="text-decoration:none;"
                                        title="Borrower Login URL is used for sending login credentials to the borrower, and also has merge tag fields within the doc & email wizards"></a></label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <input class="form-control input-sm" type="text" name="borrowerLoginURL"
                                           id="borrowerLoginURL" value="<?php echo $borrowerLoginURL ?>" maxlength="150"
                                           autocomplete="off">
                                    <?php
                                } else {
                                    echo '<h5>' . $borrowerLoginURL . '</h5>';
                                }
                                ?>
                            </div>
                        </div>

                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Time Zone</label>
                            <div class="col-md-8">
                                <?php
                                if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <select class="form-control input-sm" name="timeZone" id="timeZone">
                                        <?php
                                        $timeZoneKeyArray = [];
                                        $timeZoneKeyArray = array_keys($timeZoneArray);
                                        for ($tz = 0; $tz < count($timeZoneKeyArray); $tz++) {
                                            $timeZone = trim($timeZoneKeyArray[$tz]);
                                            ?>
                                            <option
                                                    value="<?php echo $timeZone ?>" <?php echo Arrays::isSelected($timeZone, $myTimeZone); ?>><?php echo $timeZoneArray[$timeZone] ?></option>
                                            <?php
                                        }
                                        ?>
                                    </select>
                                    <?php
                                } else {
                                    echo '<h5>' . $myTimeZone . '</h5>';
                                }
                                ?>
                            </div>
                        </div>
                        <?php if (in_array('HMLO', $fileModules)) {
                        } else { ?>
                            <div class="form-group row col-md-12">
                                <label class="font-weight-bold col-md-4">EIN Number</label>
                                <div class="col-md-8">
                                    <?php
                                    if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                        ?>
                                        <input type="text" name="EINNumber" id="EINNumber" class="form-control mask_ein"
                                               value="<?php echo $EINNumber ?>"
                                               maxlength="11">
                                        <?php
                                    } else {
                                        echo '<h5>' . $EINNumber . '</h5>';
                                    }
                                    ?>
                                </div>
                            </div>
                        <?php } ?>

                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">NMLS ID#</label>
                            <div class="col-md-8">
                                <input type="text" name="NMLSID" id="NMLSID" class="form-control input-sm"
                                       value="<?php echo $pcItem->NMLSID ?>" maxlength="11">
                            </div>
                        </div>


                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">MERS ID#</label>
                            <div class="col-md-8">
                                <input type="text" name="MERSID" id="MERSID" class="form-control input-sm"
                                       value="<?php echo $pcItem->MERSID ?>" maxlength="7">
                            </div>
                        </div>

                        <div class="col-md-12">
                            <div class="form-group row ">
                                <label class="col-md-6 font-weight-bold">Which LendingWise loan identifier to use for MIN auto-generation</label>
                                <div class="col-md-6 radio-inline">
                                    <label class="radio radio-solid font-weight-bolder" for="identifierForMinAutoGeneration1"><input
                                                class="newCheck" type="radio"
                                                name="identifierForMinAutoGeneration"
                                                id="identifierForMinAutoGeneration1"
                                                value="0" <?php echo Strings::isChecked('0', $identifierForMinAutoGeneration) ?> /><span></span>
                                        File Id</label>
                                    <label class="radio radio-solid font-weight-bolder" for="identifierForMinAutoGeneration2"><input
                                                class="newCheck" type="radio" name="identifierForMinAutoGeneration"
                                                id="identifierForMinAutoGeneration2"
                                                value="1" <?php echo Strings::isChecked('1', $identifierForMinAutoGeneration) ?> /><span></span>
                                        Loan Number</label>
                                </div>
                            </div>
                        </div>



                        <div class="form-group row col-md-12">
                            <label class="font-weight-bold col-md-4">Legal Entity Identifier</label>
                            <div class="col-md-8">
                                <input type="text" name="legalEntityIdentifier" id="legalEntityIdentifier" class="form-control input-sm"
                                       value="<?php echo $pcItem->legalEntityIdentifier ?>" maxlength="20">
                            </div>
                        </div>
                        <div id="vhostDiv" class="form-group row col-md-12" style="<?php if (!$pcItem->isPLO) {
                            echo 'display:none';
                        } ?>">
                            <label class="font-weight-bold col-md-4">vhost</label>
                            <div class="col-md-8">
                                <div class="input-group">
                                    <div class="input-group-prepend">
                                        <span class="input-group-text">https://</span>
                                    </div>
                                    <input type="text"
                                           name="vhost"
                                           id="vhost"
                                           class="form-control input-sm"
                                           onblur="processingCompany.validateVhost();"
                                           value="<?php echo $pcItem->vhost; ?>">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card card-custom">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label">
                            Company Settings
                        </h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                           data-card-tool="toggle"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                           data-card-tool="reload"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </a>
                        <a href="javascript:void(0);"
                           class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                           data-card-tool="toggle"
                           data-section="companysettingsCard"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                    </div>
                </div>

                <div class="card-body companysettingsCard" style="display: none">
                    <div class="form-group row">
                        <label class="font-weight-bold col-md-12 ">E-mail handling&nbsp; &nbsp;<i
                                    class="fa fa-info-circle text-primary tooltipClass" style="text-decoration:none;"
                                    title="This option lets you change the from E-mail address when our system sends out Email to ANYONE. So instead of the Email address showing &quot;<EMAIL>&quot; it will show your Email address. NOTE: This may cause significant Email delivery issues because many EMail providers like Hotmail, Gmail, Yahoo, etc... do not like this EMail &quot;Spoofing&quot; technique and may cause your email to NOT be delivered.<br>All options selected here will use the users E-mail as the Reply to and Bounce to E-Mail address except the branch selection."></i>
                        </label>
                        <div class="radio-list pl-6">
                            <label class="radio radio-solid font-weight-bold" for="useMyNameAndEmail2">
                                <input class="newCheck" type="radio"
                                       name="useMyNameAndEmail"
                                       id="useMyNameAndEmail2"
                                       value="0" <?php echo Strings::isChecked('0', $useMyNameAndEmail) ?> /><span></span>Recommended:
                                Do not use my name and email while sending emails <i
                                        class="fa fa-info-circle text-primary tooltipClass"
                                        style="text-decoration:none;"
                                        title="Use recommended settings: Emails will show as <NAME_EMAIL> but bounce and reply will show the user's email."></i>
                            </label>
                            <label class="radio radio-solid font-weight-bold" for="useMyNameAndEmail1"><input
                                        class="newCheck" type="radio"
                                        name="useMyNameAndEmail"
                                        id="useMyNameAndEmail1"
                                        value="1" <?php echo Strings::isChecked('1', $useMyNameAndEmail) ?> /><span></span>Use
                                my name and email while sending emails <i
                                        class="fa fa-info-circle text-primary tooltipClass "
                                        style="text-decoration:none;"
                                        title="Contact us to  properly setup your DKIM & SPF Domain settings to ensure maximum email delivery into the inbox. "></i></label>
                            <label class="radio radio-solid font-weight-bold" for="useMyNameAndEmail3"><input
                                        class="newCheck" type="radio"
                                        name="useMyNameAndEmail"
                                        id="useMyNameAndEmail3"
                                        value="branch" <?php echo Strings::isChecked('branch', $useMyNameAndEmail) ?> /><span></span>Use
                                Branch name and email while sending emails to customers <i
                                        class="fa fa-info-circle  text-primary tooltipClass"
                                        style="text-decoration:none;"
                                        title="Use branch name and email while sending emails to customer. Replies and bounces will go to the Branch email and not the user email."></i></label>
                        </div>
                    </div>

                    <?php if ($userRole == 'Super') { ?>
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-6">Enable Email Report: <i
                                        class="fa fa-info-circle  text-primary tooltipClass"
                                        style="text-decoration:none;"
                                        title="Enable Mail Forward?"></i></label>
                            <div class="col-md-6">
                               <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox" <?php if ($emailEventStatus == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $emailEventStatus ?>"
                                               id="enableMailEventId"
                                               onchange="toggleSwitch('enableMailEventId', 'emailEventStatus','1','0' );showEditSwitch('eventList','emailEventStatus');"/>
                                        <input type="hidden" name="emailEventStatus"
                                               id="emailEventStatus"
                                               value="<?php echo $emailEventStatus ?>">
                                        <span></span>
                                    </label>
                                </span>
                            </div>
                        </div>
                        <div class="form-group row">
                            <div class="radio-list pl-6 eventList" <?php if ($emailEventStatus == 0) {
                                echo "style='display:none;'";
                            } ?>>
                                <div class="checkbox-list">
                                    <label class="checkbox">
                                        <input type="checkbox" name="eventType[]"
                                               value="bounce" <?php if (in_array('bounce', $PCEmailReportEventArray)) {
                                            echo 'checked';
                                        } ?>/>
                                        <span></span>
                                        bounce
                                    </label>
                                    <label class="checkbox">
                                        <input type="checkbox" name="eventType[]"
                                               value="click" <?php if (in_array('click', $PCEmailReportEventArray)) {
                                            echo 'checked';
                                        } ?>/>
                                        <span></span>
                                        click
                                    </label>
                                    <label class="checkbox">
                                        <input type="checkbox" name="eventType[]"
                                               value="deferred" <?php if (in_array('deferred', $PCEmailReportEventArray)) {
                                            echo 'checked';
                                        } ?>/>
                                        <span></span>
                                        deferred
                                    </label>
                                    <label class="checkbox">
                                        <input type="checkbox" name="eventType[]"
                                               value="delivered" <?php if (in_array('delivered', $PCEmailReportEventArray)) {
                                            echo 'checked';
                                        } ?>/>
                                        <span></span>
                                        delivered
                                    </label>
                                    <label class="checkbox">
                                        <input type="checkbox" name="eventType[]"
                                               value="dropped" <?php if (in_array('dropped', $PCEmailReportEventArray)) {
                                            echo 'checked';
                                        } ?>/>
                                        <span></span>
                                        dropped
                                    </label>
                                    <label class="checkbox">
                                        <input type="checkbox" name="eventType[]"
                                               value="spamreport" <?php if (in_array('spamreport', $PCEmailReportEventArray)) {
                                            echo 'checked';
                                        } ?>/>
                                        <span></span>
                                        spamreport
                                    </label>
                                    <label class="checkbox">
                                        <input type="checkbox" name="eventType[]"
                                               value="unsubscribe" <?php if (in_array('unsubscribe', $PCEmailReportEventArray)) {
                                            echo 'checked';
                                        } ?>/>
                                        <span></span>
                                        unsubscribe
                                    </label>
                                </div>
                            </div>
                        </div>
                    <?php } ?>

                    <div class="form-group row">
                        <label class="font-weight-bold col-md-12">Use E-Fax Service: <i
                                    class="fa fa-info-circle  text-primary tooltipClass" style="text-decoration:none;"
                                    data-html="true"
                                    title="Default Settings will use The Loan Post's Fax provider. It is possible to use your own server to fax through The Loan Post. <br/>Currently we support: <br/>1) Vitelity <br/> 2) Ringcentral and <br/> 3) Faxage. <br/>If your provider is not listed above, please contact us directly."></i></label>
                        <div class="radio-list pl-6">
                            <label class="radio radio-solid font-weight-bold" for="faxInfoUserSetting1">
                                <input type="radio" class="newCheck"
                                       name="faxInfoUserSetting"
                                       id="faxInfoUserSetting1"
                                       value="0" <?php echo Strings::isChecked($faxInfoUserSetting, '0') ?>
                                       onclick="showFaxInfo('0','faxSettings');"><span></span>
                                Use my default settings? <b>(.06 per page)</b></label>
                            <label class="radio radio-solid font-weight-bold" for="faxInfoUserSetting2"><input
                                        type="radio" class="newCheck"
                                        name="faxInfoUserSetting"
                                        id="faxInfoUserSetting2"
                                        value="1" <?php echo Strings::isChecked($faxInfoUserSetting, '1') ?>
                                        onclick="showFaxInfo('1','faxSettings');"><span></span>
                                Let me specify my own server details? </label>
                            <input type="hidden" name="FSID" id="FSID" value="<?php echo $FSID ?>">
                        </div>
                    </div>


                    <div id="faxSettings" style="display:<?php echo $faxDisp; ?>;">
                        <div class="form-group row">
                            <label class="font-weight-bold col-md-4">E-Fax Service Provider</label>
                            <div class="col-md-8">
                                <select class="form-control input-sm" name="eFaxServiceProvider"
                                        id="eFaxServiceProvider"
                                        onchange="checkEFaxServiceProvider(this.value);">
                                    <?php for ($k = 0; $k < count($eFaxServiceProviderArray); $k++) { ?>
                                        <option
                                                value="<?php echo trim($eFaxServiceProviderArray[$k]) ?>" <?php echo Arrays::isSelected(trim($eFaxServiceProviderArray[$k]), $eFaxServiceProvider); ?>><?php echo trim($eFaxServiceProviderArray[$k]) ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>

                        <div class="form-group row ">
                            <label class="font-weight-bold col-md-4">E-Fax Hostname</label>
                            <div class="col-md-8">
                                <input class="form-control input-sm" type="text" name="eFaxHName" id="eFaxHName"
                                       value="<?php echo $eFaxHName ?>" size="40">
                            </div>
                        </div>

                        <div class="form-group row ">
                            <label class="font-weight-bold col-md-4">
                                <span id="serviceProviderDiv"
                                      style="display:<?php if ($eFaxServiceProvider == 'Ringcentral') {
                                          echo 'none';
                                      } else { ?>block<?php } ?>;"><?php if ($eFaxServiceProvider == 'Vitelity') { ?> DID Number <?php } else { ?>E-Fax Company<?php } ?></span>
                            </label>
                            <div class="col-md-8">
                                <span id="serviceProviderDiv1"
                                      style="display:<?php if ($eFaxServiceProvider == 'Ringcentral') {
                                          echo 'none';
                                      } else { ?>block<?php } ?>;"><input type="text" name="eFaxCompany"
                                                                          id="eFaxCompany" class="form-control"
                                                                          value="<?php echo $eFaxCompany ?>"></span>
                            </div>
                        </div>
                        <div class="clearfix"></div>
                        <div class="form-group row ">
                            <label class="font-weight-bold col-md-4">E-Fax Username:</label>
                            <div class="col-md-8">
                                <input class="form-control input-sm" type="text" name="eFaxUserName" id="eFaxUserName"
                                       value="<?php echo $eFaxUserName ?>">
                            </div>
                        </div>

                        <div class="form-group row ">
                            <label class="font-weight-bold col-md-4">E-Fax Password:</label>
                            <div class="col-md-8">
                                <input class="form-control input-sm" type="password" name="eFaxPwd" id="eFaxPwd"
                                       value="<?php echo $eFaxPwd ?>">
                            </div>
                        </div>
                    </div>
                    <div class="form-group row align-items-center">
                        <label class="col-lg-8 font-weight-bold">Show System Generated Notes
                            <i class="fa fa-info-circle text-primary tooltipClass"
                               style="text-decoration:none;"
                               title="System generated notes display in the notes section & are based on user activity like, file created, file status changed, interest rate changed, originated fees changed, etc"></i></label>
                        <div class="col-lg-4">
                                <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox" <?php if ($showSysGenNote == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $showSysGenNote ?>" id="SGNTog"
                                               onchange="toggleSwitch('SGNTog', 'showSysGenNote','1','0' );"/>
                                        <input type="hidden" name="showSysGenNote" id="showSysGenNote"
                                               value="<?php echo $showSysGenNote ?>">
                                        <span></span>
                                    </label>
                                </span>
                        </div>
                    </div>

                    <div class="form-group row ">
                        <label class="col-md-6 font-weight-bold">Default system generated notes as <i
                                    class="fa fa-info-circle text-primary tooltipClass" style="text-decoration:none;"
                                    title="Choose if you want all system notes to default as Public or Private. Public notes can be viewed by all users, including end clients if they have access to view client file notes. Private notes are only viewable to users with that permission enabled."></i></label>
                        <div class="col-md-6 radio-inline">
                            <label class="radio radio-solid font-weight-bolder" for="isSysNotesPrivate"><input
                                        class="newCheck" type="radio"
                                        name="isSysNotesPrivate"
                                        id="isSysNotesPrivate"
                                        value="0" <?php echo Strings::isChecked('0', $isSysNotesPrivate) ?> /><span></span>
                                Public</label>
                            <label class="radio radio-solid font-weight-bolder" for="isSysNotesPrivate2"><input
                                        class="newCheck" type="radio" name="isSysNotesPrivate"
                                        id="isSysNotesPrivate2"
                                        value="1" <?php echo Strings::isChecked('1', $isSysNotesPrivate) ?> /><span></span>
                                Private</label>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-lg-8 font-weight-bold">Show file status in dashboard</label>
                        <div class="col-lg-4">
                            <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox" <?php if ($showFSCntInDash == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $showFSCntInDash ?>" id="FSInDash"
                                               onchange="toggleSwitch('FSInDash', 'showFSCntInDash','1','0' );"/>
                                        <input type="hidden" name="showFSCntInDash" id="showFSCntInDash"
                                               value="<?php echo $showFSCntInDash ?>">
                                        <span></span>
                                    </label>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-8 font-weight-bold">Show state map in dashboard</label>
                        <div class="col-md-4">
                            <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox" <?php if ($showStateMapInDash == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $showStateMapInDash ?>" id="SMInDash"
                                               onchange="toggleSwitch('SMInDash', 'showStateMapInDash','1','0' );"/>
                                        <input type="hidden" name="showStateMapInDash" id="showStateMapInDash"
                                               value="<?php echo $showStateMapInDash ?>">
                                        <span></span>
                                    </label>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-8 font-weight-bold">Is Non Inclusive PerDiem
                            <i class="fa fa-info-circle text-primary tooltipClass"
                               style="text-decoration:none;"
                               title="This controls if you want the last day of the date range selected for accrued per diem interest to be included."></i>
                        </label>
                        <div class="col-md-4">
                            <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox" <?php if ($nonInclusivePerDiem == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $nonInclusivePerDiem ?>" id="nonInclusivePerDiemCheck"
                                               onchange="toggleSwitch('nonInclusivePerDiemCheck', 'nonInclusivePerDiem','1','0' );"/>
                                        <input type="hidden" name="nonInclusivePerDiem" id="nonInclusivePerDiem"
                                               value="<?php echo $nonInclusivePerDiem ?>">
                                        <span></span>
                                    </label>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-8 font-weight-bold">Enable Nested Entity Members</label>
                        <div class="col-md-4">
                            <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox" <?php if ($allowNestedEntityMembers == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $allowNestedEntityMembers; ?>" id="allowNestedEntityMembersCheck"
                                               onchange="processingCompany.toggleSwitch_PC();"/>
                                        <input type="hidden" name="allowNestedEntityMembers" id="allowNestedEntityMembers"
                                               value="<?php echo $allowNestedEntityMembers; ?>">
                                        <span></span>
                                    </label>
                            </span>
                        </div>
                    </div>

                    <!--<div class="form-group row col-md-12">
                        <label class="col-md-9 with-children-tip">Allow Branches to edit interest rate / Cost of Capital / Yield spread?</label>
                        <div class="col-md-3">
                            <div id="allowToEditToIR" <?php if ($allowEditToIR == '1') { ?> class="switch-on" <?php } else { ?> class="switch-off" <?php } ?> onclick="toggleSwitch('allowToEditToIR', 'allowEditToIR', '1', '0' );"></div>
                            <input type="hidden" name="allowEditToIR" id="allowEditToIR" value="<?php echo $allowEditToIR ?>">
                        </div>
                    </div>
                    <div class="form-group row col-md-12">
                        <label class="col-md-9 with-children-tip">Allow Brokers to edit interest rate / Cost of Capital / Yield spread?</label>
                        <div class="col-md-3">
                            <div id="allowToEditToIRAgent" <?php if ($allowEditToIRAgent == '1') { ?> class="switch-on" <?php } else { ?> class="switch-off" <?php } ?> onclick="toggleSwitch('allowToEditToIRAgent', 'allowEditToIRAgent', '1', '0' );"></div>
                            <input type="hidden" name="allowEditToIRAgent" id="allowEditToIRAgent" value="<?php echo $allowEditToIRAgent ?>">
                        </div>
                    </div>-->
                    <div class="form-group row">
                        <label class="col-md-8 font-weight-bold">Use Auto Generated Loan Number(s)?</label>
                        <div class="col-md-4">
                            <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox" <?php if ($showStartLoanNumber == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $showStartLoanNumber ?>" id="startLoanNumberDiv"
                                               onchange="toggleSwitch('startLoanNumberDiv', 'showStartLoanNumber','1','0' );
                                               showEditSwitch('startLoanNumberDiv','showStartLoanNumber');"/>
                                        <input type="hidden" name="showStartLoanNumber" id="showStartLoanNumber"
                                               value="<?php echo $showStartLoanNumber ?>">
                                        <span></span>
                                    </label>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row startLoanNumberDiv"
                         style="<?php if ($showStartLoanNumber == '0') { ?>display: none;<?php } else { ?>display: block;<?php } ?>">
                        <label class="col-md-6 font-weight-bold">Starting Loan number</label>
                        <div class="col-md-6">
                            <input type="number" class="form-control input-sm" name="loanNumberPrefix"
                                   id="loanNumberPrefix" value="<?php echo $loanNumberPrefix ?>">
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-8 font-weight-bold">Automatically include a user signature on outgoing
                            emails?</label>
                        <div class="col-md-4">
                            <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox" <?php if ($userSignEmail == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $userSignEmail ?>" id="userEmail"
                                               onchange="toggleSwitch('userEmail', 'userSignEmail','1','0' );"/>
                                        <input type="hidden" name="userSignEmail" id="userSignEmail"
                                               value="<?php echo $userSignEmail ?>">
                                        <span></span>
                                    </label>
                            </span>
                        </div>
                    </div>
                    <div class="form-group row">
                        <label class="col-md-8 font-weight-bold">Enable Loan Tracker</label>
                        <div class="col-md-4">
                            <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox" <?php if ($trackerEnabled == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $trackerEnabled ?>" id="loanTrackerStatus"
                                               onchange="toggleSwitch('loanTrackerStatus', 'trackerEnabled','1','0' );"/>
                                        <input type="hidden" name="trackerEnabled" id="trackerEnabled"
                                               value="<?php echo $trackerEnabled ?>">
                                        <span></span>
                                    </label>
                            </span>
                        </div>
                    </div>

                    <?php if ($drawManagement == 1) { ?>
                    <div class="form-group row">
                        <label class="col-md-8 font-weight-bold">Use New Version of Draw Management?</label>
                        <div class="col-md-4">
                            <span class="switch switch-icon">
                                    <label>
                                        <input class="form-control"
                                               type="checkbox" <?php if ($enableDrawManagementV2 == 1) { ?> checked="checked" <?php } ?>
                                               value="<?php echo $enableDrawManagementV2 ?>" id="drawManagementV2"
                                               onchange="toggleSwitch('drawManagementV2', 'enableDrawManagementV2','1','0' );"/>
                                        <input type="hidden" name="enableDrawManagementV2" id="enableDrawManagementV2"
                                               value="<?php echo $enableDrawManagementV2 ?>">
                                        <span></span>
                                    </label>
                            </span>
                        </div>
                    </div>
                    <?php } ?>

                    <div class="form-group row  ">
                        <label class="col-md-6 font-weight-bold">Enter Emails to Receive Document Information.</label>
                        <div class="col-md-6">
                            <textarea class="form-control input-sm" name="pcGlobalEmails" id="pcGlobalEmails" cols="55"
                                      rows="4"><?php echo $pcGlobalEmails; ?></textarea>
                            <span class="notes">Separate With Semicoln(;)</span>
                        </div>
                    </div>

                    <div class="form-group row  d-none">
                        <label class="col-md-4 font-weight-bold">Menu Position in File level</label>
                        <div class="col-md-8 radio-inline">
                            <label class="radio radio-solid font-weight-bolder" for="loadMenuH">
                                <input class="newCheck" type="radio"
                                       name="loadMenu"
                                       id="loadMenuH"
                                       value="h" <?php if ($loadMenu == 'h') {
                                    echo 'checked';
                                } ?> /><span></span>
                                Horizontal</label>
                            <label class="radio radio-solid font-weight-bolder" for="loadMenuV">
                                <input class="newCheck" type="radio"
                                       name="loadMenu" id="loadMenuV" value="v"
                                    <?php if ($loadMenu == 'v') {
                                        echo 'checked';
                                    } ?> /><span></span>
                                Vertical</label>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="card card-custom">
                <div class="card-header card-header-tabs-line bg-gray-100  ">
                    <div class="card-title">
                        <h3 class="card-label">
                            Loan Servicing
                        </h3>
                    </div>
                    <div class="card-toolbar">
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                           data-card-tool="toggle"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                        <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                           data-card-tool="reload"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                            <i class="ki ki-reload icon-nm"></i>
                        </a>
                        <a href="javascript:void(0);"
                           class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                           data-card-tool="toggle"
                           data-section="loanServicingCard"
                           data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                            <i class="ki ki-arrow-down icon-nm"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body loanServicingCard" style="display: none">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-md-4 font-weight-bold">Servicer Name</label>
                                <div class="col-md-8">
                                    <input class="form-control input-sm" type="text" name="servicerName"
                                           id="servicerName"
                                           value="<?php echo $servicerName ?>" maxlength="55" autocomplete="off">
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group row">
                                <label class="col-md-4 font-weight-bold">Servicer Address</label>
                                <div class="col-md-8">
                                    <input class="form-control input-sm" type="text" name="servicerAddress"
                                           id="servicerAddress" value="<?php echo $servicerAddress ?>" maxlength="80"
                                           autocomplete="off">
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group row ">
                                <label class="col-md-4 font-weight-bold">Servicer Email</label>
                                <div class="col-md-8">
                                    <input class="form-control input-sm" type="text" name="servicerEmail"
                                           id="servicerEmail"
                                           value="<?php echo $servicerEmail ?>" maxlength="75" autocomplete="off">
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-group row ">
                                <label class="col-md-4 font-weight-bold">Servicer Phone</label>
                                <div class="col-md-8">
                                    <input type="text" name="servicePhone" id="servicePhone" maxlength="31" size="4"
                                           value="<?php echo $servicerPhone; ?>" class="form-control mask_phone">
                                </div>
                            </div>
                        </div>
                        <?php if ($isHMLOPC == 1) { ?>
                            <div class="col-md-6">
                                <div class="form-group row ">
                                    <label class="col-md-4 font-weight-bold">Pay Off Phone #</label>
                                    <div class="col-md-8">
                                        <?php if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') { ?>

                                            <input type="text" name="payoffPhoneNo" id="payoffPhoneNo" maxlength="31"
                                                   size="4" value="<?php echo $payoffPhoneNo ?>"
                                                   class="form-control mask_phone">

                                        <?php } else {
                                            echo '<h5>' . $payoffPhoneNo . '</h5>';
                                        } ?>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group row ">
                                    <label class="col-md-4 font-weight-bold">Pay Off Request Email</label>
                                    <div class="col-md-8">
                                        <?php if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') { ?>
                                            <input class="form-control input-sm" type="text" name="payOffRequestEmail"
                                                   id="payOffRequestEmail" value="<?php echo $payOffRequestEmail; ?>"
                                                   size="50" maxlength="75" autocomplete="off">

                                        <?php } else {
                                            echo '<h5>' . $payOffRequestEmail . '</h5>';
                                        } ?>
                                    </div>
                                </div>
                            </div>
                        <?php } ?>
                        <div class="col-md-6">
                            <div class="form-group row ">
                                <label class="col-md-4 font-weight-bold">Lenders Loss Payable<br>Endorsement
                                    Info</label>
                                <div class="col-md-8">
                                    <?php
                                    if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                        ?>
                                        <textarea class="form-control input-sm" name="lenderPayableInfo"
                                                  id="lenderPayableInfo" cols="55"
                                                  rows="4"><?php echo $lenderPayableInfo; ?></textarea>
                                        <?php
                                    } else {
                                        echo '<h5>' . $lenderPayableInfo . '</h5>';
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-12  mb-4">
            <?php if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') { ?>
                <h6 class="text-danger font-weight-bold">
                    <i class="fa fa-info-circle text-primary tooltipClass"
                       title="This controls  which modules are available in your system. We offer loss mitigation, asset management, & business funding modules."></i>&nbsp;Select
                    the Types of Files to Activate for Your Account:
                </h6>
            <?php } else { ?>
                <h6 style="color:red;"><i class="fa fa-info-circle text-primary tooltipClass"
                                          style="text-decoration:none;"
                                          title="This controls  which modules are available in your system. We offer loss mitigation, asset management, & business funding modules."></i>&nbsp;Types
                    of Files to Activated in Your Account:
                </h6>
            <?php } ?>

            <?php
            if ($assignedPCID == 0) {
                $moduleTypeArray = glModuleArray::$glModuleArray;
            }
            ?>
            <?php
            if ($userRole == 'Super' || $userRole == 'Manager') { ?>
                <select data-placeholder="Select Module Types" name="moduleType[]" id="moduleType"
                        onchange="privateLabelChange();" class="chzn-select form-control form-controller-solid"
                        multiple="">
                    <?php
                    $lc = 0;
                    $modulesCodeArray = array_keys($libModulesArray);

                    if ($assignedPCID > 0) {
                        $myTmpLMRClientTypeArray = $moduleTypeArray;
                    } else {
                        $myTmpLMRClientTypeArray = $libModulesArray;
                    }

                    foreach ($modulesCodeArray as $lc => $moduleCode) {
                        $moduleActiveStatus = 0;
                        $moduleVal = '';
                        $selOpt = '';
                        $moduleCode = trim($moduleCode);
                        if ($userRole == 'Super') {
                            $moduleActiveStatus = 1;
                        } else {
                            $moduleActiveStatus = trim($libModulesStatusArray[$moduleCode]);
                        }
                        if (array_key_exists($moduleCode, $libModulesArray)) {
                            $moduleVal = $libModulesArray[$moduleCode];
                            if ($moduleActiveStatus == 1) {
                                $selOpt = Arrays::isSelectedArray($moduleTypeArray, $moduleCode);
                            } else {
                                while (($serachkey1 = array_search($moduleCode, $moduleTypeArray)) !== false) {
                                    unset($moduleTypeArray[$serachkey1]);
                                }
                            }
                            ?>
                            <option
                                    value="<?php echo $moduleCode ?>" <?php echo $selOpt; ?>><?php echo $moduleVal ?></option>
                            <?php
                        }
                    }
                    unset($modulesCodeArray);
                    ?>
                </select>
                <?php
            } else {
                $lc11 = 0;
                echo "<h5 class = \"div450 even force-wrap\">";
                $tempModuleCode = '';
                for ($lc1 = 0; $lc1 < count($tempLibModulesArray); $lc1++) {
                    $moduleCodeVal = '';
                    $moduleNameVal = '';
                    $moduleCodeVal = trim($tempLibModulesArray[$lc1]['moduleCode']);
                    $moduleNameVal = trim($tempLibModulesArray[$lc1]['moduleName']);

                    if ($lc11 > 0) {
                        echo ', ';
                        $tempModuleCode .= ', ';
                    }
                    $tempModuleCode .= $moduleCodeVal;
                    echo '<b>' . trim($tempLibModulesArray[$lc1]['moduleName']) . '</b>';
                    $lc11++;
                }
                echo '</h5>';
                ?>
                <input type="hidden" name="moduleTypes" id="moduleTypes" value="<?php echo $tempModuleCode ?>"/>
            <?php } ?>
        </div>
    </div>


    <div class="row">
        <div class="col-md-6">
            <h4 class="text-danger font-weight-bold">
                <a class="fa fa-info-circle text-primary tooltipClass"
                   style="text-decoration:none;"
                   title="You can add or remove loan program names. These will display for the borrower, brokers, and all users of the system when submitting or creating a loan file. You may also control the loan guidelines."></a>&nbsp;Descriptive <?php if (in_array('HMLO', $fileModules)) { ?>Loan Programs<?php } else { ?>Services<?php } ?>
            </h4>
        </div>
        <div class="selectAllHideShow col-md-6" style="<?php echo $selectAllHideShow; ?>;display:none;">

            <label for="checkAllFields" class="h4">
                Select
                All <?php if (in_array('HMLO', $fileModules)) { ?>Loan Programs<?php } else { ?>Services<?php } ?>
                <input class="newCheck" type="checkbox"
                       name="checkAllFields"
                       id="checkAllFields" value=""
                       onclick="clickToSelectAllModuleServices(this.checked);"/><span
                        class="chk-purple"></span></label>
        </div>
    </div>


    <div id="descriptiveServices">
        <?php require 'PCDescriptiveServices.php' ?>
    </div>
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="row">
                <div class="col-md-6">
                    <label class="font-weight-bold"><i
                                class="fa fa-info-circle text-primary tooltipClass" style="text-decoration:none;"
                                title="You can turn On/Off certain billing fees, which are applicable to your organization. They will be available for the client billing module located under the Billing/Commissions section of every client file. If you need a custom billing fees added, please email us."></i>
                        <?php
                        if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                            echo 'Select the billing fee(s)';
                        } else {
                            echo 'Selected the billing fee(s)';
                        }
                        ?>
                    </label>
                    <?php
                    if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                        ?>
                        &nbsp;<select data-placeholder="Select Billing Fee" name="LMRFee[]" id="LMRFee"
                                      class="chzn-select" multiple="" style="width:100%;">
                            <?php

                            if ($assignedPCID > 0) {
                                $myLMRFeeIdArray = $LMRFeeIdArray;
                            } else {
                                if ($isHMLOPC == 1) {
                                    $myLMRFeeIdArray = $glSelectedHMLOLMRFeeIdArray;
                                } else {
                                    $myLMRFeeIdArray = $glSelectedLMRFeeIdArray;
                                }
                            }
                            asort($LMRFeeArray);

                            if (count($LMRFeeArray) > 0) {
                                $LMRFeeKeyArray = array_keys($LMRFeeArray);
                            }
                            $LMRFeeCnt = 0;
                            $LMRFeeCnt = count($LMRFeeKeyArray);

                            //  if($userRole == "Manager" || $userRole == "Super" || $userRole == "Sales") {
                            $LMRFeeKeyArray = [];
                            $LMRClientTypeCodeArray = [];
                            $LMRFeeKeyArray = array_keys($LMRFeeArray);
                            for ($lc = 0; $lc < count($LMRFeeArray); $lc++) {
                                $LMRFeeKey = '';
                                $LMRFeeKey = trim($LMRFeeKeyArray[$lc]);
                                $LMRFeeName = trim($LMRFeeArray[$LMRFeeKey]);

                                if (array_key_exists($moduleCode, $libModulesArray)) {
                                    ?>
                                    <option
                                            value="<?php echo $LMRFeeKey ?>" <?php echo Arrays::isSelectedArray($myLMRFeeIdArray, $LMRFeeKey) ?>><?php echo $LMRFeeName ?></option>
                                    <?php
                                }
                            }
                            ?>
                        </select>
                        <?php
                    } else {

                        $fft = 0;
                        echo '<h5>';
                        for ($ff = 0; $ff < count($LMRFeeIdArray); $ff++) {
                            if (array_key_exists($LMRFeeIdArray[$ff], $LMRFeeArray)) {
                                if ($fft > 0) {
                                    echo ', ';
                                }
                                echo '<b>' . trim($LMRFeeArray[$LMRFeeIdArray[$ff]]) . '</b>';
                                $fft++;
                            }
                        }
                        echo '</h5>';
                    }
                    ?>
                </div>

                <div class="col-md-6">
                    <label class="font-weight-bold">
                        <i class="fa fa-info-circle text-primary tooltipClass" style="text-decoration:none;"
                           title="General Notes is default"></i>
                        <?php

                        if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                            ?>
                            Select the Notes Type(s)
                            <?php
                        } else {
                            ?>
                            Selected Notes Type(s)
                            <?php
                        }
                        ?>
                    </label>
                    <?php
                    if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {

                        ?>
                        &nbsp;<select data-placeholder="Select Notes Type" name="notesType[]" id="notesType"
                                      class="chzn-select" multiple="" style="width:100%;">
                            <?php
                            foreach ($glNotesTypeArray as $dNkey => $dNvalue) {
                                ?>
                                <option
                                        value="<?php echo $dNkey; ?>" <?php if (in_array($dNkey, $notesTypeInfoArray)) echo 'Selected'; ?>><?php echo $dNvalue; ?></option>
                                <?php
                            }
                            ?>
                        </select>
                        <?php
                    } else {
                        $aComma = '';
                        $dNV = '';
                        foreach ($glNotesTypeArray as $dNkey => $dNvalue) {
                            if (in_array($dNkey, $notesTypeInfoArray)) {
                                $dNV .= $aComma . $dNvalue;
                                $aComma = ', ';
                            }
                        }
                        echo '<h5>' . $dNV . '</h5>';
                    }
                    ?>
                </div>
            </div>
        </div>

        <?php
        if ($userRole == 'Super' || $userRole == 'Sales') {
            ?>

            <div class="col-md-6 mb-4">
                <div class="card card-custom">
                    <div class="card-header card-header-tabs-line bg-gray-100  ">
                        <div class="card-title">
                            <h3 class="card-label">
                                Super Admin Permission
                            </h3>
                        </div>
                        <div class="card-toolbar">
                            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                               data-card-tool="toggle"
                               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                                <i class="ki ki-arrow-down icon-nm"></i>
                            </a>
                            <a href="#" class="btn btn-icon btn-sm btn-hover-light-primary mr-1  d-none"
                               data-card-tool="reload"
                               data-toggle="tooltip" data-placement="top" title="" data-original-title="Reload Card">
                                <i class="ki ki-reload icon-nm"></i>
                            </a>
                            <a href="javascript:void(0);"
                               class="tooltipClass btn  btn-light-primary btn-text-primary btn-hover-primary btn-icon ml-2 toggleClass"
                               data-card-tool="toggle"
                               data-section="superadminperm"
                               data-toggle="tooltip" data-placement="top" title="" data-original-title="Toggle Card">
                                <i class="ki ki-arrow-down icon-nm"></i>
                            </a>
                        </div>
                    </div>
                    <div class="card-body superadminperm" style="display:none;">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group row ">
                                    <label class="col-md-6 font-weight-bold"><i
                                                class="fa fa-info-circle text-primary tooltipClass"
                                                style="text-decoration:none;"
                                                title="Theme 2: https://www.theloanpost.com/<br>login/backoffice?<br>PCID=357bdf35e65b715d"></i>Select
                                        Theme</label>
                                    <div class="col-md-6 radio-inline">
                                        <label class="radio radio-solid font-weight-bolder" for="theme1"><input
                                                    class="newCheck" type="radio"
                                                    name="theme"
                                                    id="theme1"
                                                    value="1" <?php echo Strings::isChecked('1', $theme) ?> /><span></span>
                                            Theme 1</label>
                                        <label class="radio radio-solid font-weight-bolder" for="theme2"><input
                                                    class="newCheck" type="radio" name="theme"
                                                    id="theme2"
                                                    value="0" <?php echo Strings::isChecked('0', $theme) ?> /><span></span>
                                            Theme 2</label>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Add E-sign service?</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowESignService == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowESignService ?>" id="esignService"
                                                       onchange="toggleSwitch('userEmail', 'allowESignService','1','0' );"/>
                                                <input type="hidden" name="allowESignService" id="allowESignService"
                                                       value="<?php echo $allowESignService ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Allow to access CallWise</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowToCreateAloware == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowToCreateAloware ?>" id="alowareCreate"
                                                       onchange="toggleSwitch('alowareCreate', 'allowToCreateAloware','1','0' );"/>
                                                <input type="hidden" name="allowToCreateAloware"
                                                       id="allowToCreateAloware"
                                                       value="<?php echo $allowToCreateAloware ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Allow to create Employee</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowToCreateEmployee == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowToCreateEmployee ?>" id="employeeCreate"
                                                       onchange="toggleSwitch('employeeCreate', 'allowToCreateEmployee','1','0' );"/>
                                                <input type="hidden" name="allowToCreateEmployee"
                                                       id="allowToCreateEmployee"
                                                       value="<?php echo $allowToCreateEmployee ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Allow Company to edit
                                        "Approved/unapproved
                                        Lender List"</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowToEditLenderList == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowToEditLenderList ?>" id="editLenderList"
                                                       onchange="toggleSwitch('editLenderList', 'allowToEditLenderList','1','0' );"/>
                                                <input type="hidden" name="allowToEditLenderList"
                                                       id="allowToEditLenderList"
                                                       value="<?php echo $allowToEditLenderList ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Allow users to send Fax</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($sendFax == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $sendFax ?>" id="allowToSendFax"
                                                       onchange="toggleSwitch('allowToSendFax', 'sendFax','1','0' );"/>
                                                <input type="hidden" name="sendFax" id="sendFax"
                                                       value="<?php echo $sendFax ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="separator separator-dashed my-2"></div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Allow to View Fresh Start Submission
                                        Form</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowToViewFSSF == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowToViewFSSF ?>" id="allowToViewFSSForm"
                                                       onchange="toggleSwitch('allowToViewFSSForm', 'allowToViewFSSF','1','0' );"/>
                                                <input type="hidden" name="allowToViewFSSF" id="allowToViewFSSF"
                                                       value="<?php echo $allowToViewFSSF ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <?php
                            if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
                                ?>
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold">Subscribe to HOME Report</label>
                                        <div class="col-md-4">
                                        <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($subscribeToHOME == 1) { ?> checked="checked" <?php } ?>
                                                           value="<?php echo $subscribeToHOME ?>"
                                                           id="subscribeToHOMEForPC"
                                                           onchange="toggleSwitch('subscribeToHOMEForPC', 'subscribeToHOME','1','0' );"/>
                                                    <input type="hidden" name="subscribeToHOME" id="subscribeToHOME"
                                                           value="<?php echo $subscribeToHOME ?>">
                                                    <span></span>
                                                </label>
                                        </span>
                                        </div>
                                    </div>
                                </div>
                                <?php
                                if ($userRole == 'Super') {
                                    if ($subscribeToHOME == 1) {
                                        $homeReportFeeDisp = 'enabled';
                                    } else {
                                        $homeReportFeeDisp = 'disabled';
                                    }
                                    ?>
                                    <div class="col-md-12">
                                        <div class="form-group row">
                                            <label class="col-md-6 font-weight-bold">Home Report Fees </label>
                                            <div class="col-md-6">
                                                <div class="input-group">
                                                    <div class="input-group-prepend">
                                                        <span class="input-group-text">$</span>
                                                    </div>
                                                    <input type="text" name="homeReportFee" id="homeReportFee"
                                                           value="<?php echo $homeReportFee ?>" <?php echo $homeReportFeeDisp ?>
                                                           class="form-control input-sm">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                } else {
                                    echo '<h5>' . $homeReportFee . '</h5>';
                                }
                                ?>
                                <!--                        <div class="form-group row col-md-12">-->
                                <!--                            <label class="form-group row col-md-8 valign-top">Allow to submit loan audit</label>-->
                                <!--                            <div class="col-md-4">-->
                                <!--                                <div --><?php //if ($allowToLASubmitForPC == '0') { ?><!-- class="switch-off" --><?php //} else { ?><!-- class="switch-on" --><?php //} ?>
                                <!--                                        id="LASubmitForPC"-->
                                <!--                                        onclick="toggleSwitch('LASubmitForPC', 'allowToLASubmitForPC', '1', '0' );"></div>-->
                                <!--                                <input type="hidden" name="allowToLASubmitForPC" id="allowToLASubmitForPC"-->
                                <!--                                       value="--><?php //echo $allowToLASubmitForPC ?><!--">-->
                                <!--                            </div>-->
                                <!--                        </div>-->
                                <?php if ($userRole == 'Super') { ?>
                                    <div class="col-md-12 d-none">
                                        <div class="form-group row">
                                            <label class="col-md-8 font-weight-bold">Allow PC to send custom
                                                texts?</label>
                                            <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowPCToSendCustomText == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowPCToSendCustomText ?>"
                                                               id="sendCustomTextForPC"
                                                               onchange="toggleSwitch('sendCustomTextForPC', 'allowPCToSendCustomText','1','0' );"/>
                                                        <input type="hidden" name="allowPCToSendCustomText"
                                                               id="allowPCToSendCustomText"
                                                               value="<?php echo $allowPCToSendCustomText ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group row">
                                            <label class="col-md-8 font-weight-bold">Allow pc submit to
                                                Peerstreet</label>
                                            <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowPeerstreet == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowPeerstreet ?>"
                                                               id="allowPeerstreetSubmit"
                                                               onchange="toggleSwitch('allowPeerstreetSubmit', 'allowPeerstreet','1','0' );"/>
                                                        <input type="hidden" name="allowPeerstreet" id="allowPeerstreet"
                                                               value="<?php echo $allowPeerstreet ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group row">
                                            <input type="hidden" name="AID" id="AID" value="<?php echo $AID ?>">
                                            <label class="col-md-6 font-weight-bold">Choose Auditor</label>
                                            <div class="col-md-6">
                                                <select name="auditor" id="auditor" class="form-control input-sm">
                                                    <option value="">- Select -</option>
                                                    <?php
                                                    for ($k = 0; $k < count($auditorListArray); $k++) {
                                                        ?>
                                                        <option
                                                                value="<?php echo $auditorListArray[$k]['AID'] ?>" <?php echo Arrays::isSelected($auditorListArray[$k]['AID'], $auditors); ?>><?php echo $auditorListArray[$k]['processorName'] . '- ' . $auditorListArray[$k]['email'] ?></option>
                                                    <?php } ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                }
                                if ($userRole == 'Super') {
                                    ?>
                                    <div class="col-md-12">
                                        <div class="form-group row">
                                            <label class="font-weight-bold col-md-6 ">Use SMTP
                                                Server: <i
                                                        class="fa fa-info-circle text-primary tooltipClass"
                                                        style="text-decoration:none;"
                                                        title="You may use your own SMTP EMail settings, which will deliver most email correspondence via your own email ID."></i>
                                            </label>
                                            <div class="radio-list col-md-6 pl-6">
                                                <label class="radio radio-solid font-weight-bold"
                                                       for="serverInfoUserSetting1">
                                                    <input class="newCheck" type="radio"
                                                           name="serverInfoUserSetting"
                                                           id="serverInfoUserSetting1"
                                                           value="0" <?php echo Strings::isChecked($myServerInfoUserSetting, '0') ?>
                                                           onclick="showSMTPInfo('0','SMTPTable');"/><span></span>Use
                                                    my default mail settings? </label>
                                                <label class="radio radio-solid font-weight-bold"
                                                       for="serverInfoUserSetting2"><input
                                                            class="newCheck" type="radio"
                                                            name="serverInfoUserSetting"
                                                            id="serverInfoUserSetting2"
                                                            value="1" <?php echo Strings::isChecked($myServerInfoUserSetting, '1') ?>
                                                            onclick="showSMTPInfo('1','SMTPTable');"/><span></span>
                                                    Let me specify my own SMTP server details?</label>

                                            </div>
                                        </div>
                                        <input type="hidden" name="ESID" id="ESID" value="<?php echo $ESID ?>">
                                    </div>
                                    <div class="col-md-12" id="SMTPTable"
                                         style="display:<?php echo $smtpDisp; ?>;">
                                        <div class="form-group row">
                                            <label class="col-md-6 font-weight-bold">SMTP Hostname:</label>
                                            <div class="col-md-6">
                                                <?php if ($userRole == 'Super') { ?>
                                                    <input class="form-control input-sm" type="text" name="hostName"
                                                           id="hostName" value="<?php echo $hostName ?>" size="40">
                                                <?php } else {
                                                    echo '<h5>' . $hostName . '</h5>';
                                                } ?>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-6 font-weight-bold">SMTP Username:</label>
                                            <div class="col-md-6">
                                                <?php if ($userRole == 'Super') { ?>
                                                    <input class="form-control input-sm" type="text" name="userName"
                                                           id="userName" value="<?php echo $userName ?>" size="40">
                                                <?php } else {
                                                    echo '<h5>' . $userName . '</h5>';
                                                } ?>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-6 font-weight-bold">SMTP Password:</label>
                                            <div class="col-md-6">
                                                <?php if ($userRole == 'Super') { ?>
                                                    <input class="form-control input-sm" type="password" name="pwd"
                                                           id="pwd"
                                                           value="<?php echo $pwd ?>">
                                                <?php } else {
                                                    echo '<h5>XXXXXX</h5>';
                                                } ?>
                                            </div>
                                            <div class="col-md-4 col-xs-6">
                                                <?php if ($userRole == 'Super') {
                                                } else {
                                                    echo '<h4>If You Want To Change Please Contact Admin</h4>';
                                                } ?>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-6 font-weight-bold">SMTP Port:</label>
                                            <div class="col-md-6">
                                                <?php if ($userRole == 'Super') { ?>
                                                    <input class="form-control input-sm" type="text" name="portNo"
                                                           id="portNo" value="<?php echo $portNo ?>">
                                                <?php } else {
                                                    echo '<h5>' . $portNo . '</h5>';
                                                } ?>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-6 font-weight-bold">Reply To:</label>
                                            <div class="col-md-6">
                                                <?php if ($userRole == 'Super') { ?>
                                                    <input class="form-control input-sm" type="text" name="replyTo"
                                                           id="replyTo" value="<?php echo $replyTo ?>" size="40">
                                                <?php } else {
                                                    echo '<h5>' . $replyTo . '</h5>';
                                                } ?>
                                            </div>
                                        </div>
                                        <div class="form-group row">
                                            <label class="col-md-6 font-weight-bold">Bounce Mail:</label>
                                            <div class="col-md-6">
                                                <?php if ($userRole == 'Super') { ?>
                                                    <input class="form-control input-sm" type="text" name="bounceMail"
                                                           id="bounceMail" value="<?php echo $bounceMail ?>" size="40">
                                                    <a
                                                            href="javascript:testSMTPSetting()">Test SMTP
                                                        Setting</a>
                                                <?php } else {
                                                    echo '<h5>' . $bounceMail . '</h5>';
                                                } ?>
                                            </div>
                                            <div class="form-group row">
                                                <?php if ($userRole == 'Super') { ?>
                                                    <div id="smtpSetting" class="note" style="float:left"></div>
                                                <?php } ?>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                    if ($userRole == 'Super' || $userRole == 'Sales' || $publicUser) {
                                        ?>
                                        <div class="col-md-12">
                                            <div class="form-group row">
                                                <label class="col-md-6 font-weight-bold">Sales
                                                    Representative</label>
                                                <div class="col-md-6">
                                                    <select name="salesRep" id="salesRep"
                                                            class="mandatory form-control input-sm">
                                                        <option value="">- Select -</option>
                                                        <?php
                                                        $salesRepKeyArray = array_keys($glSalesRepresentativeArray);
                                                        for ($sr = 0; $sr < count($salesRepKeyArray); $sr++) {
                                                            $salesRepKey = $salesRepKeyArray[$sr];
                                                            $salesRep = $glSalesRepresentativeArray[$salesRepKey];
                                                            if ($salesRep == 'none') $salesRep = 'Self Serve';
                                                            ?>
                                                            <option
                                                                    value="<?php echo $salesRepKey ?>" <?php echo Arrays::isSelected($salesRepKey, $mySalesRep); ?>><?php echo $salesRep ?></option>
                                                        <?php } ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-12">
                                            <div class="form-group row">
                                                <label class="col-md-6 font-weight-bold">CSM
                                                    Representative</label>
                                                <div class="col-md-6">
                                                    <select name="CSMRep" id="CSMRep"
                                                            class="mandatory form-control input-sm">
                                                        <option value="">- Select -</option>
                                                        <?php
                                                        $CSMRepKeyArray = array_keys($glCSMRepresentativeArray);
                                                        for ($sr = 0; $sr < count($CSMRepKeyArray); $sr++) {
                                                            $CSMRepKey = $CSMRepKeyArray[$sr];
                                                            $CSMRep = $glCSMRepresentativeArray[$CSMRepKey];
                                                            ?>
                                                            <option
                                                                    value="<?php echo $CSMRepKey ?>" <?php echo Arrays::isSelected($CSMRepKey, $myCSMRep); ?>><?php echo $CSMRep ?></option>
                                                        <?php } ?>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                        <?php
                                        if ($userRole == 'Super' || $userRole == 'Sales') {
                                            ?>
                                            <div class="col-md-12">
                                                <div class="form-group row">
                                                    <label class="col-md-6 font-weight-bold">Subscriber ID</label>
                                                    <div class="col-md-6">
                                                        <input class="form-control input-sm" type="text"
                                                               name="subscriberID"
                                                               id="subscriberID" value="<?php echo $subscriberID ?>"
                                                               size="30"
                                                               maxlength="25">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-12">
                                                <div class="form-group row">
                                                    <label class="col-md-6 font-weight-bold">Customer ID</label>
                                                    <div class="col-md-6">
                                                        <input class="form-control input-sm" type="text"
                                                               name="customerID"
                                                               id="customerID" value="<?php echo $customerID ?>"
                                                               size="30"
                                                               maxlength="25">
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                        }
                                    }
                                } else {
                                    ?>
                                    <input type="hidden" name="useMyNameAndEmail" id="useMyNameAndEmail"
                                           value="<?php echo $useMyNameAndEmail ?>">
                                    <?php
                                }
                                if ($userRole == 'Super' || $userRole == 'Sales') {
                                    ?>
                                    <input type="hidden" name="allowToOnOffAgentLogin" id="allowToOnOffAgentLogin"
                                           value="<?php echo $allowToOnOffAgentLogin ?>">

                                    <?php
                                } else {
                                    ?>
                                    <input type="hidden" name="allowToOnOffAgentLogin" id="allowToOnOffAgentLogin"
                                           value="<?php echo $allowToOnOffAgentLogin ?>">
                                    <?php
                                }

                                if (($userRole == 'Super') || ($userRole == 'Manager' || $userRole == 'Sales')) {
                                    ?>
                                    <div class="col-md-12">
                                        <div class="form-group row">
                                            <label class="col-md-8 font-weight-bold">BO users/branch/Loan
                                                Officer/Mortgage
                                                Broker should be able to upgrade/downgrade DIY clients
                                                sometimes?</label>
                                            <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowUBAToUpAndDowngradeClient == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowUBAToUpAndDowngradeClient ?>"
                                                               id="allowUBAToUpgradeClient"
                                                               onchange="toggleSwitch('allowUBAToUpgradeClient', 'allowUBAToUpAndDowngradeClient','1','0' );"/>
                                                        <input type="hidden" name="allowUBAToUpAndDowngradeClient"
                                                               id="allowUBAToUpAndDowngradeClient"
                                                               value="<?php echo $allowUBAToUpAndDowngradeClient ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                            </div>
                                        </div>
                                    </div>
                                    <?php
                                } else {
                                    echo "<input type=\"hidden\" name=\"allowUBAToUpAndDowngradeClient\" id=\"allowUBAToUpAndDowngradeClient\" value=\"" . $allowUBAToUpAndDowngradeClient . "\">";
                                }

                            }
                            ?>

                            <?php if ($userRole == 'Super') { ?>
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold"> Purchase Website or
                                            hosting?</label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($isWebsite == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $isWebsite ?>"
                                                               id="website"
                                                               onchange="toggleSwitch('website', 'isWebsite','1','0' );"/>
                                                        <input type="hidden" name="isWebsite"
                                                               id="isWebsite"
                                                               value="<?php echo $isWebsite ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>

                            <div class="form-group row col-md-12">
                                <label class="font-weight-bold col-md-4">HubSpot Link</label>
                                <div class="col-md-8">
                                    <input type="text" name="zohoLink" id="zohoLink" placeholder="https://"
                                           value="<?php echo $zohoLink ?>" maxlength="120" autocomplete="off"
                                           class="form-control input-sm mandatory">
                                </div>
                            </div>
                            <div class="form-group row col-md-12">
                                <label class="font-weight-bold col-md-4">Drive</label>
                                <div class="col-md-8">
                                    <input type="text" name="drive" id="drive" placeholder="https://"
                                           value="<?php echo $drive ?>" maxlength="120" autocomplete="off"
                                           class="form-control input-sm mandatory">
                                </div>
                            </div>
                            <div class="form-group row col-md-12">
                                <label class="font-weight-bold col-md-4">GHL</label>
                                <div class="col-md-8">
                                    <input type="text" name="GHL" id="GHL" placeholder="https://"
                                           value="<?php echo $GHL ?>" maxlength="500" autocomplete="off"
                                           class="form-control input-sm mandatory">
                                </div>
                            </div>
                            <div class="form-group row col-md-12">
                                <label class="font-weight-bold col-md-4">Captcha</label>
                                <div class="col-md-8">
                                    <select name="captcha" id="captcha"
                                            class="form-control input-sm">
                                        <option value="">- Select -</option>
                                        <?php
                                        foreach (glCaptcha::$glCaptcha as $key => $value) {
                                            ?>
                                            <option
                                                    value="<?php echo $key ?>" <?php echo Arrays::isSelected($key, $captcha); ?>><?php echo $value ?></option>
                                        <?php } ?>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group row col-lg-12 m-0 mb-4 px-0" id="featureFlagsDiv">
                                <label class="bg-secondary py-2 col-lg-12"><b>Feature Flags</b></label>
                            </div>
                            <?php if ($userRole == 'Super') { ?>
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold"> Soft pull</label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($pcAcqualifyStatus == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $pcAcqualifyStatus ?>"
                                                               id="pcAcqualifyChekcbox"
                                                               onchange="toggleSwitch('pcAcqualifyChekcbox', 'pcAcqualifyStatus','1','0' );"/>
                                                        <input type="hidden" name="pcAcqualifyStatus"
                                                               id="pcAcqualifyStatus"
                                                               value="<?php echo $pcAcqualifyStatus ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <!-- Enable/Disable Automation -->
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold">Workflow Automation</label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($allowAutomation == 1) {
                                                        echo 'checked=checked';
                                                    } ?> value="<?php echo $allowAutomation; ?>"
                                                           id="pcAutomationCheckbox"
                                                           onchange="toggleSwitch('pcAutomationCheckbox', 'allowAutomation','1','0' );"/>
                                                    <input type="hidden" name="allowAutomation" id="allowAutomation"
                                                           value="<?php echo $allowAutomation; ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <!-- // Enable/Disable Automation // -->
                            <?php } ?>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Create Branches</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowToCreateBranch == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowToCreateBranch ?>" id="branchCreate"
                                                       onchange="toggleSwitch('branchCreate', 'allowToCreateBranch','1','0' );"/>
                                                <input type="hidden" name="allowToCreateBranch" id="allowToCreateBranch"
                                                       value="<?php echo $allowToCreateBranch ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <?php if ($userRole == 'Super') { ?>
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold"> Webform to PDF</label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowWebformToPDF == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowWebformToPDF ?>"
                                                               id="allowWebformToPDFCheckbox"
                                                               onchange="toggleSwitch('allowWebformToPDFCheckbox', 'allowWebformToPDF','1','0' );"/>
                                                        <input type="hidden" name="allowWebformToPDF"
                                                               id="allowWebformToPDF"
                                                               value="<?php echo $allowWebformToPDF ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold"> Address Lookup</label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowPropertyAddressAutoLookUp == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowPropertyAddressAutoLookUp ?>"
                                                               id="allowPropertyAddressAutoLookUpCheckbox"
                                                               onchange="toggleSwitch('allowPropertyAddressAutoLookUpCheckbox', 'allowPropertyAddressAutoLookUp','1','0' );"/>
                                                        <input type="hidden" name="allowPropertyAddressAutoLookUp"
                                                               id="allowPropertyAddressAutoLookUp"
                                                               value="<?php echo $allowPropertyAddressAutoLookUp ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold"> 3rd Party
                                            Services Tab</label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($thirdPartyServices == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $thirdPartyServices ?>"
                                                               id="thirdPartyLink"
                                                               onchange="toggleSwitch('thirdPartyLink', 'thirdPartyServices','1','0' );"/>
                                                        <input type="hidden" name="thirdPartyServices"
                                                               id="thirdPartyServices"
                                                               value="<?php echo $thirdPartyServices ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-6 font-weight-bold">IP address lookup<i
                                                    class="fa fa-info-circle text-primary tooltipClass"
                                                    style="text-decoration:none;"
                                                    title="Seperate the IP addresses with comma."></i>
                                        </label>
                                        <div class="col-md-6">
                                        <textarea name="allowIPAccess" id="allowIPAccess"
                                                  class="form-control"><?php echo $allowIPAccess ?></textarea>
                                            <br><b>Use <font color="#ee0000">,</font> (comma) to seperate IP
                                                addresses</b>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold">Open API- Lead Posting</label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($leadPostingAPI == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $leadPostingAPI ?>"
                                                               id="leadPostingViaAPI"
                                                               onchange="toggleSwitch('leadPostingViaAPI', 'leadPostingAPI','1','0' );"/>
                                                        <input type="hidden" name="leadPostingAPI" id="leadPostingAPI"
                                                               value="<?php echo $leadPostingAPI ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold"> Pricing Engine</label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($pcPriceEngineStatus == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $pcPriceEngineStatus ?>"
                                                               id="pcPriceEngineStatusCheckbox"
                                                               onchange="toggleSwitch('pcPriceEngineStatusCheckbox', 'pcPriceEngineStatus','1','0' );"/>
                                                        <input type="hidden" name="pcPriceEngineStatus"
                                                               id="pcPriceEngineStatus"
                                                               value="<?php echo $pcPriceEngineStatus ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Servicing 2.0</label>
                                    <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                <label>
                                                    <input class="form-control"
                                                           type="checkbox" <?php if ($allowServicing == 1) {
                                                        echo 'checked=checked';
                                                    } ?> value="<?php echo $allowServicing; ?>"
                                                           id="allowServicingCheckbox"
                                                           onchange="toggleSwitch('allowServicingCheckbox', 'allowServicing','1','0' );"/>
                                                    <input type="hidden" name="allowServicing" id="allowServicing"
                                                           value="<?php echo $allowServicing; ?>">
                                                    <span></span>
                                                </label>
                                            </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Custom Fields</label>
                                    <div class="col-md-4">
                                        <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($allowCustomFields == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $allowCustomFields ?>"
                                                       id="allowCustomFieldsCheck"
                                                       onchange="toggleSwitch('allowCustomFieldsCheck', 'allowCustomFields','1','0' );"/>
                                                <input type="hidden" name="allowCustomFields" id="allowCustomFields"
                                                       value="<?php echo $allowCustomFields ?>">
                                                <span></span>
                                            </label>
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Private Label</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($isPLO == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $isPLO ?>" id="plo"
                                                       onchange="toggleSwitch('plo', 'isPLO','1','0' );processingCompany.toggleSwitchField_PC('plo','vhostDiv');"/>
                                                <input type="hidden" name="isPLO" id="isPLO"
                                                       value="<?php echo $isPLO ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Allow to send bulk E-mailing</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($emailCampaign == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $emailCampaign ?>" id="allowToEmailCompaign"
                                                       onchange="toggleSwitch('allowToEmailCompaign', 'emailCampaign','1','0' );"/>
                                                <input type="hidden" name="emailCampaign" id="emailCampaign"
                                                       value="<?php echo $emailCampaign ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <?php if ($userRole == 'Super') { ?>
                                <div class="col-md-12">
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold"> Enable Marketplace & Offers Tab
                                            <i class="fa fa-info-circle text-primary tooltipClass"
                                               style="text-decoration:none;"
                                               title="If Yes, the marketplace tab will be on. PC can create loan programs that will display in their private marketplace only and they will see all the public loan programs. "></i></label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowPCToMarketPlace == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowPCToMarketPlace ?>"
                                                               id="allowPCToMarketPlaceForm"
                                                               onchange="toggleSwitch('allowPCToMarketPlaceForm', 'allowPCToMarketPlace','1','0' );processingCompany.showMarketplaceDiv();"/>
                                                        <input type="hidden" name="allowPCToMarketPlace"
                                                               id="allowPCToMarketPlace"
                                                               value="<?php echo $allowPCToMarketPlace ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 showMarketPlaceChildDiv" <?php if ($allowPCToMarketPlace == '0') { ?> style="display: none;" <?php } ?>>
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold">  Enable MarketPlace Public Listings
                                            <i class="fa fa-info-circle text-primary tooltipClass"
                                               style="text-decoration:none;"
                                               title="If Yes, this account will publish their marketplace loan categories for all accounts that have Marketplace feature on."></i></label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowPCToMarketPlacePublic == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowPCToMarketPlacePublic ?>"
                                                               id="allowPCToMarketPlacePublicForm"
                                                               onchange="toggleSwitch('allowPCToMarketPlacePublicForm', 'allowPCToMarketPlacePublic','1','0' );"/>
                                                        <input type="hidden" name="allowPCToMarketPlacePublic"
                                                               id="allowPCToMarketPlacePublic"
                                                               value="<?php echo $allowPCToMarketPlacePublic ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12 showMarketPlaceChildDiv" <?php if ($allowPCToMarketPlace == '0') { ?> style="display: none;" <?php } ?>>
                                    <div class="form-group row">
                                        <label class="col-md-8 font-weight-bold"> Allow this account to Access Public
                                            Loan Programs?
                                            <i class="fa fa-info-circle text-primary tooltipClass"
                                               style="text-decoration:none;"
                                               title="If Yes, this account can access all Public Loan Programs. If No, this account can access internal Loan Programns only"></i></label>
                                        <div class="col-md-4">
                                            <span class="switch switch-icon">
                                                    <label>
                                                        <input class="form-control"
                                                               type="checkbox" <?php if ($allowPCToAccessPublicMarketPlaceLoanPrograms == 1) { ?> checked="checked" <?php } ?>
                                                               value="<?php echo $allowPCToAccessPublicMarketPlaceLoanPrograms ?>"
                                                               id="allowPCToAccessPublicMarketPlaceLoanProgramsForm"
                                                               onchange="toggleSwitch('allowPCToAccessPublicMarketPlaceLoanProgramsForm', 'allowPCToAccessPublicMarketPlaceLoanPrograms','1','0' );"/>
                                                        <input type="hidden"
                                                               name="allowPCToAccessPublicMarketPlaceLoanPrograms"
                                                               id="allowPCToAccessPublicMarketPlaceLoanPrograms"
                                                               value="<?php echo $allowPCToAccessPublicMarketPlaceLoanPrograms ?>">
                                                        <span></span>
                                                    </label>
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            <?php } ?>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Doc Wizard</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($docWizard == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $docWizard ?>" id="allowDocWizard"
                                                       onchange="toggleSwitch('allowDocWizard', 'docWizard','1','0' );"/>
                                                <input type="hidden" name="docWizard" id="docWizard"
                                                       value="<?php echo $docWizard ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">VIP Support</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($VIPSupport == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $VIPSupport ?>" id="allowVIPSupport"
                                                       onchange="toggleSwitch('allowVIPSupport', 'VIPSupport','1','0' );"/>
                                                <input type="hidden" name="VIPSupport" id="VIPSupport"
                                                       value="<?php echo $VIPSupport ?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group row">
                                    <label class="col-md-8 font-weight-bold">Enable Draw Management?</label>
                                    <div class="col-md-4">
                                    <span class="switch switch-icon">
                                            <label>
                                                <input class="form-control"
                                                       type="checkbox" <?php if ($drawManagement == 1) { ?> checked="checked" <?php } ?>
                                                       value="<?php echo $drawManagement ?>" id="allowDrawManagement"
                                                       onchange="toggleSwitch('allowDrawManagement', 'drawManagement','1','0' );"/>
                                                <input type="hidden" name="drawManagement" id="drawManagement"
                                                       value="<?php echo $drawManagement?>">
                                                <span></span>
                                            </label>
                                    </span>
                                    </div>
                                </div>
                            </div>
                            <!-- need to add above-->
                        </div>
                    </div>
                </div>
            </div>
            <?php
        } else {
            ?>
            <input type="hidden" name="allowToCreateBranch" id="allowToCreateBranch"
                   value="<?php echo $allowToCreateBranch ?>">
            <input type="hidden" name="allowToCreateEmployee" id="allowToCreateEmployee"
                   value="<?php echo $allowToCreateEmployee ?>">
            <input type="hidden" name="allowESignService" id="allowESignService"
                   value="<?php echo $allowESignService ?>">
            <input type="hidden" name="allowToOnOffAgentLogin" id="allowToOnOffAgentLogin"
                   value="<?php echo $allowToOnOffAgentLogin ?>">
            <input type="hidden" name="allowUBAToUpAndDowngradeClient" id="allowUBAToUpAndDowngradeClient"
                   value="<?php echo $allowUBAToUpAndDowngradeClient ?>">
            <input type="hidden" name="allowToViewFSSF" id="allowToViewFSSF" value="<?php echo $allowToViewFSSF ?>">

            <input type="hidden" name="AID" id="AID" value="<?php echo $AID ?>">
            <input type="hidden" name="auditor" id="auditor" value="<?php echo $auditors ?>">
            <?php
        }
        ?>
        <input type="hidden" name="companyGoals" id="companyGoals" value="<?php echo $companyGoals ?>">

        <?php
        if ($userRole == 'Manager' || $userRole == 'Super' || $userRole == 'Sales') {
            ?>
            <div class="clear"></div>
            <div class="col-md-12" style="text-align: center;">
                <input class="btn btn-primary" type="submit" name="save" id="save" value="Save">
                <input class="btn btn-primary" type="submit" name="saveProcessingCompany" id="saveProcessingCompany"
                       value="Save & Next">
            </div>
            <?php
        }
        ?>

    </div>
</form>


<script type='text/javascript'>
    <?php
    if ($tabNumb == 1) {
    ?>
    function showAndHideModuleServiceTypesForPC(t, val, opt) {
        console.log({
            func: 'showAndHideModuleServiceTypesForPC',
            t: t,
            val: val,
            opt: opt,
        })
        if (t === 'moduleType[]') {
            if (opt === 'show') {
                $('#div_' + val).show();
            } else {
                $('#div_' + val).hide();
            }
        }
    }

    $(document).ready(function () {
        $(".serviceDiv")
            .click(function () {
                var MC = '';
                MC = $(this).attr("id");
                MC = MC.replace("div1_", "");
                $("#subDiv_" + MC).toggle('fast');
                $(this).not(".caret").children(".fa").toggleClass("fa-minus-square fa-plus-square");
                $(this, ".caret").children(".fa").toggleClass("fa-caret-right fa-caret-down");
                return false;
            });

        $(".companyDetails").click(function () {
            $("#subCompanyInfo").toggle('fast');
            $(this).not(".caret").children(".fa").toggleClass("fa-plus-square fa-minus-square");
            $(this, ".caret").children(".fa").toggleClass("fa-caret-right fa-caret-down");
            return false;
        });

        $(".companySettings")
            .click(function () {
                $("#subCompanySet").toggle('fast');
                $(this).not(".caret").children(".fa").toggleClass("fa-minus-square fa-plus-square");
                $(this, ".caret").children(".fa").toggleClass("fa-caret-right fa-caret-down");
                return false;
            });

        $(".loanServicing")
            .click(function () {
                $("#subLoanServicing").toggle('fast');
                $(this).not(".caret").children(".fa").toggleClass("fa-minus-square fa-plus-square");
                $(this, ".caret").children(".fa").toggleClass("fa-caret-right fa-caret-down");
                return false;
            });

        $(".superAdmin")
            .click(function () {
                $("#subSuperAdmin").toggle('fast');
                $(this).not(".caret").children(".fa").toggleClass("fa-minus-square fa-plus-square");
                $(this, ".caret").children(".fa").toggleClass("fa-caret-right fa-caret-down");
                return false;
            });
    });

    function privateLabelChange() {
        console.log({
            func: 'privateLabelChange',
        });

        var len = 0, k = 0, obj = '', isPLO = '';
        var ks = 0;
        isPLO = document.processingCompanyForm.isPLO ? eval(document.processingCompanyForm.isPLO.value) : null;
        //var HMLODefaultFee  = ['1', '87', '96', '103', '104', '47', '97', '98', '100', '102'];
        var HMLODefaultFee = <?php echo $defaultSelBillingfeeID; ?>;
        var HMLODefaultNotesType = <?php echo $defaultNotesTypeID; ?>;
        var LMDefaultFee = ['1', '2', '5'];
        var LMDefaultNotesType = ['GE', 'CL', 'LN', 'WFN', 'BN', 'AN', 'RN', 'CALN', 'TLN', 'BGN', 'CHKN', 'BK', 'SN'];
        var defaultLoanPgm = '';
        try {
            eval("obj = document.processingCompanyForm['moduleType[]']");
            len = obj.length;
        } catch (e) {
        }

        for (var i = 0; i < obj.length; i++) {
            if (obj[i].selected) {
                if (obj[i].value == 'HMLO') {
                    $("#LMRFee").val(HMLODefaultFee);
                    $("#LMRFee").trigger("chosen:updated");
                    $("#notesType").val(HMLODefaultNotesType);
                    $("#notesType").trigger("chosen:updated");
                    clickToAllServiceFields('checked', 'HMLO', 'LW', defaultLoanPgm);
                    if (isPLO == 0) {
                        toggleSwitch('plo', 'isPLO', '1', '0');
                    }
                } else {
                    $("#LMRFee").val(LMDefaultFee);
                    $("#LMRFee").trigger("chosen:updated");
                    $("#notesType").val(LMDefaultNotesType);
                    $("#notesType").trigger("chosen:updated");
                }
                ks++;
            }
        }
        if (ks > 1) {
            $('.selectAllHideShow').show();
        } else {
            $('.selectAllHideShow').show();
        }
    }
    <?php
    }
    ?>

    $(document).on('click', '.text-primary .panel-title span.clickable', function (e) {
        var $this = $(this);
        if (!$this.hasClass('panel-collapsed')) {
            $this.parents('.panel').find('.panel-body').slideUp();
            $this.addClass('panel-collapsed');
            $this.find('i').removeClass('glyphicon-chevron-down').addClass('glyphicon-chevron-up');
        } else {
            $this.parents('.panel').find('.panel-body').slideDown();
            $this.removeClass('panel-collapsed');
            $this.find('i').removeClass('glyphicon-chevron-up').addClass('glyphicon-chevron-down');
        }
        /* validate for pc upload logo */
        var _URL = window.URL || window.webkitURL;
        $("#logoName").change(function (e) {
            var file, img;
            if ((file = this.files[0])) {
                img = new Image();
                img.onload = function () {
                    if (this.width > 25000 || this.height > 15000) {  /* i added a power of 100 for now to stop this */
                        toastrNotification('The Image size should be 150px tall by 250px wide', 'error');
                        $("#logoName").val('');
                        return false;
                    } else {
                        return true;
                    }
                };
                img.src = _URL.createObjectURL(file);
            }
        });
    });
</script>
<!-- processingCompanyForm.php -->
