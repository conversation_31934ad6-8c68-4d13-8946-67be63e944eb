<?php
use models\portals\PublicPage;
use models\JSCompiler;
use models\standard\Strings;
use models\composite\oDrawManagement\SowTemplateManager;
use models\composite\oDrawManagement\DrawRequestManager;
use models\composite\oDrawManagement\DrawRequest;
use models\cypher;
use models\Request;

session_start();
require '../includes/util.php';
$userType = 'borrower';
$pcid = Request::GetClean('pcid') ?? 0;
if (!is_numeric($pcid)) {
    $pcid = cypher::myDecryption($pcid);
}
$LMRId = Request::GetClean('lmrid');
if (!is_numeric($LMRId)) {
    $LMRId = (int)cypher::myDecryption($LMRId);
}
$templateManager = SowTemplateManager::forProcessingCompany($pcid);
$templateData = $templateManager->getTemplateDataArray();

$drawRequestManager = DrawRequestManager::forLoanFile($LMRId);
$drawRequest = $drawRequestManager->getDrawRequest();

if($drawRequest) {
    switch($drawRequest->status) {
        case DrawRequest::STATUS_PENDING:
            $displayStatus = $drawRequest->isDrawRequest ? 'Your draw request has been submitted and is awaiting approval by the lender.' : 'Your scope of work has been submitted and is awaiting approval by the lender.';
            $displayStatusClass = 'alert-warning';
            break;
        case DrawRequest::STATUS_APPROVED:
            $displayStatus = $drawRequest->isDrawRequest ? 'Draw request approved.' : 'Scope of work approved.';
            $displayStatusClass = 'alert-success';
            break;
        case DrawRequest::STATUS_REJECTED:
            $displayStatus = 'Please see revision requests under lender notes, revise, and resubmit.';
            $displayStatusClass = 'alert-danger';
            break;
        }
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <link href="/assets/images/favicon-whitelabel.png" rel="SHORTCUT ICON" />
    <title> Submit/Revise Scope of Work</title>
    <?php
    PublicPage::Init();
    echo JSCompiler::scripts();
    echo JSCompiler::stylesheets();
    Strings::includeMyScript([
        '/assets/js/drawManagement/utils/ApiClient.js',
        '/assets/js/drawManagement/utils/DataMapper.js',
        '/assets/js/drawManagement/utils/Validator.js',
    ]);
    if(!$drawRequest->isDrawRequest) {
        Strings::includeMyScript([
            '/assets/js/drawManagement/common.js',
            '/assets/js/drawManagement/borrower.js',
            '/assets/js/3rdParty/sortable/Sortable.min.js'
        ]);
        Strings::includeMyCSS(['/assets/css/components/drawManagement.css']);
    }
    ?>
</head>

<body translate="no">

<style>
    /* Base styling */
    html {
        font-size: 12px;
    }

    body {
        background-color: #f8f9fa;
        font-size: 1.2rem;
    }

    /* Table styling */
    .table th {
        font-size: 1rem;
    }

    .table th,
    .table td {
        vertical-align: middle;
        border: none !important;
    }

    .table td {
        border-bottom: 1px solid #dee2e6 !important;
    }

    .table td:first-child {
        white-space: nowrap;
    }

    .table td:first-child > * {
        display: inline-block;
        vertical-align: middle;
    }

    tr > th {
        background: #e1f0ff;
    }

    .category-header td {
        font-weight: 600;
    }

    tr.rejected {
        background-color: #f2dede;
        border-left: 2px solid red;
        border-bottom: 2px solid #fff;
    }

    /* Card styling */
    .card,
    .line-item-table {
        -webkit-box-shadow: 0 0 30px 0 rgba(82, 63, 105, .05);
        box-shadow: 0 0 30px 0 rgba(82, 63, 105, .05);
        border: 0;
    }

    .card {
        padding: 0 !important;
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid #f1f1f1;
    }

    /* Typography and colors */
    .text-success {
        color: #1bc5bd !important;
    }

    .text-muted {
        color: #ccc !important;
    }

    .font-weight-bold {
        font-weight: 400 !important;
    }

    .categories-container i,
    .content-step-line-items i {
        color: #b5b5c3;
    }

    .line-item-display {
        font-size: 1.1rem;
        padding-left: 5px;
    }

    .percentage {
        padding: 0.5rem;
        font-weight: 600;
        font-size: 0.9rem;
        color: #fff;
        text-align: center;
        text-shadow: 0px 0px 2px rgba(0, 0, 0, 0.5);
    }

    /* Notes and popover system */
    .note-container,
    .popup-container {
        position: relative;
        display: inline-block;
    }

    .note-btn {
        color: #3699ff;
        border: none;
        background: transparent;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .note-btn[data-note=""] {
        color: #ccc !important;
    }

    .note-btn:hover {
        transform: scale(1.1);
    }

    .note-btn:focus {
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    .popover {
        position: absolute;
        width: max-content;
        left: 100%;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 10px;
        background-color: #fff;
        color: #212529;
        border: 1px solid #d3d3d3;
        border-radius: 0.25rem;
        padding: 0.5rem 0.75rem;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
        z-index: 1060;
        display: none;
        font-size: 0.875rem;
        white-space: pre-line;
    }

    .popover::before {
        content: "";
        position: absolute;
        top: 50%;
        right: 100%;
        transform: translateY(-50%);
        border-width: 0.4rem;
        border-style: solid;
        border-color: transparent #fff transparent transparent;
    }

    .popover::after {
        content: "";
        position: absolute;
        top: 50%;
        right: 100%;
        transform: translateY(-50%);
        border-width: 0.4rem;
        border-style: solid;
        border-color: transparent #d3d3d3 transparent transparent;
        margin-right: 1px;
        z-index: -1;
    }

    /* Show popover on hover */
    .note-container:hover .popover,
    .popup-container:hover .popover {
        display: block;
    }

    /* Hide popover if empty */
    .note-container .popover:empty,
    .popup-container .popover:empty {
        display: none;
    }

    /* Form validation */
    .is-invalid {
        border-color: #dc3545;
        box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    }

    .validation-message {
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    /* Toast positioning */
    #toast-container {
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
    }
</style>

    <div class="container mt-4 mb-5">
        <!-- Main Content Card -->
        <div class="card p-4 p-md-5 shadow-sm">
            <?php if($drawRequest->sowApproved) {
                require 'drawRequest.php';
            } else {
                require '../backoffice/drawManagement/partials/_draw-management-card.php';
            }
            ?>

            <input type="hidden" id="lmrid" value="<?php echo htmlspecialchars($_REQUEST['lmrid'] ?? ''); ?>">
        </div>
        <?php
            require('../backoffice/drawManagement/loanFile/drawHistory.php');
        ?>
    </div>
    <?php if(!$drawRequest->isDrawRequest): ?>
    <script>
        $(document).ready(function() {
            DrawManagement.init(`<?=$userType ?>`, `<?= json_encode($templateData) ?>`);
        });
    </script>
    <?php endif; ?>
</body>

</html>
