<?php
use models\composite\oDrawManagement\DrawRequest;
use models\standard\Strings;

$categoriesData = [];
$allowEdit = false;
if($drawRequest) {
    $drawRequestData = $drawRequest->toArray();
    $categoriesData = $drawRequestData['categories'] ?? [];
    $allowEdit = !$drawRequest->isDrawRequest || $drawRequest->status !== DrawRequest::STATUS_PENDING;
} else {
    return;
}
Strings::includeMyScript([
    '/assets/js/drawManagement/drawRequest.js'
]);
if($allowEdit) Strings::includeMyScript([
    '/assets/js/drawManagement/borrowerDrawRequest.js'
]);
?>

<div class="card-body">
    <?php if(isset($displayStatus)) { ?>
        <div class="alert <?= $displayStatusClass; ?>" role="alert">
            <i class="fas fa-info-circle"></i>
            <?= $displayStatus; ?>
        </div>
    <?php } ?>
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <h4 class="mb-0 text-success">Manage Draw Request</h4>
        </div>
    </div>

    <!-- Draw Request Table -->
    <?php if (!empty($categoriesData)): ?>
        <table class="table line-item-table table-bordered">
            <thead class="thead-light">
                <colgroup>
                    <col style="width:20%">
                    <col style="width:15%">
                    <col style="width:15%">
                    <col style="width:15%">
                    <col style="width:10%">
                    <col style="width:30%">
                    <col style="width:8%">
                    <col style="width:9%">
                </colgroup>
                <tr>
                    <th scope="col">Line Item</th>
                    <th scope="col">Total Budget</th>
                    <th scope="col">Completed Renovations</th>
                    <th scope="col">Disbursed Amount</th>
                    <th scope="col">% Completed</th>
                    <th scope="col">Request Amount (% | $)</th>
                    <th scope="col">Notes</th>
                    <th scope="col">Lender Notes</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($categoriesData as $category): ?>
                    <?php if (!empty($category['lineItems'])): ?>
                        <!-- Category Header -->
                        <tr class="category-header">
                            <td colspan="8" class="bg-light">
                                <?= htmlspecialchars(strtoupper($category['categoryName'])) ?>
                                <?php if (!empty($category['description'])): ?>
                                    <div class="popup-container">
                                        <i class="fa fa-info-circle text-primary ml-2"><span class="text"></span></i>
                                        <div class="popover"><?= htmlspecialchars($category['description']) ?></div>
                                    </div>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <!-- Line Items -->
                        <?php foreach ($category['lineItems'] as $lineItem): ?>
                            <?php
                                $rejected = false;
                                $rejected = $drawRequest->status === DrawRequest::STATUS_REJECTED && ($lineItem['rejectReason'] !== '' || $lineItem['lenderNotes'] !== '');
                            ?>
                            <tr class="line-item <?= $rejected ? 'rejected' : '' ?>">
                                <td>
                                    <?= htmlspecialchars($lineItem['name']) ?>
                                    <?php if (!empty($lineItem['description'])): ?>
                                        <div class="popup-container">
                                            <i class="fa fa-info-circle text-primary ml-2"><span class="text"></span></i>
                                            <div class="popover"><?= htmlspecialchars($lineItem['description']) ?></div>
                                        </div>
                                    <?php endif; ?>
                                </td>
                                <td>$<?= number_format($lineItem['cost'], 2) ?></td>
                                <td>$<?= number_format($lineItem['completedAmount'], 2) ?></td>
                                <td>$<?= number_format($lineItem['disbursedAmount'], 2) ?></td>
                                <td>
                                    <span class="badge percentage"><?= round($lineItem['completedPercent']) ?>%</span>
                                </td>
                                <td>
                                    <?php if($allowEdit): ?>
                                    <div class="input-group">
                                        <div class="input-group-prepend">
                                            <span class="input-group-text bold">%</span>
                                        </div>
                                        <?php $reqPrecent = $drawRequest->status === DrawRequest::STATUS_REJECTED ? round($lineItem['requestedAmount'] / $lineItem['cost'] * 100) : 0; ?>
                                        <input type="number"
                                                name="requestedPercent"
                                                class="form-control requested-percent"
                                                min="0"
                                                step="100"
                                                value="<?= $reqPrecent ?>"
                                                data-line-item-id="<?= $lineItem['id'] ?>"
                                                data-cost="<?= $lineItem['cost'] ?>"
                                                data-completed-amount="<?= $lineItem['completedAmount'] ?>"
                                                data-completed-percent="<?= round($lineItem['completedPercent'], 2) ?>"
                                                placeholder="0"
                                                style="max-width: 100px;"/>
                                        <div class="input-group-prepend">
                                            <span class="input-group-text bold">$</span>
                                        </div>
                                        <?php $reqAmount = $drawRequest->status === DrawRequest::STATUS_REJECTED ? $lineItem['requestedAmount'] : 0; ?>
                                        <input type="number"
                                                name="requestedAmount"
                                                class="form-control requested-amount"
                                                min="0"
                                                step="100"
                                                value="<?= $reqAmount ?>"
                                                data-line-item-id="<?= $lineItem['id'] ?>"
                                                data-cost="<?= $lineItem['cost'] ?>"
                                                data-completed-amount="<?= $lineItem['completedAmount'] ?>"
                                                data-completed-percent="<?= round($lineItem['completedPercent'], 2) ?>"
                                                placeholder="0.00"/>
                                    </div>
                                    <div class="validation-message text-danger small mt-1" style="display: none;"></div>
                                    <?php else: ?>
                                        <?= round($lineItem['requestedAmount'] / $lineItem['cost'] * 100) ?>% <span class= "text-muted">|</span> $<?= number_format($lineItem['requestedAmount'], 2) ?>
                                    <?php endif; ?>
                                </td>
                                <td class="text-center">
                                    <div class="popup-container">
                                        <button type="button" class="btn p-0 note-btn"
                                            data-toggle="modal"
                                            data-target="#noteModal"
                                            data-note="<?= htmlspecialchars($lineItem['notes']) ?>"
                                            data-line-item-id="<?= $lineItem['id'] ?>">
                                            <i class="icon-md fas fa-comment-medical fa-lg"></i>
                                        </button>
                                        <div class="popover"><?= htmlspecialchars($lineItem['notes']) ?></div>
                                    </div>
                                </td>
                                <td class="text-center">
                                    <div class="popup-container">
                                        <?php $lenderNotes = htmlspecialchars($lineItem['lenderNotes']);
                                            if($lineItem['rejectReason']) {
                                                $lenderNotes .= "\n\nRejection Reason:\n" . htmlspecialchars($lineItem['rejectReason']);
                                            }
                                            $lenderNotes = trim($lenderNotes);
                                        ?>
                                        <button type="button" class="btn p-0 lender-notes">
                                            <i class="icon-md fas fa-comment-medical fa-lg <?= !empty($lenderNotes) ? 'text-primary' : 'text-muted' ?>"></i>
                                        </button>
                                        <div class="popover"><?= $lenderNotes ?></div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <div class="alert alert-info" role="alert">
            <i class="fas fa-info-circle"></i>
            No draw request data available.
        </div>
    <?php endif; ?>

    <?php if($allowEdit): ?>
        <div class="d-flex justify-content-center btn-sm action-buttons">
            <button type="submit" name="btnSave" id="btnSave" class="btn btn-primary">Submit</button>
        </div>
    <?php endif; ?>
</div>

<!-- Notes Modal -->
<div class="modal fade" id="noteModal" tabindex="-1" role="dialog" aria-labelledby="noteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="noteModalLabel">Edit Notes</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <textarea id="noteTextarea" class="form-control" rows="4" placeholder="Enter notes…"></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-sm" data-dismiss="modal">Cancel</button>
                <button type="button" id="saveNoteBtn" class="btn btn-primary btn-sm">Save</button>
            </div>
        </div>
    </div>
</div>
