$(document).ready(function() {
    let $currentNoteBtn;
    let $saveBtn = $('#btnSave');

    $('#noteModal').on('show.bs.modal', function (event) {
        $currentNoteBtn = $(event.relatedTarget);
        const noteText = $currentNoteBtn.data('note') || '';
        $('#noteTextarea').val(noteText);
    });

    $('#saveNoteBtn').on('click', function () {
        const updatedNote = $('#noteTextarea').val();

        $currentNoteBtn.data('note', updatedNote);
        $currentNoteBtn.attr('data-note', updatedNote);

        const popover = $currentNoteBtn.siblings('.popover');
        popover.text(updatedNote);

        const icon = $currentNoteBtn.find('i');
        if (updatedNote.trim()) {
            icon.removeClass('text-muted').addClass('text-primary');
        } else {
            icon.removeClass('text-primary').addClass('text-muted');
        }

        $('#noteModal').modal('hide');

        if (typeof DrawManagement !== 'undefined') {
            DrawManagement.config.lineItemsModified = true;
            DrawManagement.elements.$saveLineItemsBtn.prop('disabled', false);
        }
    });

    $('.note-btn').each(function() {
        const noteText = $(this).data('note') || '';
        const icon = $(this).find('i');
        if (noteText.trim()) {
            icon.addClass('text-primary');
        } else {
            icon.addClass('text-muted');
        }
    });

    function validateSubmitButton() {
        const hasNonZeroValue = DrawRequestUtils.hasNonZeroValues();
        const hasValidationErrors = DrawRequestUtils.hasValidationErrors();
        $saveBtn.prop('disabled', !hasNonZeroValue || hasValidationErrors);
    }

    DrawRequestUtils.setupInputHandlers(validateSubmitButton);

    validateSubmitButton();

    $('#btnSave').on('click', function(e) {
        e.preventDefault();

        if (!DrawRequestUtils.validateAllInputs()) {
            toastrNotification('Please fix validation errors before submitting.', 'error');
            return;
        }

        const lineItemsData = {};
        const lmrid = $('#lmrid').val();
        const status = 'pending';

        $('.line-item').each(function() {
            const $row = $(this);
            const $amountInput = $row.find('.requested-amount');
            const $noteBtn = $row.find('.note-btn');

            if ($amountInput.length > 0) {
                const lineItemId = $amountInput.data('line-item-id');
                const requestedAmount = parseFloat($amountInput.val()) || 0;
                const notes = $noteBtn.data('note') || '';

                const lineItemData = {
                    id: lineItemId,
                    requestedAmount: requestedAmount,
                    notes: notes
                };

                lineItemsData[lineItemId] = DataMapper.mapObject(lineItemData, 'lineItem', 'toBackend');
            }
        });

        const drawRequestData = {
            lmrid: lmrid,
            status: status,
            lineItems: lineItemsData
        };

        const validator = new Validator();
        const validationContext = {
            userType: 'borrower',
            isDraft: false,
            isDrawRequest: true
        };

        if (!validator.validateForm(drawRequestData, 'lineItems', validationContext)) {
            const errors = validator.getErrors();
            toastrNotification('Validation Error: ' + errors.join(', '), 'error');
            return;
        }

        const sanitizedData = DataMapper.sanitizeObject(drawRequestData);

        $saveBtn.prop('disabled', true).text('Submitting...');

        drawManagementApi.saveDrawRequest(sanitizedData)
            .then(function(response) {
                if (response.success) {
                    toastrNotification('Draw request submitted successfully!', 'success');
                    setTimeout(function() {
                        window.location.reload();
                    }, 1500);
                } else {
                    toastrNotification(response.message || 'Failed to submit draw request.', 'error');
                }
            })
            .catch(function(error) {
                console.error('API Error:', error);
                let errorMsg = error.message || 'An error occurred while submitting the draw request.';
                toastrNotification(errorMsg, 'error');
            })
            .always(function() {
                $saveBtn.prop('disabled', false).text('Submit');
            });
    });
});
