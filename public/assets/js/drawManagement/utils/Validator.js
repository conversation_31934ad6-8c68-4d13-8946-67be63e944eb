class Validator {
    constructor() {
        this.errors = [];
    }

    validateCategory(category, context = {}) {
        this.clearErrors();

        // Required fields
        if (!category.categoryName || category.categoryName.trim() === '') {
            this.addError('Category name is required');
        }

        // Category name length
        if (category.categoryName && category.categoryName.length > 255) {
            this.addError('Category name must not exceed 255 characters');
        }

        // Description length
        if (category.description && category.description.length > 1000) {
            this.addError('Category description must not exceed 1000 characters');
        }

        // Order validation
        if (!category.order || category.order <= 0) {
            this.addError('Category order must be a positive number');
        }

        // Max categories check
        if (context.maxCategories && category.order > context.maxCategories) {
            this.addError(`Category order cannot exceed ${context.maxCategories}`);
        }

        // Validate line items if present
        if (category.lineItems && Array.isArray(category.lineItems)) {
            category.lineItems.forEach((lineItem, index) => {
                if (!this.validateLineItem(lineItem, context)) {
                    this.addError(`Line item ${index + 1}: ${this.getFirstError()}`);
                }
            });
        }

        return this.isValid();
    }

    validateLineItem(lineItem, context = {}) {
        this.clearErrors();
        // Required fields
        if (!lineItem.name || lineItem.name.trim() === '') {
            this.addError('Line item name is required');
        }

        // Name length
        if (lineItem.name && lineItem.name.length > 255) {
            this.addError('Line item name must not exceed 255 characters');
        }

        // Description length
        if (lineItem.description && lineItem.description.length > 1000) {
            this.addError('Line item description must not exceed 1000 characters');
        }

        // Order validation
        if (!lineItem.order || lineItem.order <= 0) {
            this.addError('Line item order must be a positive number');
        }

        //Validate Borrower Side Fields
        if (context.userType === 'borrower') {
            // Cost validation
            if (!context.isDraft && (!lineItem.cost || lineItem.cost <= 0)) {
                this.addError('Line item cost must be a positive number');
            }

            // Completed amount validation
            if (lineItem.completedAmount < 0) {
                this.addError('Completed amount cannot be negative');
            }

            if (lineItem.completedAmount > lineItem.cost) {
                this.addError('Completed amount cannot exceed total cost');
            }

            // Completed percent validation
            if (lineItem.completedPercent < 0 || lineItem.completedPercent > 100) {
                this.addError('Completed percent must be between 0 and 100');
            }

            // Requested amount validation
            if (lineItem.requestedAmount < 0) {
                this.addError('Requested amount cannot be negative');
            }

            const maxRequestable = lineItem.cost - lineItem.completedAmount;
            if (lineItem.requestedAmount > maxRequestable) {
                this.addError('Requested amount cannot exceed remaining cost');
            }

            // Requested percent validation
            if (lineItem.requestedPercent < 0) {
                this.addError('Requested percent cannot be negative');
            }

            const maxRequestablePercent = 100 - lineItem.completedPercent;
            if (lineItem.requestedPercent > maxRequestablePercent) {
                this.addError('Requested percent cannot exceed remaining percent');
            }

            // Consistency check between amount and percent
            if (lineItem.cost > 0) {
                const calculatedAmount = (lineItem.requestedPercent / 100) * lineItem.cost;
                const tolerance = 0.01;

                if (Math.abs(lineItem.requestedAmount - calculatedAmount) > tolerance) {
                    this.addError('Requested amount and percent are inconsistent');
                }
            }

            // Notes length validation
            if (lineItem.notes && lineItem.notes.length > 2000) {
                this.addError('Notes must not exceed 2000 characters');
            }

            if (lineItem.lenderNotes && lineItem.lenderNotes.length > 2000) {
                this.addError('Lender notes must not exceed 2000 characters');
            }

        }

        return this.isValid();
    }

    validateDrawRequest(drawRequest, context = {}) {
        this.clearErrors();

        // Required fields
        if (!drawRequest.lmrid) {
            this.addError('Loan file ID is required');
        }

        if (!drawRequest.status) {
            this.addError('Status is required');
        }

        // Status validation
        const validStatuses = ['new', 'pending', 'approved', 'rejected'];
        if (drawRequest.status && !validStatuses.includes(drawRequest.status)) {
            this.addError('Invalid status value');
        }

        // Amount validation
        if (drawRequest.amountRequested < 0) {
            this.addError('Requested amount cannot be negative');
        }

        if (drawRequest.amountApproved && drawRequest.amountApproved < 0) {
            this.addError('Approved amount cannot be negative');
        }

        if (drawRequest.amountApproved > drawRequest.amountRequested) {
            this.addError('Approved amount cannot exceed requested amount');
        }

        // Lender notes length
        if (drawRequest.lenderNotes && drawRequest.lenderNotes.length > 2000) {
            this.addError('Lender notes must not exceed 2000 characters');
        }

        // Validate each category
        if (drawRequest.categories) {
            drawRequest.categories.forEach((category, index) => {
                if (!this.validateCategory(category, context)) {
                    this.addError(`Category ${index + 1}: ${this.getFirstError()}`);
                }
            });
        }

        return this.isValid();
    }

    validateForm(formData, formType, context = {}) {
        this.clearErrors();

        switch (formType) {
            case 'categories':
                return this.validateCategoriesForm(formData, context);
            case 'lineItems':
                return this.validateLineItemsForm(formData, context);
            case 'drawRequest':
                return this.validateDrawRequest(formData, context);
            default:
                this.addError('Unknown form type');
                return false;
        }
    }

    validateCategoriesForm(formData, context) {
        if (!formData.categories || !Array.isArray(formData.categories)) {
            this.addError('Categories data is required');
            return false;
        }

        // Check for duplicate orders
        const orders = formData.categories.map(cat => cat.order);
        const duplicateOrders = orders.filter((order, index) => orders.indexOf(order) !== index);
        if (duplicateOrders.length > 0) {
            this.addError('Duplicate category orders found');
        }

        formData.categories.forEach((category, index) => {
            if (!this.validateCategory(category, context)) {
                this.addError(`Category ${index + 1}: ${this.getFirstError()}`);
            }
        });

        return this.isValid();
    }

    validateLineItemsForm(formData, context) {
        if (!formData.lineItems || typeof formData.lineItems !== 'object') {
            this.addError('Line items data is required');
            return false;
        }

        let hasRequestedAmounts = true;
        let isDrawRequest = context.isDrawRequest !== undefined ? context.isDrawRequest : false;

        // Validate each category's line items
        Object.keys(formData.lineItems).forEach(categoryId => {
            let categoryLineItems = formData.lineItems;

            if(isDrawRequest) {
                hasRequestedAmounts = this.validateDrawRequestLineItemsData(categoryLineItems);
            } else {
                this.validateSoWLineItemsData(categoryLineItems, context);
            }
        });

        // Require at least one requested amount
        if (!context.isDraft && !hasRequestedAmounts) {
            this.addError('At least one line item must have a requested amount');
        }

        return this.isValid();
    }

    validateSoWLineItemsData(lineItemsData, context) {
        Object.keys(lineItemsData).forEach(categoryId => {
            let categoryLineItems = lineItemsData[categoryId];

            if (Array.isArray(categoryLineItems) && categoryLineItems.length > 0) {
                categoryLineItems.forEach((lineItem, index) => {
                    if (!this.validateLineItem(lineItem, context)) {
                        this.addError(`Category ${categoryId}, Line item ${index + 1}: ${this.getFirstError()}`);
                    }
                });
            }
        })
    }

    validateDrawRequestLineItemsData(lineItemsData) {
        Object.keys(lineItemsData).forEach(categoryId => {
            let categoryLineItems = lineItemsData;

            if(typeof categoryLineItems === 'object') {
                categoryLineItems = Object.values(categoryLineItems);
            }

            if (Array.isArray(categoryLineItems) && categoryLineItems.length > 0) {
                categoryLineItems.forEach((lineItem, index) => {
                    if (!lineItem.requestedAmount > 0) {
                        return false;
                    }
                });
            }
        })
        return true;
    }

    addError(error) {
        this.errors.push(error);
    }


    getErrors() {
        return this.errors;
    }

    getFirstError() {
        return this.errors.length > 0 ? this.errors[0] : null;
    }

    isValid() {
        return this.errors.length === 0;
    }

    clearErrors() {
        this.errors = [];
    }

    displayErrors(containerSelector = '.validation-errors') {
        const $container = $(containerSelector);

        if (this.errors.length === 0) {
            $container.hide();
            return;
        }

        const errorHtml = this.errors.map(error => `<li>${error}</li>`).join('');
        $container.html(`<ul class="list-unstyled mb-0">${errorHtml}</ul>`).show();
    }
}

window.Validator = Validator;
