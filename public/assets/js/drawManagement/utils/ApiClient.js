class ApiClient {
    constructor(baseUrl = '/backoffice/api_v2/draw_management') {
        this.baseUrl = baseUrl;
        this.defaultHeaders = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };
    }

    request(method, endpoint, data = null, options = {}) {
        const config = {
            url: `${this.baseUrl}/${endpoint}`,
            type: method.toUpperCase(),
            headers: { ...this.defaultHeaders, ...options.headers },
            dataType: 'json',
            ...options
        };

        if (data && ['POST', 'PUT', 'PATCH'].includes(config.type)) {
            config.data = JSON.stringify(data);
            config.contentType = 'application/json';
        } else if (data && config.type === 'GET') {
            config.data = data;
        }

        return $.ajax(config)
            .then(response => this.handleResponse(response))
            .catch(error => this.handleError(error));
    }

    handleResponse(response) {
        if (response && typeof response === 'object') {
            // Standardize response format
            return {
                success: response.success !== false,
                data: response.data || response,
                message: response.message || '',
                errors: response.errors || []
            };
        }

        return {
            success: true,
            data: response,
            message: '',
            errors: []
        };
    }

    handleError(error) {
        let errorResponse = {
            success: false,
            data: null,
            message: 'An error occurred',
            errors: []
        };

        if (error.responseJSON) {
            errorResponse.message = error.responseJSON.message || errorResponse.message;
            errorResponse.errors = error.responseJSON.errors || [];
            errorResponse.data = error.responseJSON.data || null;
        } else if (error.responseText) {
            try {
                const parsed = JSON.parse(error.responseText);
                errorResponse.message = parsed.message || errorResponse.message;
                errorResponse.errors = parsed.errors || [];
            } catch (e) {
                errorResponse.message = error.responseText;
            }
        } else if (error.statusText) {
            errorResponse.message = error.statusText;
        }

        // Add HTTP status information
        if (error.status) {
            errorResponse.status = error.status;
            errorResponse.statusText = error.statusText;
        }

        return Promise.reject(errorResponse);
    }

    get(endpoint, params = {}, options = {}) {
        return this.request('GET', endpoint, params, options);
    }

    post(endpoint, data = {}, options = {}) {
        return this.request('POST', endpoint, data, options);
    }

    put(endpoint, data = {}, options = {}) {
        return this.request('PUT', endpoint, data, options);
    }

    delete(endpoint, options = {}) {
        return this.request('DELETE', endpoint, null, options);
    }

    saveCategories(categoriesData, userType) {
        const endpoint = `${userType}/SowCategories`;
        return this.post(endpoint, categoriesData);
    }

    getCategories(params, userType) {
        const endpoint = `${userType}/SowCategories`;
        return this.get(endpoint, params);
    }

    saveLineItems(lineItemsData, userType) {
        const endpoint = `${userType}/SowLineItems`;
        return this.post(endpoint, lineItemsData);
    }

    getLineItems(params, userType) {
        const endpoint = `${userType}/SowLineItems`;
        return this.get(endpoint, params);
    }

    saveDrawRequest(drawRequestData) {
        return this.post('LoanFile', drawRequestData);
    }

    getDrawRequest(params) {
        return this.get('LoanFile', params);
    }

    exportToPdf(exportData) {
        return this.post('ExportPdf', exportData);
    }

    batch(requests) {
        const promises = requests.map(req => {
            const { method, endpoint, data, options } = req;
            return this.request(method, endpoint, data, options)
                .then(response => ({ success: true, response, request: req }))
                .catch(error => ({ success: false, error, request: req }));
        });

        return Promise.all(promises);
    }

    setAuthToken(token) {
        this.defaultHeaders['Authorization'] = `Bearer ${token}`;
    }

    setHeader(name, value) {
        this.defaultHeaders[name] = value;
    }

    removeHeader(name) {
        delete this.defaultHeaders[name];
    }

    forUserType(userType) {
        return {
            saveCategories: (data) => this.saveCategories(data, userType),
            getCategories: (params) => this.getCategories(params, userType),
            saveLineItems: (data) => this.saveLineItems(data, userType),
            getLineItems: (params) => this.getLineItems(params, userType)
        };
    }
}

const drawManagementApi = new ApiClient();

window.ApiClient = ApiClient;
window.drawManagementApi = drawManagementApi;
