class DataMapper {
    static propertyMaps = {
        category: {
            frontend: ['id', 'categoryName', 'description', 'order', 'lineItems'],
            backend: ['id', 'categoryName', 'description', 'order', 'lineItems'],
            mapping: {} // Direct mapping for categories
        },
        lineItem: {
            frontend: ['id', 'categoryId', 'name', 'description', 'order', 'cost', 'completedAmount',
                      'completedPercent', 'requestedAmount', 'requestedPercent', 'notes', 'lenderNotes'],
            backend: ['id', 'categoryId', 'name', 'description', 'order', 'cost', 'completedAmount',
                     'completedPercent', 'requestedAmount', 'requestedPercent', 'notes', 'lenderNotes'],
            mapping: {}
        },
        drawRequest: {
            frontend: ['id', 'LMRId', 'status', 'categories', 'amountRequested', 'lenderNotes'],
            backend: ['id', 'LMRId', 'status', 'categories', 'amountRequested', 'lenderNotes'],
            mapping: {}
        }
    };

    static mapObject(sourceObject, objectType, direction = 'toBackend') {
        if (!sourceObject || typeof sourceObject !== 'object') {
            return {};
        }

        const config = this.propertyMaps[objectType];
        if (!config) {
            console.warn(`Unknown object type: ${objectType}`);
            return sourceObject;
        }

        const mappedObject = {};
        const sourceProperties = direction === 'toBackend' ? config.frontend : config.backend;
        const mapping = config.mapping;

        sourceProperties.forEach(property => {
            const mappedProperty = mapping[property] || property;

            if (sourceObject.hasOwnProperty(property)) {
                let value = sourceObject[property];

                if (property === 'lineItems' && Array.isArray(value)) {
                    value = value.map(item => this.mapObject(item, 'lineItem', direction));
                } else if (property === 'categories' && Array.isArray(value)) {
                    value = value.map(item => this.mapObject(item, 'category', direction));
                }

                value = this.convertValue(value, property, direction);

                mappedObject[mappedProperty] = value;
            }
        });

        return mappedObject;
    }

    static convertValue(value, property, direction) {
        if (value === null || value === undefined) {
            return null;
        }

        // Handle numeric properties
        if (['id', 'order', 'LMRId'].includes(property)) {
            return value === '' ? null : parseInt(value, 10);
        }

        if (['cost', 'completedAmount', 'completedPercent', 'requestedAmount',
             'requestedPercent', 'amountRequested'].includes(property)) {
            return value === '' ? 0 : parseFloat(value);
        }

        if (['categoryName', 'description', 'name', 'notes', 'lenderNotes', 'status'].includes(property)) {
            return String(value);
        }

        return value;
    }

    static extractCategoriesFromDOM($container) {
        const categories = [];

        $container.find('.category-item').each(function(index) {
            const $this = $(this);
            let id = $this.data('category-id');

            // Handle new category IDs
            if (typeof id === 'string' && id.startsWith('new_cat_')) {
                id = null;
            } else {
                id = parseInt(id, 10);
            }

            const category = {
                id: id,
                categoryName: $this.find('.category-name').text().trim(),
                description: $this.find('.category-description').text().trim(),
                order: index + 1,
                lineItems: []
            };

            categories.push(category);
        });

        return categories;
    }

    static extractLineItemsFromDOM($container) {
        const groupedLineItems = {};

        $container.find('.line-item-category-section').each(function() {
            const $categoryCard = $(this);
            const categoryId = $categoryCard.data('category-id');

            if (!categoryId) {
                return;
            }

            groupedLineItems[categoryId] = [];

            $categoryCard.find('.editable-line-item').each(function() {
                const $row = $(this);
                const lineItemId = $row.data('line-item-id');

                if (!lineItemId) {
                    return;
                }

                const lineItem = {
                    id: parseInt(lineItemId, 10),
                    categoryId: parseInt(categoryId, 10),
                    name: $row.find('.line-item-name-display').text().trim(),
                    cost: parseFloat($row.find('.line-item-cost-input').val() || 0),
                    completedAmount: parseFloat($row.find('.line-item-completed-amount-input').val() || 0),
                    completedPercent: parseFloat($row.find('.line-item-completed-percentage-input').val() || 0),
                    requestedAmount: parseFloat($row.find('.line-item-requested-amount').val() || 0),
                    notes: $row.find('.note-btn').data('note') || '',
                    order: $row.index() + 1
                };

                if($row.find('.line-item-description-display').length){
                    lineItem.description = $row.find('.line-item-description-display').text().trim();
                } else if ($row.find('.description-btn').length) {
                    lineItem.description = $row.find('.description-btn').data('note') || '';
                }

                // Calculate requested percent from amount
                if (lineItem.cost > 0 && lineItem.requestedAmount > 0) {
                    lineItem.requestedPercent = (lineItem.requestedAmount / lineItem.cost) * 100;
                } else {
                    lineItem.requestedPercent = 0;
                }

                groupedLineItems[categoryId].push(lineItem);
            });
        });

        return groupedLineItems;
    }

    static buildCategoriesRequest($container, config) {
        const categories = this.extractCategoriesFromDOM($container);
        const mappedCategories = categories.map(cat => this.mapObject(cat, 'category', 'toBackend'));

        const request = {
            categories: mappedCategories
        };

        // Add identifier based on config
        if (config.dataKey && config[config.dataKey]) {
            request[config.dataKey] = config[config.dataKey];
        }

        return request;
    }

    static buildLineItemsRequest($container, config) {
        const lineItems = this.extractLineItemsFromDOM($container);
        const mappedLineItems = {};

        // Map each category's line items
        Object.keys(lineItems).forEach(categoryId => {
            mappedLineItems[categoryId] = lineItems[categoryId].map(
                item => this.mapObject(item, 'lineItem', 'toBackend')
            );
        });

        const request = {
            lineItems: mappedLineItems,
            isDraft: config.isDraft || false
        };

        // Add identifier based on config
        if (config.dataKey && config[config.dataKey]) {
            request[config.dataKey] = config[config.dataKey];
        }

        return request;
    }

    static buildDrawRequestRequest(formData, config) {
        const drawRequest = this.mapObject(formData, 'drawRequest', 'toBackend');

        // Add identifier
        if (config.lmrid) {
            drawRequest.lmrid = config.lmrid;
        }

        return drawRequest;
    }

    static sanitizeObject(object) {
        if (!object || typeof object !== 'object') {
            return object;
        }

        const sanitized = {};

        Object.keys(object).forEach(key => {
            let value = object[key];

            if (Array.isArray(value)) {
                value = value.map(item => this.sanitizeObject(item));
            } else if (typeof value === 'object' && value !== null) {
                value = this.sanitizeObject(value);
            } else if (typeof value === 'string') {
                // Basic XSS protection
                value = value.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
            }

            sanitized[key] = value;
        });

        return sanitized;
    }
}

window.DataMapper = DataMapper;
