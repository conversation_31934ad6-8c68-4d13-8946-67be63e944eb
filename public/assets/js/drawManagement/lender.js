DrawManagement.lender = {
    init: function() {
        DrawManagement.config.pcid = $('#pcid').val();
        DrawManagement.config.dataKey = 'pcid';
        DrawManagement.config.dataUrl = `/backoffice/api_v2/draw_management/lender/SowCategories?pcid=${DrawManagement.config.pcid}`;
        DrawManagement.config.saveLineItemsSuccessMessage = 'Line items saved successfully!';
    },

    renderLineItemRowsUI: function(lineItems, $tbodyElement) {
        const rowTemplate = document.getElementById('line-item-row-template');
        if (!rowTemplate) {
            console.error("line-item-row-template not found!");
            return;
        }

        if (lineItems && lineItems.length > 0) {
            lineItems.forEach(item => {
                const clone = rowTemplate.content.cloneNode(true);
                const $row = $(clone).find('tr');

                $row.attr('data-line-item-id', item.id);
                $row.find('.line-item-name-display').text(item.name);
                $row.find('.line-item-name-input').val(item.name);
                $row.find('.line-item-name-display').attr('title', item.description);
                $row.find('.line-item-description-input').val(item.description);
                $row.find('.line-item-description-display').text(item.description);

                $tbodyElement.append($row);
            });
        }
    },

    initTemplateSettings: function() {
        const settings = DrawManagement.currentTemplateData;
        $('#allowBorrowersAddEditCategoriesTog').prop('checked', settings.allowBorrowersAddEditCategories === 1);
        $('#allowBorrowersDeleteCategoriesTog').prop('checked', settings.allowBorrowersDeleteCategories === 1);
        $('#allowBorrowersAddEditLineItemsTog').prop('checked', settings.allowBorrowersAddEditLineItems === 1);
        $('#allowBorrowersDeleteLineItemsTog').prop('checked', settings.allowBorrowersDeleteLineItems === 1);
        $('#allowBorrowersSOWRevisionsTog').prop('checked', settings.allowBorrowersSOWRevisions === 1);
        $('#allowBorrowersExceedFinancedRehabCostOnRevisionTog').prop('checked', settings.allowBorrowersExceedFinancedRehabCostOnRevision === 1);
        $('#allowBorrowersAddEditCategories').val(settings.allowBorrowersAddEditCategories);
        $('#allowBorrowersDeleteCategories').val(settings.allowBorrowersDeleteCategories);
        $('#allowBorrowersAddEditLineItems').val(settings.allowBorrowersAddEditLineItems);
        $('#allowBorrowersDeleteLineItems').val(settings.allowBorrowersDeleteLineItems);
        $('#allowBorrowersSOWRevisions').val(settings.allowBorrowersSOWRevisions);
        $('#allowBorrowersExceedFinancedRehabCostOnRevision').val(settings.allowBorrowersExceedFinancedRehabCostOnRevision);
        $('#drawFee').val(settings.drawFee);
    }
};



