.category-details {
    flex-grow: 1;
    flex-shrink: 1;
    min-width: 0;
    overflow: hidden;
    margin-right: 8px;
}

.category-details h5 {
    margin-bottom: 0.25rem;
    font-size: 1rem;
    font-weight: 600;
}
.category-details p {
    margin-bottom: 0;
    font-size: 0.875rem;
}
.actions a {
    margin-left: 0.75rem;
}
.actions a:hover {
    color: #007bff;
}

.categories-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
}

.category-item {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
    background-color: #fff;
    display: flex;
    justify-content: space-between;
    overflow: hidden;
    align-items: flex-start;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
              0 2px 4px -2px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease-in-out;
}

.category-item:hover, .editable-line-item:hover  {
    background-color: #f3f5f7;
    cursor: move;
}

.category-item:hover {
  box-shadow: 0 8px 10px -2px rgba(0, 0, 0, 0.12), /* Increased shadow on hover */
              0 3px 6px -3px rgba(0, 0, 0, 0.12);
}

.category-item .actions {
    display: flex;
    flex-shrink: 0;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s ease-in-out, visibility 0s 0.2s;
}

.category-item:hover .actions {
  opacity: 1;
  visibility: visible;
  transition-delay: 0s;
}

.category-item .actions a {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2em;
    padding: 5px;
}

.category-details .category-name {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 4px;
}

.category-details .category-description {
  display: -webkit-box;
  line-clamp: 1;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-word;
}

.category-name, .line-item-name-display {
    text-transform: capitalize;
}

.line-item-table {
    table-layout: fixed;
    width: 100%;
    border-collapse: collapse;
}

i.fas:hover {
    color: #181c32;
}
i.fa-trash-alt:hover {
    color: #dc3545;
}

@media (max-width: 768px) {
    .categories-container {
        grid-template-columns: 1fr;
    }
}

/* Stepper Styling */
.stepper-container {
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: relative;
}

.stepper-container::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    border-top: 1px solid #ccc;
    z-index: 0;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    cursor: pointer;
    z-index: 1;
    background: white;
    padding: 0 10px;
}

.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #ccc;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: bold;
    margin-bottom: 5px;
}

.step-label {
    font-size: 0.9em;
    color: #555;
}

.step.active .step-icon {
    background-color: #3699ff;
}

.step.active .step-label {
    font-weight: bold;
    color: #000;
}

.sortable-ghost {
    opacity: 0.4;
    background-color: #e9ecef;
    border: 2px dashed #adb5bd;
}

.add-line-item-row {
    background-color: #fffbe6;
    cursor: pointer;
}

.add-line-item-row:hover {
    background-color: #fffacd;
}

.add-line-item-row td {
    font-style: italic;
}

.line-item-category-header{
    cursor: pointer;
}

.line-item-category-header[aria-expanded="true"] .collapse-icon {
    transform: rotate(180deg);
    transition: transform 0.3s ease;
}
.line-item-category-header[aria-expanded="false"] .collapse-icon {
    transform: rotate(0deg);
    transition: transform 0.3s ease;
}

.line-item-display {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.line-item-display::after {
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    content: "\f303";
    margin-left: 10px;
    transform: translateY(-50%);
    color: #b5b5c3;
    pointer-events: none;
}

.line-item-display:not(:empty)::after {
    display: none;
}

.line-item-display:not(:empty):hover::after {
    display: inline;
}

.invalid-feedback {
    display: none;
    width: 100%;
    margin-top: .25rem;
    font-size: .875em;
}

.form-control.is-invalid ~ .invalid-feedback {
    display: block;
}

.card.card-custom>.card-header .card-title {
    margin: 0;
}

.popover {
    max-width: 300px;
    background-color: #f8f9fa;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 0.3rem;
    box-shadow: 0 0.25rem 0.5rem rgba(0,0,0,.1);
}

.popover .arrow::before {
    border-top-color: rgba(0,0,0,.2);
}

.popover .popover-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,.1);
    font-size: 0.875rem;
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    margin: 0;
}

.popover .popover-body {
    padding: 0.5rem 0.75rem;
    color: #212529;
    font-size: 0.875rem;
}

